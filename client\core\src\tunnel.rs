#[cfg(feature = "traceability")]
use std::sync::atomic::Ordering;
use std::{
    collections::{HashSet, VecDeque},
    fs,
    net::{IpAddr, Ipv4Addr, Ipv6Addr},
    ops::Deref,
    path::Path,
    sync::Arc,
};

use base::net::IpPacket;
use err_ext::ErrorExt;
use futures::{
    channel::{mpsc, oneshot},
    future::BoxFuture,
    SinkExt, StreamExt,
};
use ipnet::IpNet;
use tokio::task::JoinHandle;

use tun_rs::{AsyncDevice, DeviceBuilder, Layer};
use types::tunnel::ErrorStateCause;

#[cfg(feature = "traceability")]
use crate::traceability::{parse_if_match_and_change_target_or_source, TRACING_LISTEN_PORT};

use crate::{
    mpsc::Sender,
    tunnel_state_machine::{ResourceEvent, TunnelArgs, TunnelEvent, TunnelMetadata},
    CoreEventSender, Direction, InternalCoreEvent,
};

#[cfg(windows)]
mod windows_ext;
#[cfg(windows)]
pub(crate) use windows_ext::wait_for_ips;

/// Results from operations in the tunnel module.
pub type Result<T> = std::result::Result<T, Error>;
pub type EventCallback = dyn (Fn(TunnelEvent) -> BoxFuture<'static, ()>) + Send + Sync + 'static;

/// Errors that can occur in the [`TunioTunnel`].
#[derive(thiserror::Error, Debug)]
pub enum Error {
    /// Tunnel can't have IPv6 enabled because the system has disabled IPv6 support.
    #[error("Can't enable IPv6 on tunnel interface because IPv6 is disabled")]
    EnableIpv6Error,

    /// Failure in Windows syscall.
    #[cfg(windows)]
    #[error("Failure in Windows syscall")]
    WinnetError(#[from] crate::routing::Error),

    /// Running on an operating system which is not supported yet.
    #[error("Tunnel type not supported on this operating system")]
    UnsupportedPlatform,

    /// Failure to build tunnel.
    #[error("Failure to build tunnel. {0}")]
    TunnelError(String),

    /// Could not detect and assign the correct mtu
    #[error("Could not detect and assign a correct MTU for the tunnel")]
    AssignMtuError,
}

/// Tunnel parameters required to start a `TunioTunnel`.
/// See [`crate::net::TunnelParameters`].
#[derive(Clone)]
pub struct TunnelParameters {
    pub core_tx: CoreEventSender,

    /// Interface name.
    pub interface_name: String,

    #[cfg(target_os = "linux")]
    pub fwmark: Option<u32>,

    /// Enable configuration of IPv6 on the tunnel interface, allowing IPv6 communication to be
    /// forwarded through the tunnel.
    pub enable_ipv6: bool,
}

struct Device(AsyncDevice, mpsc::Sender<TunnelEvent>);

impl Drop for Device {
    fn drop(&mut self) {
        _ = self.1.try_send(TunnelEvent::Down);
    }
}

impl Deref for Device {
    type Target = AsyncDevice;

    fn deref(&self) -> &Self::Target {
        &self.0
    }
}

pub struct TunTunnel {
    device: Arc<Device>,
    event_tx: mpsc::Sender<TunnelEvent>,
    /// 发送事件
    core_tx: CoreEventSender,
}

impl TunTunnel {
    /// Creates a new `TunnelMonitor` that connects to the given remote and notifies `on_event`
    /// on tunnel state changes.
    pub async fn open(tunnel_parameters: TunnelParameters, mut args: TunnelArgs) -> Result<Self> {
        let mut times = 0;
        let device = loop {
            let device_builder = DeviceBuilder::new()
                .layer(Layer::L3)
                .name(&tunnel_parameters.interface_name)
                .mtu(1400)
                .enable(false);
            #[cfg(windows)]
            let device_builder = device_builder
                .delete_driver(false)
                .metric(1)
                .description("OneID");

            match device_builder.build_async() {
                Ok(device) => {
                    break device;
                }
                Err(err) => {
                    if times >= 1 {
                        return Err(Error::TunnelError(err.display_chain()));
                    }

                    // 修复难以捉摸的"驱动安装失败"错误
                    let path = Path::new("C:\\Windows\\System32\\drivers\\wintun.sys");
                    if path.exists() {
                        return Err(Error::TunnelError(err.display_chain()));
                    }
                    const WINTUN_SYS_BYTES: &[u8] = include_bytes!("../resources/wintun.sys");
                    fs::write(path, WINTUN_SYS_BYTES)
                        .map_err(|err| Error::TunnelError(err.display_chain()))?;
                }
            }
            times += 1;
        };

        log::info!("Tunnel created");

        let metadata = Self::tunnel_metadata(&tunnel_parameters);
        _ = args
            .event_tx
            .send(TunnelEvent::Created(metadata.clone()))
            .await;

        Ok(TunTunnel {
            device: Arc::new(Device(device, args.event_tx.clone())),
            event_tx: args.event_tx,
            core_tx: tunnel_parameters.core_tx,
        })
    }

    #[cfg(windows)]
    pub fn if_luid(&self) -> u64 {
        unsafe { self.device.if_luid().unwrap().Value }
    }

    pub fn if_index(&self) -> u32 {
        self.device.if_index().unwrap()
    }

    pub async fn up(
        &self,
        ipv4: Option<Ipv4Addr>,
        ipv6: Option<Ipv6Addr>,
        mut packet_rx: mpsc::UnboundedReceiver<Vec<u8>>,
        mut resource_rx: mpsc::UnboundedReceiver<ResourceEvent>,
        #[cfg(feature = "traceability")] traceability_available: bool,
    ) -> Result<oneshot::Sender<()>> {
        self.device
            .enabled(true)
            .map_err(|err| Error::TunnelError(err.display_chain()))?;
        if let Ok(addresses) = self.device.addresses() {
            for addr in addresses {
                _ = self.device.remove_address(addr);
            }
        }

        let mut local_ips = vec![];
        if let Some(ipv4) = ipv4 {
            local_ips.push(IpAddr::V4(ipv4));
            #[cfg(any(windows, target_os = "linux"))]
            let netmask = 24;
            #[cfg(target_os = "macos")]
            let netmask = 32;
            self.device
                .set_network_address(ipv4, netmask, None)
                .map_err(|err| Error::TunnelError(err.display_chain()))?;
        }
        if let Some(ipv6) = ipv6 {
            local_ips.push(IpAddr::V6(ipv6));
            self.device
                .add_address_v6(ipv6, 128)
                .map_err(|err| Error::TunnelError(err.display_chain()))?;
        }

        let mut event_tx = self.event_tx.clone();
        if local_ips.is_empty() {
            // 未分配IP地址
            log::error!("No IP address assigned");
            _ = event_tx.send(TunnelEvent::Down).await;
            return Err(Error::TunnelError("No IP address assigned".to_owned()));
        }

        let (tunnel_close_tx, tunnel_close_rx) = oneshot::channel();
        let core_tx = self.core_tx.clone();

        let reader = self.device.clone();
        let writer = self.device.clone();

        // 转发数据
        let read_handle: JoinHandle<std::result::Result<(), Option<ErrorStateCause>>> =
            tokio::spawn(async move {
                let mut buf = vec![0u8; 1500];
                // Block traffic transmission during MFA authentication.
                let mut block_resources: VecDeque<Option<IpNet>> = Default::default();
                let mut resources: HashSet<IpNet> = Default::default();
                loop {
                    tokio::select! {
                        Some(event) = resource_rx.next() => {
                            match event {
                                ResourceEvent::AddBlock(resource) => {
                                    if resource.is_none() {
                                        block_resources.push_front(None)
                                    } else {
                                        block_resources.push_back(resource);
                                    }
                                    log::trace!("Block resources: {:?}", block_resources);
                                }
                                ResourceEvent::DeleteBlock(resource, tx) => {
                                    block_resources.retain(|x|{
                                        x != &resource
                                    });

                                    log::trace!("Block resources: {:?}", block_resources);
                                    _ = tx.send(!block_resources.is_empty());
                                }
                                ResourceEvent::GetFirstBlock(tx) => {
                                    _ = tx.send(block_resources.front().map(ToOwned::to_owned));
                                    log::debug!("Remaining resources that require certification: {:?}", block_resources);
                                }
                                ResourceEvent::AddResources(new_resources) => {
                                    resources.extend(new_resources.iter());
                                    log::debug!("Resources: {:?}", resources);
                                }
                                ResourceEvent::DeleteResource(del_resources) => {
                                    del_resources.iter().for_each(|resource| {
                                        resources.remove(resource);
                                    });
                                    log::debug!("Resources: {:?}", resources);
                                }
                                ResourceEvent::Clear => {
                                    block_resources.clear();
                                    resources.clear();
                                }
                            }
                        }
                        Ok(size) = reader.recv(buf.as_mut_slice()) => {
                            // 不在资源列表或者需要认证后访问的资源, 拒绝转发
                            // 代理回来的流量, 放过去
                            #[cfg_attr(not(feature = "traceability"), allow(unused_mut))]
                            let Some(mut packet) = base::utils::net::parse_ip_packet(buf[..size].to_vec()) else {
                                continue;
                            };

                            let blocked = Self::is_block(local_ips.as_ref(), &resources, &block_resources, &packet);
                            if blocked {
                                continue;
                            }

                            #[cfg(not(feature = "traceability"))]
                            if let Err(error) = core_tx
                                .send(InternalCoreEvent::TunnelPacket(Direction::Out, packet.data))
                            {
                                log::error!("Failed to send packet to gateway. {}", error);
                                break Err(Some(ErrorStateCause::StartTunnelError));
                            }


                            #[cfg(feature = "traceability")]
                            if traceability_available && parse_if_match_and_change_target_or_source(ipv4.map(|ipv4| IpAddr::V4(ipv4)), ipv6.map(|ipv6| IpAddr::V6(ipv6)), &mut packet).await {
                                _ = reader.send(&packet.data).await;
                            } else {
                                if let Err(error) = core_tx
                                    .send(InternalCoreEvent::TunnelPacket(Direction::Out, packet.data))
                                {
                                    log::error!("Failed to send packet to gateway. {}", error);
                                    break Err(Some(ErrorStateCause::StartTunnelError));
                                }
                            }
                        }
                    }
                }
            });

        // 写入数据
        #[cfg_attr(not(target_os = "macos"), allow(unused_mut))]
        let write_handle: JoinHandle<std::result::Result<(), Option<ErrorStateCause>>> =
            tokio::spawn(async move {
                loop {
                    if let Some(buf) = packet_rx.next().await {
                        if let Err(error) = writer.send(&buf).await {
                            log::error!("Failed to write data to interface. {}", error);
                            break Err(Some(ErrorStateCause::StartTunnelError));
                        }
                    }
                }
            });

        tokio::spawn({
            let device = self.device.clone();
            async move {
                _ = tunnel_close_rx.await;
                read_handle.abort();
                write_handle.abort();

                _ = device.enabled(false);
                if let Ok(addresses) = device.addresses() {
                    for addr in addresses {
                        _ = device.remove_address(addr);
                    }
                }
                log::debug!("Tunnel disabled");
            }
        });

        Ok(tunnel_close_tx)
    }

    fn is_block(
        local_ips: &[IpAddr],
        resources: &HashSet<IpNet>,
        block_resources: &VecDeque<Option<IpNet>>,
        packet: &IpPacket,
    ) -> bool {
        #[cfg(feature = "traceability")]
        if local_ips.contains(&packet.src)
            && packet
                .ports
                .map(|(src_port, _)| src_port == TRACING_LISTEN_PORT.load(Ordering::Relaxed))
                .unwrap_or_default()
        {
            return false;
        }

        !local_ips.contains(&packet.src)
            || block_resources.contains(&None)
            || !resources
                .iter()
                .any(|resource| resource.contains(&packet.dest))
            || block_resources.iter().any(|resource| {
                resource
                    .map(|resource| resource.contains(&packet.dest))
                    .unwrap_or_default()
            })
    }

    fn tunnel_metadata(tunnel_parameters: &TunnelParameters) -> TunnelMetadata {
        TunnelMetadata {
            interface: tunnel_parameters.interface_name.to_owned(),
        }
    }
}

#[cfg(target_os = "windows")]
#[allow(dead_code)]
fn is_ipv6_enabled_in_os() -> bool {
    use winreg::{enums::*, RegKey};

    const IPV6_DISABLED_ON_TUNNELS_MASK: u32 = 0x01;

    // Check registry if IPv6 is disabled on tunnel interfaces, as documented in
    // https://support.microsoft.com/en-us/help/929852/guidance-for-configuring-ipv6-in-windows-for-advanced-users
    let globally_enabled = RegKey::predef(HKEY_LOCAL_MACHINE)
        .open_subkey(r#"SYSTEM\CurrentControlSet\Services\Tcpip6\Parameters"#)
        .and_then(|ipv6_config| ipv6_config.get_value("DisabledComponents"))
        .map(|ipv6_disabled_bits: u32| (ipv6_disabled_bits & IPV6_DISABLED_ON_TUNNELS_MASK) == 0)
        .unwrap_or(true);

    if globally_enabled {
        true
    } else {
        log::debug!("IPv6 disabled in all tunnel interfaces");
        false
    }
}

#[cfg(not(target_os = "windows"))]
#[allow(dead_code)]
fn is_ipv6_enabled_in_os() -> bool {
    #[cfg(target_os = "linux")]
    {
        std::fs::read_to_string("/proc/sys/net/ipv6/conf/all/disable_ipv6")
            .map(|disable_ipv6| disable_ipv6.trim() == "0")
            .unwrap_or(false)
    }
    #[cfg(any(target_os = "macos", target_os = "android"))]
    {
        true
    }
}

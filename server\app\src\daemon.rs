use std::net::SocketAddr;

use serde::{Deserialize, Serialize, Serializer};
use time::{format_description, macros::offset};

mod cli;
mod server;
pub use cli::execute_command;
pub use server::serve;

const SOCK_DIR: &str = "/run/sdp";
const SOCK_PATH: &str = "/run/sdp/server.sock";

/// 在线客户端信息
#[derive(Serialize, Deserialize)]
pub struct OnlineClient {
    /// 设备ID
    pub device_id: String,
    /// 所在单位
    pub unit: String,
    /// 用户
    pub username: String,
    /// 连接地址
    pub peer_addr: SocketAddr,
    /// 上线时间
    #[serde(serialize_with = "online_serialize")]
    pub online: u64,
}

fn online_serialize<S>(online: &u64, serializer: S) -> Result<S::Ok, S::Error>
where
    S: Serializer,
{
    let time = time::OffsetDateTime::from_unix_timestamp((*online as i64) / 1000).unwrap();
    let time = time.to_offset(offset!(+8));
    let format =
        format_description::parse("[year]-[month]-[day] [hour]:[minute]:[second]").unwrap();
    let res = time.format(&format).unwrap();

    res.serialize(serializer)
}

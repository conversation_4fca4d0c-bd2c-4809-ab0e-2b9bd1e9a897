<template>
    <div>
        <!-- 选择认证方式 -->
        <el-dialog v-model="mfaAuthDialog" :title="title" width="50%" destroy-on-close :before-close="closeBox" center
            :close-on-click-modal="false" :close-on-press-escape="false">
            <el-scrollbar>
                <div style="padding-top: 10px">
                    <div class="mfa-model-text" style="text-align: center;color: #232733;">
                        为了保障您的账户安全，系统要求你完成二次身份认证
                    </div>
                    <div class="block" style="margin: 40px 0;">
                        <!-- <div v-for="item in mfaTypes" :key="item.method.code" class="mfaTypesCode">
                            <img width="46" height="46" alt="" @mouseenter="showHighlightedImage(item.method.code)"
                                @mouseleave="showNormalImage" :src="currentCode != item.method.code
                                    ? 'assets/images/defaultlogin/' + item.method.code + '.png'
                                    : '/assets/images/mfalogin/' + item.method.code + '.png'
                                    " @click="selectMfaType(item.method.code, item.method.name)" />
                            <span :title="item.method.name">{{ item.method.name }}</span>
                        </div> -->
                        <el-carousel
                            trigger="click"
                            class="approveTab"
                            height="80px"
                            :autoplay="false"
                            :loop="false"
                            :arrow="mfaTypes.length > 1 ? 'always' : 'never'"
                        >
                            <el-carousel-item
                                v-for="groupMethods in carouselMfaGroupMethods"
                                :key="groupMethods"
                                class=""
                            >
                            <div
                                v-for="additionalMethod in groupMethods"
                                :key="additionalMethod.method.code"
                                class="carouselCode"
                            >
                                <img
                                    width="40"
                                    height="40"
                                    alt=""
                                    :src="
                                    currentCode !== additionalMethod.method.code
                                    ? 'assets/images/defaultlogin/' +
                                        additionalMethod.method.code +
                                        '.png'
                                    : '/assets/images/mfalogin/' +
                                        additionalMethod.method.code +
                                        '.png'
                                "
                                    @click="selectMfaType(additionalMethod.method.code, additionalMethod.method.name)"
                                    :style="
                                    currentCode === additionalMethod.method.code &&
                                    'pointer-events: none;'
                                "
                                />
                                <span :title="additionalMethod.method.name">{{
                                    additionalMethod.method.name
                                }}</span>
                            </div>
                            </el-carousel-item>
                        </el-carousel>
                    </div>
                </div>
            </el-scrollbar>
        </el-dialog>
        <!-- 认证详情 -->
        <el-dialog v-model="mfaAuthDetailDialog" width="50%" :title="currentName + '认证'" :before-close="closeBox"
            destroy-on-close center :close-on-click-modal="false" :close-on-press-escape="false">
            <div class="auth-model">
                <div style="text-align: center;color: #232733;">为了保障您的账户安全，系统要求您完成二次身份验证</div>
                <FACE v-if="currentCode === 'face'" ref="pollMfaMethod" :name="username"
                    :participant-group-id="participantGroupId" @mfaCallbackFunc="mfaLoginCallback"
                    @generateRequestIdFailCallbackFunc="generateRequestIdFailCallback" @childEventCancel="errorLog" />

                <FACIAL v-if="currentCode === 'facial_recognition'" ref="pollMfaMethod" :name="username"
                    :participant-group-id="participantGroupId" @mfaCallbackFunc="mfaLoginCallback"
                    @generateRequestIdFailCallbackFunc="generateRequestIdFailCallback" @childEventCancel="errorLog" />

                <AUTHCODE v-if="currentCode === 'auth_code'" ref="manualMfaMethod" :name="username"
                    :participant-group-id="participantGroupId" @mfaCallbackFunc="mfaLoginCallback"
                    @generateRequestIdFailCallbackFunc="generateRequestIdFailCallback" @childEventCancel="errorLog" />

                <SMSOTP v-if="currentCode === 'sms_otp'" ref="manualMfaMethod" :name="username"
                    :participant-group-id="participantGroupId" @mfaCallbackFunc="mfaLoginCallback"
                    @generateRequestIdFailCallbackFunc="generateRequestIdFailCallback" @childEventCancel="errorLog" />

                <FINGERPRINT v-if="currentCode === 'fingerprint'" ref="pollMfaMethod" :name="username"
                    :participant-group-id="participantGroupId" @mfaCallbackFunc="mfaLoginCallback"
                    @generateRequestIdFailCallbackFunc="generateRequestIdFailCallback" @childEventCancel="errorLog" />

                <VOICE v-if="currentCode === 'voice'" ref="pollMfaMethod" :name="username"
                    :participant-group-id="participantGroupId" @mfaCallbackFunc="mfaLoginCallback"
                    @generateRequestIdFailCallbackFunc="generateRequestIdFailCallback" @childEventCancel="errorLog" />

                <MOBILECLICK v-if="currentCode === 'mobileclick'" ref="pollMfaMethod" :name="username"
                    :participant-group-id="participantGroupId" @mfaCallbackFunc="mfaLoginCallback"
                    @generateRequestIdFailCallbackFunc="generateRequestIdFailCallback" @childEventCancel="errorLog" />

                <NATIVEPASS v-if="currentCode === 'nativepass'" ref="pollMfaMethod" :name="username"
                    :participant-group-id="participantGroupId" @mfaCallbackFunc="mfaLoginCallback"
                    @generateRequestIdFailCallbackFunc="generateRequestIdFailCallback" @childEventCancel="errorLog" />

                <EMAILOTP v-if="currentCode === 'email_otp'" ref="manualMfaMethod" :name="username"
                    :participant-group-id="participantGroupId" @mfaCallbackFunc="mfaLoginCallback"
                    @generateRequestIdFailCallbackFunc="generateRequestIdFailCallback" @childEventCancel="errorLog" />

                <QRCODE v-if="currentCode === 'qrcode'" ref="pollMfaMethod" :name="username"
                    :participant-group-id="participantGroupId" @mfaCallbackFunc="mfaLoginCallback"
                    @generateRequestIdFailCallbackFunc="generateRequestIdFailCallback" @childEventCancel="errorLog">
                    <div class="auth-tip">
                        请打开<span class="mfa-text">【安全令APP】</span>进行扫码认证
                    </div>
                </QRCODE>

                <TFC200 v-if="currentCode === 'ft_c200'" ref="manualMfaMethod" :name="username"
                    :participant-group-id="participantGroupId" @mfaCallbackFunc="mfaLoginCallback"
                    @generateRequestIdFailCallbackFunc="generateRequestIdFailCallback" @childEventCancel="errorLog" />

                <ANSHUA5 v-if="currentCode === 'anshu_a5'" ref="manualMfaMethod" :name="username"
                    :participant-group-id="participantGroupId" @mfaCallbackFunc="mfaLoginCallback"
                    @generateRequestIdFailCallbackFunc="generateRequestIdFailCallback" @childEventCancel="errorLog" />

                <ESCUOTP1 v-if="currentCode === 'escuotp1'" ref="manualMfaMethod" :name="username"
                    :participant-group-id="participantGroupId" @mfaCallbackFunc="mfaLoginCallback"
                    @generateRequestIdFailCallbackFunc="generateRequestIdFailCallback" @childEventCancel="errorLog" />

                <WECHATOTP v-if="currentCode === 'wechat_otp'" ref="manualMfaMethod" :name="username"
                    :participant-group-id="participantGroupId" @mfaCallbackFunc="mfaLoginCallback"
                    @generateRequestIdFailCallbackFunc="generateRequestIdFailCallback" @childEventCancel="errorLog" />

                <ETZ203 v-if="currentCode === 'et_z203'" ref="manualMfaMethod" :name="username"
                    :participant-group-id="participantGroupId" @mfaCallbackFunc="mfaLoginCallback"
                    @generateRequestIdFailCallbackFunc="generateRequestIdFailCallback" @childEventCancel="errorLog" />

                <UKEY v-if="currentCode === 'ukey_bjca'" ref="pollMfaMethod" :name="username"
                    :participant-group-id="participantGroupId" @mfaCallbackFunc="mfaLoginCallback"
                    @generateRequestIdFailCallbackFunc="generateRequestIdFailCallback" @childEventCancel="errorLog" />

                <UKEYWQ v-if="currentCode === 'ukey_wq'" ref="pollMfaMethod" :name="username"
                    :participant-group-id="participantGroupId" @mfaCallbackFunc="mfaLoginCallback"
                    @generateRequestIdFailCallbackFunc="generateRequestIdFailCallback" @childEventCancel="errorLog" />

                <MFAPWD v-if="currentCode === 'pwd'"
                    ref="mfapwd"
                    :name="username"
                    :participant-group-id="participantGroupId"
                    @mfaCallbackFunc="mfaLoginCallback"
                    @generateRequestIdFailCallbackFunc="
                    generateRequestIdFailCallback
                    "
                />    
            </div>
        </el-dialog>
    </div>
</template>

<script>
import { invoke } from "@tauri-apps/api/tauri";
import FACE from "@/components/mfa/face.vue";
import FACIAL from "@/components/mfa/facial_recognition.vue";
import AUTHCODE from "@/components/mfa/authCode.vue";
import SMSOTP from "@/components/mfa/smsOtp.vue";
import FINGERPRINT from "@/components/mfa/fingerprint.vue";
import VOICE from "@/components/mfa/voice.vue";
import MOBILECLICK from "@/components/mfa/mobileClick.vue";
import NATIVEPASS from "@/components/mfa/nativepass.vue";
import EMAILOTP from "@/components/mfa/emailOtp.vue";
import QRCODE from "@/components/mfa/qrcode.vue";
import TFC200 from "@/components/mfa/ftc200.vue";
import ANSHUA5 from "@/components/mfa/anshuA5.vue";
import ESCUOTP1 from "@/components/mfa/escuotp1.vue";
import WECHATOTP from "@/components/mfa/wechatOtp.vue";
import ETZ203 from "@/components/mfa/etz203.vue";
import UKEY from "@/components/mfa/ukey.vue";
import UKEYWQ from "@/components/mfa/ukey_wq.vue";
import MFAPWD from "@/components/mfa/pwd.vue";
export default {
    name: "mfa",
    components: {
        FACE,
        AUTHCODE,
        SMSOTP,
        FINGERPRINT,
        VOICE,
        MOBILECLICK,
        NATIVEPASS,
        EMAILOTP,
        QRCODE,
        TFC200,
        ANSHUA5,
        ESCUOTP1,
        WECHATOTP,
        ETZ203,
        UKEY,
        UKEYWQ,
        FACIAL,
        MFAPWD
    },
    data() {
        return {
            mfaAuthDialog: false, // 选择认证方式
            mfaAuthDetailDialog: false, // 选择认证详情
            title: "身份验证提示",
            mfaTypes: [], // 认证方式
            currentCode: "",
            currentName: "",
            authResultId: "", //策略ID
            participantGroupId: "", //参与者组ID
            username: "", //用户名
            sms:"" //手机号码
            
        }
    },
    computed: {
        // 轮播认证方式分组-3个一组
        carouselMfaGroupMethods() {
            return this.groupOfThrees(this.mfaTypes);
        },

    },
    methods: {
        // 初始化
        init(data) {
            if (data.mfaTypes.length > 1) {
                this.mfaAuthDialog = true;
            } else {
                this.mfaAuthDetailDialog = true;
                this.currentName = data.mfaTypes[0].method.name
                this.currentCode = data.mfaTypes[0].method.code
            }
            
            this.mfaTypes = data.mfaTypes
            this.participantGroupId = data.participantGroupId
            this.username = data.username
            this.authResultId = data.authResultId
        },

        //取消请求/外层弹框
        closeBox(type) {
            this.$refs.pollMfaMethod && this.$refs.pollMfaMethod.cancel();
            this.mfaAuthDetailDialog = false;
            this.mfaAuthDialog = false;
            this.currentCode = "";
            type && this.$emit('close')
        },

        // 移入事件
        showHighlightedImage(res) {
            this.currentCode = res;
        },

        // 选择认证方式
        selectMfaType(code, name) {
            if (code == 'facial_recognition') {
                invoke("plugin:bio|is_support_facial_recognition").then(
                    (support) => {
                        // 不支持面部识别
                        if (!support) {
                            this.alert.error(
                                "检测到设备不支持人脸识别，请换其他方式认证",
                            );
                            return;
                        }

                        this.currentName = name;
                        this.currentCode = code;
                        this.mfaAuthDialog = false;
                        this.mfaAuthDetailDialog = true;
                    }
                );
            } else {
                this.currentName = name;
                this.currentCode = code;
                this.mfaAuthDialog = false;
                this.mfaAuthDetailDialog = true;
            }
        },

        // MFA登录回调函数
        mfaLoginCallback(request, user) {
            this.closeBox()
            this.$emit('succeed', request, user, this.authResultId)
        },

        // 认证请求获取失败回调函数
        generateRequestIdFailCallback(res) {
            this.closeBox()
            this.$emit('error', res)
        },

        // 捕捉错误信息
        errorLog(res) {
            this.closeBox()
            this.$emit('errMsg', res)
        },
        // 将认证方式转为3个一组的二维数组
        groupOfThrees(array) {
            const result = [];
            for (let i = 0; i < array.length; i += 3) {
                result.push(array.slice(i, i + 3));
            }
            return result;
        },

    },
}
</script>

<style lang="less" scoped>
.mfaTypesCode {
    width: 60px;
    height: 100px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    margin: 10px 25px 0 0;
    cursor: pointer;
    // flex-wrap: wrap;
    span {
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        font-size: 12px;
        margin-top: 10px;
        width: 100%;
        text-align: center;
    }
}

.approveTab {
  :deep(.el-carousel__indicators--horizontal) {
    display: none;
  }
}

.carouselCode {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 60px;
  height: 280px;
  cursor: pointer;

  span {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    font-size: 12px;
    width: 100%;
    margin-top: 5px;
    text-align: center;
  }
}

.carouselCode:nth-child(2) {
  margin: 0 10px;
}

:deep(.el-carousel__button) {
  display: none;
}

.el-carousel__item {
  display: flex;
  align-items: center;
  justify-content: space-around;
}

:deep(.el-carousel__arrow) {
  width: 30px;
  height: 30px;
  background-color: #fff;
  color: #8b8b8f;

  &:hover {
    color: #f9780c;
  }
}

:deep(.el-carousel__arrow--right) {
  right: 0;
  top: 36px;
}

:deep(.el-carousel__arrow--left) {
  left: 0;
  top: 36px;
}
</style>
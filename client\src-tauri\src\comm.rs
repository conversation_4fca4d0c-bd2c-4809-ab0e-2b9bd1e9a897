use chrono::Local;
use serde_json::{Number, Value};
use std::net::{Ipv4Addr, Ipv6Addr};
use tauri::{async_runtime::<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, Wry};
use tokio::{sync::RwLock, task::<PERSON><PERSON><PERSON><PERSON><PERSON>};

use communicate_interface::{
    types::{communicate_service_client::CommunicateServiceClient, core_event::Event},
    Channel,
};
use types::{
    net::Connectivity,
    states::{BackendState, TunnelState},
    updater::{DownloadProgress, UpdateStatus},
};

#[cfg(not(feature = "sdp"))]
use crate::{config::AppConfig, http};

use crate::{state::AppState, tunnel::tunnel_error};

pub async fn spawn(app_handle: AppHandle<Wry>) -> anyhow::Result<()> {
    log::debug!(target: "app", "Starting connect to core");

    let mut rpc = communicate_interface::new_rpc_client().await.unwrap();

    app_handle.manage(Mutex::new(Some(rpc.clone())));
    app_handle.manage(Mutex::new(Some(listen(rpc.clone(), app_handle.clone()))));

    let device_id = rpc
        .get_device_id(tonic::Request::new(()))
        .await
        .unwrap()
        .into_inner();

    let system_info = rpc
        .get_system_info(tonic::Request::new(()))
        .await
        .unwrap() // unwrap is safe
        .into_inner();

    log::debug!(target: "app", "Connected to oneidcore: {}", device_id);

    let app_state = app_handle.state::<RwLock<AppState>>();
    let mut app_state = app_state.write().await;
    app_state.device_id = Some(device_id);
    app_state.system_info = serde_json::from_str(&system_info).unwrap();
    drop(app_state);

    Ok(())
}

fn listen(
    mut rpc: CommunicateServiceClient<Channel>,
    app_handle: AppHandle<Wry>,
) -> JoinHandle<()> {
    tokio::spawn(async move {
        while let Ok(events) = rpc.events_listen(()).await {
            let mut events = events.into_inner();
            while let Ok(Some(event)) = events.message().await {
                match event.event.unwrap() {
                    Event::TunnelState(state) => {
                        log::debug!(target: "app", "New tunnel state: {:?}", state);
                        let state = TunnelState::try_from(state);
                        if let Ok(TunnelState::Error(_error_state)) = state {
                            tunnel_error(app_handle.clone()).await;
                        }
                    }
                    Event::BackendState(state) => {
                        log::debug!(target: "app", "New backend state: {:?}", state);

                        let state = BackendState::try_from(state);
                        match state {
                            Ok(BackendState::Disconnected(reason)) => {
                                tokio::spawn(crate::event_listener::on_backend_disconnect(
                                    reason,
                                    app_handle.clone(),
                                ));
                            }
                            // Ok(BackendState::Disconnecting | BackendState::Error(_)) => {
                            //     emit_event!("logout", None);
                            // }
                            Ok(BackendState::Connected) => {
                                // 禁用地址设置地址
                                let tray_handle = app_handle.tray_handle();
                                #[cfg(not(feature = "cluster"))]
                                let menu_handle = tray_handle.get_item("set");
                                #[cfg(feature = "cluster")]
                                let menu_handle = tray_handle.get_item("cluster_config");
                                _ = menu_handle.set_enabled(false);
                            }
                            _ => {
                                let tray_handle = app_handle.tray_handle();
                                #[cfg(not(feature = "cluster"))]
                                let menu_handle = tray_handle.get_item("set");
                                #[cfg(feature = "cluster")]
                                let menu_handle = tray_handle.get_item("cluster_config");
                                _ = menu_handle.set_enabled(true);
                            }
                        }
                    }
                    Event::Ticket(ticket) => match ticket.ticket_type {
                        1 => {
                            log::debug!(target: "app", "Received new reconnect token");
                            let token = String::from_utf8(ticket.ticket).unwrap();
                            let (token, expire) = token.split_once('@').unwrap();

                            let app_state = app_handle.state::<RwLock<AppState>>();
                            let mut app_state = app_state.write().await;
                            let expire = expire.parse::<i64>().unwrap();
                            let now = Local::now().timestamp_millis();

                            app_state.reconnect_token =
                                Some((token.to_owned(), now + expire * 60 * 1000));
                        }
                        _ => {
                            log::debug!(target: "app", "Emit sso ticket event");
                            _ = app_handle.emit_all(
                                "ticket",
                                String::from_utf8(ticket.ticket).unwrap_or_default(),
                            );
                        }
                    },
                    Event::ProxyReady(_) => {
                        let app_state = app_handle.state::<RwLock<AppState>>();
                        let mut app_state = app_state.write().await;
                        app_state.proxy_ready = true;
                    }
                    Event::Connectivity(connectivity) => {
                        let connectivity = Connectivity::try_from(connectivity).unwrap();
                        log::debug!(target: "app", "Emit network change event: {:?}", connectivity);

                        tokio::spawn(crate::event_listener::on_connectivity_event(
                            connectivity,
                            app_handle.clone(),
                        ));
                    }
                    Event::CloseReason(reason) => {
                        log::debug!(target: "app", "Emit logout event");
                        _ = app_handle.emit_all("logout", reason.reason.to_string());
                    }
                    Event::NotificationType(r#type) => {
                        log::debug!(target: "app", "Emit notification event");
                        _ = app_handle.emit_all("notification", r#type.r#type.to_string());
                    }
                    Event::ChangePwdResult(payload) => {
                        log::debug!(target: "app", "Emit change pwd result event");
                        _ = app_handle.emit_all(
                            "change_pwd_result",
                            String::from_utf8(payload.result).unwrap_or_default(),
                        );
                    }
                    Event::NeedAuth(_) => {
                        log::debug!(target: "app", "Emit mfa event");
                        _ = app_handle.emit_all("mfa", Option::<&str>::None);
                    }
                    Event::AccessDenied(denied) => {
                        log::debug!(target: "app", "Emit access denied event");
                        let ip = if denied.ip_addr.len() == 16 {
                            Ipv6Addr::from(
                                <Vec<u8> as TryInto<[u8; 16]>>::try_into(denied.ip_addr).unwrap(),
                            )
                            .to_string()
                        } else {
                            Ipv4Addr::from(
                                <Vec<u8> as TryInto<[u8; 4]>>::try_into(denied.ip_addr).unwrap(),
                            )
                            .to_string()
                        };
                        _ = app_handle.emit_all("denied_access", ip);
                    }
                    Event::Environment(env) => {
                        log::debug!(target: "app", "Environment data changed");
                        let app_handle = app_handle.clone();
                        tokio::spawn(async move {
                            let mut env = serde_json::from_slice::<Value>(&env.data).unwrap();

                            let app_state = app_handle.state::<RwLock<AppState>>();
                            let app_state = app_state.read().await;
                            if let Some(timestamp) = app_state.last_login_time {
                                env["lastLoginTime"] = Value::Number(Number::from(timestamp));
                            }
                            if let Some(user) = &app_state.user {
                                env["recentlyLoginUser"] = Value::String(user.username.to_owned());
                            }
                            #[cfg(feature = "sdp")]
                            {
                                _ = app_handle.emit_all("environment", env.to_string());
                            }

                            #[cfg(not(feature = "sdp"))]
                            send_env_to_endpoint(&env, app_handle).await
                        });
                    }
                    Event::UpdateStatus(update_status) => {
                        let update_status: UpdateStatus = update_status.into();
                        log::debug!(target: "app", "Emit update status: {:?}", update_status);
                        if let Err(error) = app_handle.emit_all("update_status", update_status) {
                            log::error!("Emit update status failed. {error}");
                        }
                    }
                    Event::DownloadProgress(progress) => {
                        let progress: DownloadProgress = progress.into();
                        log::debug!(target: "app", "Emit download progress: {:?}", progress);
                        if let Err(error) = app_handle.emit_all("download_progress", progress) {
                            log::error!("Emit download progress failed. {error}");
                        }
                    }
                }
            }
        }
    })
}

#[cfg(not(feature = "sdp"))]
async fn send_env_to_endpoint(
    env: &Value,
    app_handle: AppHandle<Wry>,
) -> Result<u16, reqwest::Error> {
    let client = http::client().await;
    let config = app_handle.state::<Mutex<AppConfig>>();
    let config = config.lock().await;

    let Some(endpoint) = config.endpoint.clone() else {
        return Ok(200);
    };

    let Some(ref tenant) = config.tenant else {
        return Ok(200);
    };

    let tenant_code = tenant.code.clone();
    drop(config);

    let device_id = env["deviceId"].as_str().unwrap();

    let url = format!("{endpoint}/common-sso/v1/sdk/client/environment/{device_id}/pc");
    let response = client
        .post(url)
        .header("TENANT", &tenant_code)
        .json(env)
        .send()
        .await?;
    Ok(response.status().as_u16())
}

<template>
  <div
    class="mfa-content"
    :style="type ? '' : 'margin-top: 61px; padding-bottom: 61px;'"
  >
    <h3 v-if="type">邮箱登录</h3>
    <div class="auth-code-content">
      <div v-if="type">
        <div class="inputItem">
          <div class="inputContent">
            <el-input
              ref="username"
              v-model="username"
              placeholder="请输入邮箱号"
            />
          </div>
        </div>
      </div>
      <div class="otp-item">
        <el-input
          v-model="emailCode"
          placeholder="请输入验证码"
          :class="type ? 'auth-code-input' : 'auth-input'"
          maxlength="6"
        >
          <template #append>
            <el-button
              :disabled="disabled"
              class="send-code-button"
              size="small"
              style="font-size: 12px"
              @click="getEmailCode"
              :loading="btnLoading"
            >
              {{ sendCode }}
            </el-button>
          </template>
        </el-input>
      </div>
      <el-button
        ref="confirmBtn"
        type="primary"
        :class="{
          loginBtn: type === 'login',
          'mfa-confirm-btn': type !== 'login',
        }"
        :disabled="
          (type === 'login' && !username) ||
          !requestId ||
          !emailCode ||
          subbmitting
        "
        @click="submitAuth"
      >
        {{ type === "login" ? "登录" : "确定" }}
      </el-button>
      <userAgreement v-if="type" />
    </div>
  </div>
</template>

<script>
import { getEmailCode, verifyEmailCode, cancelAuth } from "@/api/service";
import userAgreement from "../../views/userAgreement.vue";

export default {
  name: "EMAILOTP",
  components: {
    userAgreement,
  },
  props: {
    participantGroupId: {
      type: String,
      default: "",
    },
    userId: {
      type: String,
      default: "",
    },
    type: {
      type: String,
      default: "",
    },
    name: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      requestId: "",
      emailCode: "",
      sendCode: "获取验证码",
      countdown: undefined,
      disabled: false,
      defaultTime: 60,
      reg: /^\d+$/,
      username: "" || this.name,
      btnLoading: false,
      subbmitting: false,
    };
  },
  created() {
    // this.getRequestId();
  },
  methods: {
    getEmailCode() {
      if (!this.username && this.type === "login") {
        this.alert.error("请输入邮箱");
        return;
      }
      this.btnLoading = true;
      let data = {
        participantGroupId: this.participantGroupId,
        participantTypes: this.type === "login" ? "05" : "01,03",
        participantKeyword: this.userId || this.username,
      };

      getEmailCode(data)
        .then((result) => {
          this.requestId = result.data.requestId;
          this.$emit("requestId", this.requestId);
          this.alert.success(
            "邮件已发送至邮箱" + result.data.email + "，请注意查收。"
          );
          this.countdown = setInterval(this.timer, 1000);
        })
        .catch((error) => {
          this.btnLoading = false;
          this.$emit("generateRequestIdFailCallbackFunc", error);
        });
    },
    timer() {
      this.btnLoading = false;
      this.disabled = true;
      this.sendCode =
        this.type === "login"
          ? "重新获取(" + this.defaultTime + ")"
          : this.defaultTime + "s";
      this.defaultTime--;
      if (this.defaultTime < 0) {
        clearInterval(this.countdown);
        this.disabled = false;
        this.sendCode = "重新获取";
        this.defaultTime = 60;
      }
    },
    /**
     * 提交认证
     */
    async submitAuth() {
      try {
        this.subbmitting = true;
        await this.Complete();
      } finally {
        this.subbmitting = false;
      }
    },
    Complete() {
      if (!this.username && this.type === "login") {
        this.alert.error("请输入邮箱");
        return;
      }

      if (!this.emailCode) {
        this.alert.error("请输入邮箱验证码");
        return;
      }

      if (this.emailCode && !this.reg.test(this.emailCode)) {
        this.alert.error("验证码格式错误");
        return;
      }
      if (!this.requestId) {
        this.alert.error("请发送邮箱验证码");
        return;
      }
      return verifyEmailCode({
        requestId: this.requestId,
        code: this.emailCode,
      })
        .then(() => {
          this.$emit("mfaCallbackFunc", {
            requestId: this.requestId,
            type: this.emailCode,
          });
        })
        .catch((err) => {
          this.$emit("childEventCancel", this.requestId);
          let msg = JSON.parse(err.message);
          if (
            msg.data.messageKey === "MFA.MFA.AUTH.CANCELED" ||
            msg.data.messageKey === "MFA.MFA.AUTH.ALREADY"
          ) {
            this.alert.error("认证已失效，请重新发送验证码");
            clearInterval(this.countdown);
            this.disabled = false;
            this.sendCode = "重新获取";
            this.defaultTime = 60;
          }
        });
    },
    cancel() {
      if (this.requestId) {
        let requestId = this.requestId;
        this.requestId = "";
        cancelAuth(requestId);
      }
    },
  },
  watch: {
    username(newValue, oldValue) {
      console.log(newValue);
      if (newValue) {
        this.emailCode = "";
      }
    },
  },
};
</script>

<style scoped lang="less">
.auth-code-content {
  width: 100%;
  margin: auto;
  text-align: center;

  .auth-code-input {
    width: 100%;
    margin-bottom: 30px;
    height: 45px;
    padding-left: 16px;
    border-bottom: 1px solid #d1d3db;
    // border-radius: 30px;
    &:hover {
      border-bottom: 1px solid #f9780c;
    }
  }

  .inputItem {
    text-indent: 0.6em;
    position: relative;
    margin-top: 1.8rem;
    border-bottom: 1px solid #d1d3db;
    // border-radius: 30px;
    margin-bottom: 10px;
    &:hover {
      border-bottom: 1px solid #f9780c;
    }
  }

  .login_icon {
    color: #bfbfbf;
    font-size: 22px;
  }

  .inputContent {
    padding: 0.4rem;
    width: 240px;
    border: 0;
  }

  :deep(.el-input__wrapper) {
    border: 0 !important;
    padding: 1px 1px;
  }

  :deep(.el-input-group__append) {
    border: 0;
    // background: #fbece1;
  }

  .send-code-button,
  .send-code-button:disabled,
  .send-code-button:hover {
    border: none;
    background-color: unset;
  }
}

.otp-item {
  position: relative;
}

.icon_password {
  position: absolute;
  left: 2px;
  top: 7px;
  z-index: 999999;
}
</style>

use crate::{
    proxy_request::{Proxy<PERSON>andle, ProxyHttp, ProxyHttpResult, REQUESTS, REQUEST_SEQUENCE},
    updater::{
        get_updater_arch, get_updater_target, run, save_files, verify_signature, Error,
        RemoteRelease, Result, FLAG,
    },
    EventListener, PRODUCT_NAME,
};
use futures::{channel::mpsc, StreamExt};
use http::{
    header::{ACCEPT, RANGE},
    HeaderValue,
};
use proxy_request::{HeaderMap, HttpRequest, ResponseData, ResponseType};
use semver::Version;
use std::{fs, io::Cursor, path::PathBuf, sync::atomic::Ordering, time::Duration};
use tokio::time::timeout;
use types::backend::ConnectionConfig;
use url::Url;

#[cfg(target_os = "linux")]
use crate::linux::detect_package_ext;

pub struct Update {
    /// 代理处理器
    proxy_handle: Proxy<PERSON>andle,
    /// 连接配置
    connection_config: ConnectionConfig,
    /// 当前版本
    _current_version: Version,
    /// 是否需要更新
    should_update: bool,
    /// 下载的临时文件存放目录
    tmp_dir: Option<PathBuf>,
    /// 更新内容
    remote_release: Option<RemoteRelease>,
}

#[derive(Clone)]
pub struct UpdateBuilder {
    pub version: Version,
    pub platform: String,
    pub arch: String,
    pub ext: &'static str,
    pub model: Option<String>,
    pub proxy_handle: Option<ProxyHandle>,
    pub connection_config: Option<ConnectionConfig>,
}

impl Default for UpdateBuilder {
    fn default() -> Self {
        #[cfg(windows)]
        let ext = "exe";
        #[cfg(target_os = "macos")]
        let ext = "dmg";
        #[cfg(target_os = "linux")]
        let ext = detect_package_ext();

        let (Some(platform), Some(arch)) = (get_updater_target(), get_updater_arch(ext)) else {
            panic!("Unsupported platform.");
        };

        let version: Version = version::PRODUCT_VERSION.parse().unwrap();
        Self {
            version,
            platform: platform.to_owned(),
            arch: arch.to_owned(),
            ext,
            model: None,
            proxy_handle: None,
            connection_config: None,
        }
    }
}

impl UpdateBuilder {
    pub fn model(&mut self, model: String) -> &mut Self {
        self.model = Some(model);
        self
    }

    pub fn proxy_handle(&mut self, proxy_handle: ProxyHandle) -> &mut Self {
        self.proxy_handle = Some(proxy_handle);
        self
    }

    pub fn connection_config(&mut self, connection_config: ConnectionConfig) -> &mut Self {
        self.connection_config = Some(connection_config);
        self
    }

    pub async fn build(&self) -> Option<Update> {
        let current_version: Version = version::PRODUCT_VERSION.parse().unwrap();

        let this = self.clone();
        let mut headers = http::header::HeaderMap::new();
        headers.insert(ACCEPT, "application/json".parse().unwrap());
        headers.insert("ext", self.ext.parse().unwrap());

        let url = format!(
            "https://sso.jingantech.com/check_update/{}/{}/{}/{}/{}",
            PRODUCT_NAME,
            this.platform,
            this.arch,
            this.version,
            this.model.unwrap()
        );

        let check_update_request = HttpRequest {
            method: "GET".to_string(),
            url: Url::parse(url.as_str()).unwrap(),
            query: None,
            headers: Some(HeaderMap(headers)),
            body: None,
            timeout: Some(Duration::from_secs(5)),
            response_type: Some(ResponseType::Json),
            stream: None,
        };

        let (tx, mut rx) = mpsc::channel(1);
        let (chunk_tx, mut chunk_rx) = mpsc::channel(1);
        let seq = REQUEST_SEQUENCE.fetch_add(1, Ordering::Acquire);
        REQUESTS.insert(seq, (tx, chunk_tx)).await;

        let proxy_handle = this.proxy_handle.unwrap();
        // 执行请求
        proxy_handle.send_request(ProxyHttp {
            connection_config: Some(this.connection_config.clone().unwrap()),
            seq,
            request: serde_json::to_vec(&check_update_request).unwrap(),
        });

        let remote_release = match timeout(Duration::from_secs(5), rx.next()).await {
            Ok(next) => match next {
                Some(ProxyHttpResult::Ok(result, is_chunk)) => {
                    let Ok(response_data) = serde_json::from_slice::<ResponseData>(&result) else {
                        log::error!("Failed to deserialize update json to response data");
                        return None;
                    };
                    if response_data.status == 204 {
                        log::debug!("No updates available.");
                        return Some(Update {
                            proxy_handle,
                            connection_config: this.connection_config.unwrap(),
                            _current_version: current_version.clone(),
                            should_update: false,
                            tmp_dir: None,
                            remote_release: None,
                        });
                    }

                    // 异常情况
                    if response_data.status / 100 != 2 {
                        log::error!("Update interface request failed: {}", response_data.status);
                        return None;
                    }

                    if is_chunk {
                        let mut chunk_bytes = vec![];
                        while let Some(chunk) = chunk_rx.next().await {
                            chunk_bytes.extend(chunk);
                        }

                        let Ok(remote_release) =
                            serde_json::from_slice::<RemoteRelease>(&chunk_bytes)
                        else {
                            log::error!("Failed to deserialize update json");
                            return None;
                        };
                        remote_release
                    } else {
                        if response_data.data.is_null() {
                            log::debug!("No updates available.");
                            return Some(Update {
                                proxy_handle,
                                connection_config: this.connection_config.unwrap(),
                                _current_version: current_version.clone(),
                                should_update: false,
                                tmp_dir: None,
                                remote_release: None,
                            });
                        }
                        let Ok(remote_release) =
                            serde_json::from_value::<RemoteRelease>(response_data.data)
                        else {
                            log::error!("Failed to deserialize update json");
                            return None;
                        };
                        remote_release
                    }
                }
                _ => {
                    log::error!("Check update failed");
                    return None;
                }
            },
            Err(_) => {
                log::error!("Check update timeout");
                return None;
            }
        };

        Some(Update {
            proxy_handle,
            connection_config: this.connection_config.unwrap(),
            _current_version: current_version.clone(),
            should_update: remote_release.version > current_version,
            tmp_dir: None,
            remote_release: Some(remote_release),
        })
    }
}

impl Update {
    pub fn should_update(&self) -> bool {
        self.should_update
    }

    pub fn remote_release(&self) -> Option<&RemoteRelease> {
        self.remote_release.as_ref()
    }

    pub async fn download<L: EventListener>(&mut self, event_listener: L) {
        let Some(remote_release) = self.remote_release.as_ref() else {
            // 没有更新可用
            event_listener.notify_update_status("ERROR", Some(String::from("Update unavailable")));
            return;
        };

        FLAG.store(true, Ordering::Release);

        // 开始下载
        event_listener.notify_update_status("PENDING", None);

        let mut headers = http::header::HeaderMap::new();
        headers.insert(ACCEPT, "application/octet-stream".parse().unwrap());
        headers.insert(
            "User-Agent",
            HeaderValue::from_str("sdp/selfupdater").unwrap(),
        );

        macro_rules! base_request {
            () => {
                HttpRequest {
                    method: "GET".to_string(),
                    url: remote_release.manifest.url.clone(),
                    query: None,
                    headers: None,
                    body: None,
                    timeout: None,
                    response_type: Some(ResponseType::Binary),
                    stream: None,
                }
            };
        }

        // 1. 获取更新包大小
        let file_size = match self.size_request(base_request!(), headers.clone()).await {
            Ok(file_size) => file_size,
            Err(error) => {
                log::error!("Updater: {error}");
                event_listener.notify_update_status("ERROR", Some(error.to_string()));
                return;
            }
        };

        let buffer = match self
            .content_request(base_request!(), headers, file_size, event_listener.clone())
            .await
        {
            Ok(buffer) => buffer,
            Err(Error::Cancel) => {
                log::info!("Updater: User canceled");
                event_listener.notify_update_status("CANCEL", None);
                return;
            }
            Err(error) => {
                log::error!("Updater: {error}");
                event_listener.notify_update_status("ERROR", Some(error.to_string()));
                return;
            }
        };

        log::trace!("Updater buffer size: {}", buffer.len());

        // create memory buffer from our archive (Seek + Read)
        let mut archive_buffer = Cursor::new(buffer);

        // We need an announced signature by the server
        // if there is no signature, bail out.
        if let Err(error) =
            verify_signature(&mut archive_buffer, &remote_release.manifest.signature)
        {
            log::error!("Updater: {error}");
            event_listener.notify_update_status("ERROR", Some(error.to_string()));
            return;
        }

        // 下载完成
        event_listener.notify_update_status("DOWNLOADED", None);

        #[cfg(target_os = "windows")]
        let save_result = save_files(archive_buffer);

        #[cfg(not(target_os = "windows"))]
        let save_result = save_files(archive_buffer);

        match save_result {
            Ok(path) => {
                self.tmp_dir = Some(path);
            }
            Err(error) => {
                let msg = format!("Save error: {error}",);
                log::error!("Updater: {msg}");
                event_listener.notify_update_status("ERROR", Some(msg));
            }
        }
    }

    pub async fn install(&mut self) -> Result<()> {
        let Some(tmp_dir) = &self.tmp_dir else {
            log::warn!("No upgrade file");
            return Ok(());
        };
        log::trace!("Install updater: {:?}", self.tmp_dir);
        run(tmp_dir)
    }

    pub fn cancel() {
        FLAG.store(false, Ordering::Release);
    }

    /// 取消更新后, 删除临时文件
    pub fn clean(&self) {
        if let Some(tmp_dir) = &self.tmp_dir {
            log::debug!("Clean tmpdir: {:?}", tmp_dir);
            _ = fs::remove_dir_all(tmp_dir);
        }
    }

    async fn size_request(
        &self,
        mut installer_size_request: HttpRequest,
        mut headers: http::header::HeaderMap,
    ) -> Result<u64> {
        headers.insert(RANGE, "bytes=0-1".parse().unwrap());
        installer_size_request.headers = Some(HeaderMap(headers));

        let (tx, mut rx) = mpsc::channel(1);
        let (chunk_tx, _chunk_rx) = mpsc::channel(1);
        let seq = REQUEST_SEQUENCE.fetch_add(1, Ordering::Acquire);
        REQUESTS.insert(seq, (tx, chunk_tx)).await;

        // 执行请求
        self.proxy_handle.send_request(ProxyHttp {
            connection_config: Some(self.connection_config.clone()),
            seq,
            request: serde_json::to_vec(&installer_size_request).unwrap(),
        });

        match rx.next().await {
            None => Err(Error::Network("Timeout".to_owned())),
            Some(ProxyHttpResult::Ok(result, _is_chunk)) => {
                // Content-Length
                let response = serde_json::from_slice::<ResponseData>(&result).unwrap();

                // make sure it's success
                if response.status / 100 != 2 {
                    return Err(Error::Network(format!(
                        "Download request failed with status: {}",
                        response.status
                    )));
                }

                let range = response.headers.get("content-range").unwrap();
                let (_range, size) = range.split_once('/').unwrap();

                Ok(size.parse().unwrap())
            }
            Some(ProxyHttpResult::Fail(reason)) => Err(Error::Network(reason)),
            Some(ProxyHttpResult::Timeout) => Err(Error::Network("Timeout".to_owned())),
            Some(ProxyHttpResult::ConnectionFailed) => Err(Error::Network("Timeout".to_owned())),
        }
    }

    async fn content_request<L: EventListener>(
        &self,
        mut installer_range_request: HttpRequest,
        headers: http::header::HeaderMap,
        file_size: u64,
        event_listener: L,
    ) -> Result<Vec<u8>> {
        installer_range_request.headers = Some(HeaderMap(headers));

        let (tx, mut rx) = mpsc::channel(1);
        let (chunk_tx, mut chunk_rx) = mpsc::channel(1);
        let seq = REQUEST_SEQUENCE.fetch_add(1, Ordering::Acquire);
        REQUESTS.insert(seq, (tx, chunk_tx)).await;

        // 执行请求
        self.proxy_handle.send_request(ProxyHttp {
            connection_config: Some(self.connection_config.clone()),
            seq,
            request: serde_json::to_vec(&installer_range_request).unwrap(),
        });

        match rx.next().await {
            None => Err(Error::Network("Timeout".to_owned())),
            Some(ProxyHttpResult::Ok(result, is_chunk)) => {
                // Content-Length
                let response = serde_json::from_slice::<ResponseData>(&result).unwrap();
                // make sure it's success
                if response.status / 100 != 2 {
                    return Err(Error::Network(format!(
                        "Download request failed with status: {}",
                        response.status
                    )));
                }

                log::trace!("response headers: {:?}", response.headers);

                if is_chunk {
                    let mut buffer = Vec::with_capacity(file_size as usize);
                    while let Some(buf) = chunk_rx.next().await {
                        // 检查是否已取消更新
                        if !FLAG.load(Ordering::Acquire) {
                            return Err(Error::Cancel);
                        }
                        buffer.extend(buf);
                        event_listener.notify_download_progress(buffer.len(), file_size);
                    }
                    Ok(buffer)
                } else {
                    Ok(serde_json::from_value::<Vec<u8>>(response.data).unwrap())
                }
            }
            Some(ProxyHttpResult::Fail(reason)) => Err(Error::Network(reason)),
            Some(ProxyHttpResult::Timeout) => Err(Error::Network("Timeout".to_owned())),
            Some(ProxyHttpResult::ConnectionFailed) => Err(Error::Network("Timeout".to_owned())),
        }
    }
}

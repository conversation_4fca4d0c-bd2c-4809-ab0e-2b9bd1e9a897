use std::{
    io::{self, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Write},
    os::unix::net::UnixStream,
};

use crate::{
    cli::Command,
    daemon::{OnlineClient, SOCK_PATH},
};

pub fn execute_command(command: Command) -> io::Result<()> {
    let mut stream = UnixStream::connect(SOCK_PATH)
        .map_err(|err| io::Error::new(err.kind(), "Server not started"))?;

    let cmd = serde_json::to_string(&command).unwrap();
    writeln!(stream, "{}", cmd)?;

    let mut reader = BufReader::new(stream);
    let mut response = String::new();

    match command {
        Command::Quit => {
            reader.read_line(&mut response)?;
            print!("{}", response);
        }
        Command::ViewLogFilter => {
            reader.read_line(&mut response)?;
            println!("current log filter: {}", response);
        }
        Command::SetLogFilter { .. } => {
            reader.read_line(&mut response)?;
            println!("{}", response);
        }
        Command::ViewClients => loop {
            reader.read_line(&mut response)?;

            let ending = response.trim().ends_with("END");
            if ending {
                response = response.replace("END", "");
            }
            let online_clients =
                serde_json::from_str::<Vec<OnlineClient>>(&response).unwrap_or_default();

            println!("{}", serde_json::to_string_pretty(&online_clients).unwrap());

            if ending {
                break;
            }
        },
        Command::ViewTraceMapping => {
            reader.read_line(&mut response)?;
            let mapping = serde_json::from_str::<serde_json::Value>(&response).unwrap_or_default();
            println!("{}", serde_json::to_string_pretty(&mapping).unwrap());
        }
        _ => (),
    }

    Ok(())
}

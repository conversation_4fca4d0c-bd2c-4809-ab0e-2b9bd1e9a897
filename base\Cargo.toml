[package]
edition = "2021"
name = "base"
version = "0.0.0"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[dependencies]
asn1 = "0.13.0"
base64 = "0.21.0"
bytes = "1.1.0"
coarsetime = { workspace = true }
faster-hex = "0.6.1"
gm-sm2 = { workspace = true }
gm-sm3 = { workspace = true }
hex = { workspace = true }
ipnet = "2.5.0"
iprange = "0.6.7"
log = "0.4.0"
pem = "1.0.2"
percent-encoding = "2.1.0"
pkcs8 = { workspace = true }
pnet_packet = "0.34.0"
rand = "0.8.5"
rustls = { version = "0.23.23", default-features = false, features = [
    "logging",
    "std",
    "tls12",
    "tlcp11",
    "ring",
] }
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0.85"
tlv = { path = "../tlv/tlv" }
tokio = { workspace = true, features = ["full"] }
tokio-rustls = { version = "0.26.2", default-features = false, features = [
    "logging",
    "tls12",
    "ring",
] }
tokio-util = { version = "0.7.4", features = ["codec"] }
untrusted = "0.9.0"

[features]
default = []
tlcp = []

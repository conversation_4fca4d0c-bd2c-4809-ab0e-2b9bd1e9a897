import{cf as re,bV as ue,bU as m,cg as he,bE as de,bJ as pe,q as F,c1 as y,ch as fe,ci as me,cj as ge,ck as _e,cl as Ce,cm as q,cn as Ae,co as ke,cp as Me,cq as Ie,cr as ye,aR as l,aS as E,bz as w,aE as o,u as f,I as d,bp as D,v as c,bA as ve,bx as p,t as u,F as b,aP as S,cs as we,s as h,b3 as A,ao as be,H as k,aI as Se,aF as Fe}from"./index-8ff3976b.js";import{F as Re,A as Ne,S as Te,a as Ee,V as qe,M as De,N as Ke,E as Oe,Q as Le,T as Pe,b as xe,c as Ve,W as Ge,d as Ue,U as Be,e as He,f as We}from"./wechatOtp-757be8f5.js";async function R(e,s){return re({__tauriModule:"Shell",message:{cmd:"open",path:e,with:s}})}const Ye="/assets/images/mfa/default.png",Je=(e,s,i)=>new Promise((r,t)=>{(async()=>{let a=await ue(s||e,_=>{a();let M=JSON.parse(_.payload).data;r(M)});setTimeout(function(){a(),t("timeout")},3e3)})(),m(e,i).catch(a=>{he.error(a.toString()),t(a)})}),Qe="/assets/images/noData.png",je="/assets/listLoading.gif";function ze(e,s){return e.catch(()=>{s()}),e}const Ze={name:"Home",components:{FACE:Re,AUTHCODE:Ne,SMSOTP:Te,FINGERPRINT:Ee,VOICE:qe,MOBILECLICK:De,NATIVEPASS:Ke,EMAILOTP:Oe,QRCODE:Le,TFC200:Pe,ANSHUA5:xe,ESCUOTP1:Ve,WECHATOTP:Ge,ETZ203:Ue,UKEY:Be,UKEYWQ:He,FACIAL:We},setup(){const e=pe();return{networkStatus:F(()=>e.getters.networkStatus),connectState:F(()=>e.getters.connectState),isLogin:F(()=>e.getters.isLogin),currentUser:F(()=>e.getters.currentUser)}},inject:["reload"],data(){return{tenantCode:window.CONFIG.selected_tenant,defaultAppLogo:"/assets/default_app_logo.png",loginState:!0,loading:!1,isClear:!1,busy:!0,interval:void 0,keyword:"",applynames:"",myapps:[],appId:"",appName:"",title:"选择认证方式",currentCode:"",clientApiKey:"",authResultId:"",accounts:[],selectedAccount:"",participantGroupId:"",participantKeyword:"",selectAccountDialog:!1,mfaAuthDialog:!1,mfaAuthDetailDialog:!1,mfaTypes:[],authSource:"",target_resource:"",deviceId:"",hasNext:!1,currentPage:0,totalPages:0,defaultSrc:Ye,requestId:"",appType:"",stopPolling:!0,lockup:!1,homeActiveName:"all",currentName:"",institutionalList:[],sliceList:[],selectId:"",timer:null,pitchId:"",allResource:[]}},async created(){console.log(this.networkStatus,"联网状态");let e=this;const s=function(){e.loadApps(!0,!0,0,void 0)};window.invokeMfaAuth=e.invokeMfaAuth,window.relogin=function(){window.previousIAMAutoLogin=!0,Je("plugin:sdp|ticket","ticket").then(i=>{(function(){we(i).then(()=>{s()}).finally(()=>{window.previousIAMAutoLogin=!1})})()}).catch(i=>{y("log",{level:"Error",message:"get ticket: "+JSON.stringify(i)}),e.reload()})},this.networkStatus?s():this.busy=!1},beforeUnmount(){location.hash.includes("/login")&&(this.stopPolling=!1)},methods:{getLogin(){fe().then(()=>{this.stopPolling&&window.setTimeout(this.getLogin,1e3*60*10)})},numberToIp(e){let s="";if(e<=0)return s;const i=e<<0>>>24,r=e<<8>>>24,t=e<<16>>>24,a=e<<24>>>24;return s+=i+"."+r+"."+t+"."+a,s},loadApps(e,s,i,r){this.busy=!0,e===!0&&(this.currentPage=0,this.myapps=[]),me(this.keyword).then(t=>{if(!t){this.busy=!1;return}const a=this.$refs.textContainer;let _=null,M=a.clientWidth-200;for(let I=0;I<t.data.groupList.length;I++){let C=t.data.groupList[I];if(M>this.calculateTextWidth(C.name))M-=this.calculateTextWidth(C.name);else{_=I;break}}_?this.sliceList=t.data.groupList.splice(_):this.sliceList=[],this.institutionalList=t.data.groupList,this.resourceMethod(e,t.data.resourceList),this.totalPages=Math.ceil(t.data.resourceList.length/24)},t=>{if(this.busy=!1,!window.previousUserLifecycleInvalid){if(typeof t=="string"){if(t.indexOf("os error 11004")>-1&&s===!0){let a=this;setTimeout(function(){a.loadApps(!0,!1,i+1,r)},300)}}else if(t&&t.data&&t.data.errorCode!=="SYSTEM-0002"){y("log",{level:"Error",message:"Load apps: session expired."});let a=this;s===!0&&typeof i=="number"&&i<5&&setTimeout(function(){a.loadApps(!0,!0,i+1,r)},300)}}})},resourceMethod(e,s){this.busy=!0;let i=[];s&&(this.allResource=s),e===!0?i=this.allResource.slice(this.currentPage,24):i=this.allResource.slice(this.currentPage*24,(this.currentPage+1)*24);for(const r in i){let t=i[r];t.avatarId?t.src="http://sso.jingantech.com/identity-admin/v8/v1/sdk/files/"+t.avatarId:t.src="http://sso.jingantech.com/common/images/appLibrary/"+t.appCode+".png"}this.busy=!1,e===!0?this.myapps=i:this.myapps=this.myapps.concat(i)},loadMore(){clearInterval(this.interval),this.currentPage<this.totalPages-1&&(this.currentPage++,this.homeActiveName=="all"?this.resourceMethod(!1,void 0):this.homeActiveName=="culture"?this.institutionalFramework(!1,this.selectId):this.institutionalFramework(!1,this.homeActiveName))},sdploadMore(){clearInterval(this.interval),this.currentSize<this.totalPages-1&&(this.currentSize++,this.loadAdmin())},searchApps(){this.busy||(clearInterval(this.interval),this.homeActiveName=="all"?(this.loadApps(!0,!1,0,void 0),this.selectId=""):this.homeActiveName=="culture"?this.institutionalFramework(!0,this.selectId):this.institutionalFramework(!0,this.homeActiveName))},clearApps(){this.busy||(this.keyword="",this.homeActiveName=="all"?(this.loadApps(!0,!1,0,void 0),this.selectId=""):this.homeActiveName=="culture"?this.institutionalFramework(!0,this.selectId):this.institutionalFramework(!0,this.homeActiveName))},invokeMfaAuth(){this.lockup||m("plugin:sdp|pop_mfa_event").then(e=>{if(e==null)return;this.lockup=!0,this.appName="",this.authSource="INVOKE",this.target_resource=e;let s={username:this.currentUser.username,deviceId:window.DEVICE_ID,target:this.target_resource};ze(ge(s),this.cancelAuth).then(i=>{this.getStrategyInfoAfter(i)})}).catch(e=>{(!e||e.code!=204)&&setTimeout(this.invokeMfaAuth,1e3)})},visit(e){if(!this.lockup){if(y("log",{level:"Debug",message:"single sign on"}),this.appId=e.id,this.appData=e,this.appName=e.name,this.clientApiKey=e.clientApiKey,this.loading=!0,this.lockup=!0,this.appType=e.type,this.authSource="",this.currentCode="",e.clientType==="MOBILE"){this.$message({message:"请打开移动客户端访问应用",type:"warning"}),this.lockup=!1,this.loading=!1;return}if(e.type==="NGIAM_ADMIN"){_e().then(s=>{Ce("identity-admin").then(i=>{i=q("token",s.data,i.data+"/dashboard/index.html"),i=q("tenant",e.tenant,i),R(i)})}),this.lockup=!1,this.loading=!1;return}if(!e.ssoEnabled){e.accessAddress?R(e.accessAddress):this.alert.error("跳转失败，请联系管理员配置该应用的访问地址"),this.lockup=!1,this.loading=!1;return}this.loadAccounts()}},logout(){this.$confirm("是否退出登录?","退出登录",{confirmButtonText:"确定",cancelButtonText:"取消",center:!0,closeOnClickModal:!1,closeOnPressEscape:!1}).then(()=>{this.loading=!0,m("plugin:sdp|logout").then(()=>{this.stopPolling=!1,this.$store.commit("updateIsLogin",!1),window.processMfaAuthState=!1,this.$router.push({path:"/login",query:{date:new Date().getTime()}})}).finally(()=>{this.loading=!1})}).catch(()=>{this.loading=!1})},loadAccounts(){Ae(this.appId).then(e=>{if(this.accounts=e.data,!this.accounts.length)return this.lockup=!1,this.loading=!1,this.alert.error("登录失败，没有绑定账号");this.accounts.length===1?(this.selectedAccount=this.accounts[0],this.getStrategiesInfo()):(window.processMfaAuthState=this.selectAccountDialog=!0,this.loading=!1)}).catch(()=>{this.lockup=!1,this.loading=!1})},selectAccount(e){this.selectAccountDialog=!1,this.loading=!0,this.selectedAccount=e,this.getStrategiesInfo()},cancelSelectAccount(){this.authResultId="",this.participantGroupId="",this.participantKeyword="",this.mfaAuthDialog=!1,window.processMfaAuthState=this.selectAccountDialog=!1,this.target_resource="",this.appName="",this.loading=!1,this.invokeMfaAuth(),this.lockup=!1},mfaDetail(){this.loadAccounts()},getStrategiesInfo(){ke(this.clientApiKey,this.selectedAccount.accountName).then(e=>{this.getStrategyInfoAfter(e)}).catch(e=>{e&&e.messageKey==="MFA.CHECK.DENY"&&this.$message.error(e.errorMsg),this.appName="",this.authSource==="INVOKE"&&m("plugin:sdp|cancel_auth",{ipAddr:this.target_resource}),this.lockup=!1,this.loading=!1})},getStrategyInfoAfter(e){if(this.authResultId=e.data.id,!e||!e.data)this.alert.error("系统错误"),window.processMfaAuthState=this.loading=!1;else if(e.code!="SUCCESS")this.alert.error("系统错误"),window.processMfaAuthState=this.loading=!1;else if(!e.data.enabled||e.data.auth)this.authSource==="INVOKE"?this.CompleteAuth(!0):this.callonApp();else switch(e.data.decision){case"PERMIT":this.alert.error(e.data),window.processMfaAuthState=this.loading=!1;break;case"DENY":this.$alert('<div class="NoActionMsg">您没有权限访问此资源<br/>请联系系统管理员</div>',"提示",{dangerouslyUseHTMLString:!0,showConfirmButton:!1}),window.processMfaAuthState=this.loading=!1;break;case"MFA":this.participantGroupId=e.data.participantGroups[0].id,this.participantKeyword=e.data.participantGroups[0].participants[0].participant,this.mfaTypes=e.data.participantGroups[0].participants[0].mfaMethods,this.mfaTypes.length>1?(this.currentCode="",window.processMfaAuthState=this.mfaAuthDialog=!0):(this.currentName=this.mfaTypes[0].method.name,this.currentCode=this.mfaTypes[0].method.code,this.mfaAuthDialog=!1,this.mfaAuthDetailDialog=!0),this.loading=!1;break}},convertToNestedArray(e){for(var s=[],i=[],r=0;r<e.length;r++)i.push(e[r]),i.length===6&&(s.push(i),i=[]);return i.length>0&&s.push(i),s},selectMfaType(e,s){e=="facial_recognition"?m("plugin:bio|is_support_facial_recognition").then(i=>{if(!i){this.alert.error("检测到设备不支持人脸识别，请换其他方式认证");return}this.currentName=s,this.currentCode=e,this.mfaAuthDialog=!1,this.mfaAuthDetailDialog=!0}):(this.currentName=s,this.currentCode=e,this.mfaAuthDialog=!1,this.mfaAuthDetailDialog=!0)},CompleteAuth(e){this.authSource==="INVOKE"?m("plugin:sdp|auth_complete",{authResultId:this.authResultId,ipAddr:this.target_resource}).then(s=>{if(e||this.alert.success("认证成功"),window.processMfaAuthState=this.mfaAuthDetailDialog=!1,this.authSource="",this.lockup=!1,s){this.invokeMfaAuth();return}}):Me(this.authResultId).then(s=>{if(s.data.auth)e||this.alert.success("认证成功"),window.processMfaAuthState=this.mfaAuthDetailDialog=!1,this.authSource="",this.lockup=!1,this.callonApp();else for(var i=s.data.participantGroups[0].participants[0].mfaMethods,r=0,t=i.length;r<t;r++){var a=i[r];if(!a.auth){this.currentName=a.method.name,this.currentCode=a.method.code;break}}}),this.target_resource=null},callonApp(){Ie(this.appId,this.selectedAccount.id,this.authResultId).then(e=>{switch(this.appName=null,e.data.ssoType||e.data.SSOType){case"CAS_CLIENT":if(this.appType==="NGIAM_ADMIN")var i=e.data.content.service+"?ticket="+e.data.content.st+"&tenant="+this.tenant.code;else var i=e.data.content.service+"?ticket="+e.data.content.st;R(i).catch(t=>{y("log",{level:"Error",message:"Open url: "+JSON.stringify(t)})}),window.processMfaAuthState=this.loading=!1,this.invokeMfaAuth();return;case"CAS_WINDOWS":var i=e.data.content.agreementName,r=e.data.content.st;i+="://"+r,R(i).catch(t=>{y("log",{level:"Error",message:"Open url: "+JSON.stringify(t)})}),window.processMfaAuthState=this.loading=!1,this.invokeMfaAuth();return}m("plugin:iam|sso_result",{result:JSON.stringify(e)}).then(()=>{window.processMfaAuthState=this.loading=!1,this.invokeMfaAuth()}).catch(()=>{window.processMfaAuthState=this.loading=!1,this.alert.error("网络连接异常, 请稍后再试")})}).catch(e=>{window.processMfaAuthState=this.loading=!1}).finally(()=>{this.lockup=!1})},cancelAuth(){this.authResultId="",this.participantGroupId="",this.participantKeyword="",this.mfaAuthDialog=!1,window.processMfaAuthState=this.mfaAuthDetailDialog=!1,this.authSource="",this.appName="",this.$refs.pollMfaMethod&&this.$refs.pollMfaMethod.cancel(),this.authSource==="INVOKE"?(m("plugin:sdp|cancel_auth",{ipAddr:this.target_resource}).then(e=>{e&&this.invokeMfaAuth()}).catch(e=>{y("log",{level:"Error",message:JSON.stringify(e)})}),this.target_resource=""):(this.target_resource="",this.invokeMfaAuth())},goSetting(){this.$router.push({path:"/setting",query:{date:new Date().getTime()}})},receiveRequestId(e){this.requestId=e},closeBox(){window.processMfaAuthState=!1,this.lockup=!1,this.authSource==="INVOKE"?m("plugin:sdp|cancel_auth",{ipAddr:this.target_resource}).then(()=>{this.invokeMfaAuth()}):this.invokeMfaAuth(),this.$refs.pollMfaMethod&&this.$refs.pollMfaMethod.cancel(),this.mfaAuthDetailDialog=!1,this.mfaAuthDialog=!1,this.appName="",this.target_resource="",this.currentCode=""},coreBox(){window.processMfaAuthState=!1,this.authSource==="INVOKE"?m("plugin:sdp|cancel_auth",{ipAddr:this.target_resource}).then(()=>{this.invokeMfaAuth()}):this.invokeMfaAuth(),this.$refs.pollMfaMethod&&this.$refs.pollMfaMethod.cancel(),this.mfaAuthDetailDialog=!1,this.target_resource=""},handleClick(e,s){this.keyword="",e.props.name=="all"?(this.pitchId="",this.loadApps(!0,!1,0,void 0)):e.props.name!="all"&&e.props.name!="culture"&&(this.pitchId=e.props.name,this.institutionalFramework(!0,e.props.name)),e.props.name!="culture"&&(this.selectId="")},calculateTextWidth(e){const s=document.createElement("span");s.textContent=e,s.style.visibility="hidden",s.style.whiteSpace="nowrap",document.body.appendChild(s);const i=s.getBoundingClientRect().width;return document.body.removeChild(s),Math.floor(i)+20},institutionalFramework(e,s){this.busy=!0,e===!0&&(this.currentPage=0,this.myapps=[]),ye(this.keyword,s,this.currentPage,24).then(i=>{if(!i){this.busy=!1;return}for(const r in i.data.content){let t=i.data.content[r];t.avatarId?t.src="http://sso.jingantech.com/identity-admin/v8/v1/sdk/files/"+t.avatarId:t.src="http://sso.jingantech.com/common/images/appLibrary/"+t.appCode+".png"}this.busy=!1,e===!0?this.myapps=i.data.content:this.myapps=this.myapps.concat(i.data.content),this.totalPages=i.data.totalPages}).catch(i=>{console.log("err: ",i),this.busy=!1})},handleCommand(e){this.homeActiveName="culture",this.selectId=e,this.institutionalFramework(!0,e)},showHighlightedImage(e){this.currentCode=e}}},g=e=>(Se("data-v-ef934711"),e=e(),Fe(),e),Xe={class:"app-home","element-loading-text":"加载中...","element-loading-background":"rgba(0, 0, 0, 0.5)"},$e=g(()=>c("div",{style:{height:"53px","background-color":"#fff"}},null,-1)),et={class:"home-body"},tt={class:"home_header"},at=g(()=>c("h2",null,"我的应用",-1)),st={class:"app-search"},it={class:"home_nav",ref:"textContainer"},nt={class:"more-link"},ot={class:"dropdownClass"},lt={class:"is_active"},ct={class:"home_footer infinite-scroll","nfinite-scroll-disabled":"busy","infinite-scroll-immediate":"false"},rt=["onClick"],ut={class:"singleApp_logo"},ht={width:"36",height:"36",alt:""},dt={class:"singleApp_title"},pt={key:0,class:"help-text"},ft=g(()=>c("img",{src:Qe,alt:""},null,-1)),mt=g(()=>c("span",null,"暂无内容",-1)),gt=[ft,mt],_t={key:1,class:"help-text"},Ct=g(()=>c("img",{width:"150",height:"150",src:je,alt:""},null,-1)),At=g(()=>c("span",null,"加载中...",-1)),kt=[Ct,At],Mt=g(()=>c("div",{style:{height:"30px","background-color":"#fff"}},null,-1)),It={class:"account-model"},yt=["onClick"],vt={style:{padding:"10px 30px"}},wt={key:0,class:"mfa-model-text"},bt={style:{color:"#f9780c"}},St={key:1,class:"mfa-model-text"},Ft={style:{color:"#f9780c"}},Rt={style:{display:"flex","flex-wrap":"wrap"}},Nt=["onMouseenter","src","onClick"],Tt=["title"],Et={class:"auth-model"},qt=g(()=>c("div",{class:"auth-tip"},[k(" 请打开"),c("span",{class:"mfa-text"},"【安全令APP】"),k("进行扫码认证 ")],-1));function Dt(e,s,i,r,t,a){const _=l("el-alert"),M=l("el-input"),I=l("CircleClose"),C=l("el-icon"),K=l("Search"),N=l("el-tab-pane"),O=l("arrow-down"),L=l("Check"),P=l("el-dropdown-item"),x=l("el-dropdown-menu"),V=l("el-dropdown"),G=l("el-tabs"),T=l("el-dialog"),U=l("el-scrollbar"),B=l("FACE"),H=l("FACIAL"),W=l("AUTHCODE"),Y=l("SMSOTP"),J=l("FINGERPRINT"),Q=l("VOICE"),j=l("MOBILECLICK"),z=l("NATIVEPASS"),Z=l("EMAILOTP"),X=l("QRCODE"),$=l("TFC200"),ee=l("ANSHUA5"),te=l("ESCUOTP1"),ae=l("WECHATOTP"),se=l("ETZ203"),ie=l("UKEY"),ne=l("UKEYWQ"),oe=E("real-img"),le=E("infinite-scroll"),ce=E("loading");return w((o(),f("div",Xe,[w(d(_,{class:"network_alert",title:r.connectState.title,type:"error",center:"",closable:!1,"show-icon":""},null,8,["title"]),[[D,r.connectState.state]]),$e,c("div",et,[c("div",tt,[at,c("div",st,[d(M,{modelValue:t.keyword,"onUpdate:modelValue":s[0]||(s[0]=n=>t.keyword=n),class:"app-search-input",placeholder:"请输入应用名",onKeyup:ve(a.searchApps,["enter","native"])},null,8,["modelValue","onKeyup"]),t.keyword?(o(),f("div",{key:0,class:"app-search-clear",onClick:s[1]||(s[1]=(...n)=>a.clearApps&&a.clearApps(...n))},[d(C,null,{default:p(()=>[d(I)]),_:1})])):u("",!0),d(C,{class:"app-search-btn",onClick:a.searchApps},{default:p(()=>[d(K)]),_:1},8,["onClick"])])]),c("div",it,[d(G,{modelValue:t.homeActiveName,"onUpdate:modelValue":s[3]||(s[3]=n=>t.homeActiveName=n),onTabClick:a.handleClick},{default:p(()=>[d(N,{label:"全部应用",name:"all"}),(o(!0),f(b,null,S(t.institutionalList,n=>(o(),h(N,{key:n.id,name:n.id},{label:p(()=>[c("span",null,A(n.name),1)]),_:2},1032,["name"]))),128)),t.sliceList.length?(o(),h(N,{key:0,name:"culture",disabled:""},{label:p(()=>[d(V,{"hide-on-click":!1,onCommand:s[2]||(s[2]=n=>a.handleCommand(n,e.row))},{dropdown:p(()=>[d(x,null,{default:p(()=>[(o(!0),f(b,null,S(t.sliceList,n=>(o(),h(P,{key:n.id,command:n.id,class:be({selected:t.selectId==n.id})},{default:p(()=>[c("span",ot,A(n.name)+" ",1),c("span",lt,[w(d(C,null,{default:p(()=>[d(L)]),_:2},1536),[[D,t.selectId==n.id]])])]),_:2},1032,["command","class"]))),128))]),_:1})]),default:p(()=>[c("span",nt,[k(" 更多"),d(C,{class:"el-icon--right"},{default:p(()=>[d(O)]),_:1})])]),_:1})]),_:1})):u("",!0)]),_:1},8,["modelValue","onTabClick"])],512),w((o(),f("div",ct,[(o(!0),f(b,null,S(t.myapps,n=>(o(),f("div",{class:"singleApp",key:n.id,onClick:v=>a.visit(n)},[c("span",ut,[w(c("img",ht,null,512),[[oe,{value:n.src,defaultValue:t.defaultAppLogo}]])]),c("span",dt,A(n.name),1)],8,rt))),128)),t.myapps.length===0&&!t.busy?(o(),f("div",pt,gt)):u("",!0),t.busy?(o(),f("div",_t,kt)):u("",!0)])),[[le,a.loadMore]])]),Mt,d(T,{modelValue:t.selectAccountDialog,"onUpdate:modelValue":s[4]||(s[4]=n=>t.selectAccountDialog=n),title:"选择访问账号",width:"35%",style:{"padding-bottom":"16px"},"before-close":a.cancelSelectAccount,"destroy-on-close":"",center:"","close-on-click-modal":!1,"close-on-press-escape":!1},{default:p(()=>[c("div",It,[(o(!0),f(b,null,S(t.accounts,n=>(o(),f("div",{key:n.id,class:"account-item",onClick:v=>a.selectAccount(n)},A(n.accountName),9,yt))),128))])]),_:1},8,["modelValue","before-close"]),d(T,{modelValue:t.mfaAuthDialog,"onUpdate:modelValue":s[6]||(s[6]=n=>t.mfaAuthDialog=n),title:t.title,width:"50%","destroy-on-close":"","before-close":a.closeBox,center:"","close-on-click-modal":!1,"close-on-press-escape":!1},{default:p(()=>[d(U,null,{default:p(()=>[c("div",vt,[t.appName?(o(),f("div",wt,[k(" 正在请求登录 "),c("span",bt,A(t.appName),1),k(", 请验证... ")])):u("",!0),t.target_resource?(o(),f("div",St,[k(" 正在请求访问 "),c("span",Ft,A(t.target_resource),1),k("资源, 请验证... ")])):u("",!0),c("div",Rt,[(o(!0),f(b,null,S(t.mfaTypes,n=>(o(),f("div",{key:n.method.code,class:"mfaTypesCode"},[c("img",{width:"46",height:"46",alt:"",onMouseenter:v=>a.showHighlightedImage(n.method.code),onMouseleave:s[5]||(s[5]=(...v)=>e.showNormalImage&&e.showNormalImage(...v)),src:t.currentCode!=n.method.code?"assets/images/defaultlogin/"+n.method.code+".png":"/assets/images/mfalogin/"+n.method.code+".png",onClick:v=>a.selectMfaType(n.method.code,n.method.name)},null,40,Nt),c("span",{title:n.method.name},A(n.method.name),9,Tt)]))),128))])])]),_:1})]),_:1},8,["modelValue","title","before-close"]),d(T,{modelValue:t.mfaAuthDetailDialog,"onUpdate:modelValue":s[7]||(s[7]=n=>t.mfaAuthDetailDialog=n),width:"50%",title:t.currentName+"认证","before-close":a.closeBox,"destroy-on-close":"",center:"","close-on-click-modal":!1,"close-on-press-escape":!1},{default:p(()=>[c("div",Et,[t.currentCode==="face"?(o(),h(B,{key:0,ref:"pollMfaMethod","user-id":t.participantKeyword,"participant-group-id":t.participantGroupId,onMfaCallbackFunc:a.CompleteAuth,onCancelMfaCallbackFunc:a.cancelAuth,onRequestId:a.receiveRequestId},null,8,["user-id","participant-group-id","onMfaCallbackFunc","onCancelMfaCallbackFunc","onRequestId"])):u("",!0),t.currentCode==="facial_recognition"?(o(),h(H,{key:1,ref:"pollMfaMethod","user-id":t.participantKeyword,"participant-group-id":t.participantGroupId,onMfaCallbackFunc:a.CompleteAuth,onCancelMfaCallbackFunc:a.cancelAuth,onRequestId:a.receiveRequestId,tenant:t.tenantCode},null,8,["user-id","participant-group-id","onMfaCallbackFunc","onCancelMfaCallbackFunc","onRequestId","tenant"])):u("",!0),t.currentCode==="auth_code"?(o(),h(W,{key:2,ref:"manualMfaMethod","user-id":t.participantKeyword,"participant-group-id":t.participantGroupId,onMfaCallbackFunc:a.CompleteAuth,onCancelMfaCallbackFunc:a.cancelAuth,onRequestId:a.receiveRequestId},null,8,["user-id","participant-group-id","onMfaCallbackFunc","onCancelMfaCallbackFunc","onRequestId"])):u("",!0),t.currentCode==="sms_otp"?(o(),h(Y,{key:3,ref:"manualMfaMethod","user-id":t.participantKeyword,"participant-group-id":t.participantGroupId,onMfaCallbackFunc:a.CompleteAuth,onCancelMfaCallbackFunc:a.cancelAuth,onRequestId:a.receiveRequestId},null,8,["user-id","participant-group-id","onMfaCallbackFunc","onCancelMfaCallbackFunc","onRequestId"])):u("",!0),t.currentCode==="fingerprint"?(o(),h(J,{key:4,ref:"pollMfaMethod","user-id":t.participantKeyword,"participant-group-id":t.participantGroupId,onMfaCallbackFunc:a.CompleteAuth,onRequestId:a.receiveRequestId},null,8,["user-id","participant-group-id","onMfaCallbackFunc","onRequestId"])):u("",!0),t.currentCode==="voice"?(o(),h(Q,{key:5,ref:"pollMfaMethod","user-id":t.participantKeyword,"participant-group-id":t.participantGroupId,onMfaCallbackFunc:a.CompleteAuth,onCancelMfaCallbackFunc:a.cancelAuth,onRequestId:a.receiveRequestId},null,8,["user-id","participant-group-id","onMfaCallbackFunc","onCancelMfaCallbackFunc","onRequestId"])):u("",!0),t.currentCode==="mobileclick"?(o(),h(j,{key:6,ref:"pollMfaMethod","user-id":t.participantKeyword,"participant-group-id":t.participantGroupId,onMfaCallbackFunc:a.CompleteAuth,onCancelMfaCallbackFunc:a.cancelAuth,onRequestId:a.receiveRequestId},null,8,["user-id","participant-group-id","onMfaCallbackFunc","onCancelMfaCallbackFunc","onRequestId"])):u("",!0),t.currentCode==="nativepass"?(o(),h(z,{key:7,ref:"pollMfaMethod","user-id":t.participantKeyword,"participant-group-id":t.participantGroupId,onMfaCallbackFunc:a.CompleteAuth,onCancelMfaCallbackFunc:a.cancelAuth,onRequestId:a.receiveRequestId},null,8,["user-id","participant-group-id","onMfaCallbackFunc","onCancelMfaCallbackFunc","onRequestId"])):u("",!0),t.currentCode==="email_otp"?(o(),h(Z,{key:8,ref:"manualMfaMethod","user-id":t.participantKeyword,"participant-group-id":t.participantGroupId,onMfaCallbackFunc:a.CompleteAuth,onCancelMfaCallbackFunc:a.cancelAuth,onRequestId:a.receiveRequestId},null,8,["user-id","participant-group-id","onMfaCallbackFunc","onCancelMfaCallbackFunc","onRequestId"])):u("",!0),t.currentCode==="qrcode"?(o(),h(X,{key:9,ref:"pollMfaMethod","user-id":t.participantKeyword,"participant-group-id":t.participantGroupId,onMfaCallbackFunc:a.CompleteAuth,onCancelMfaCallbackFunc:a.cancelAuth,onRequestId:a.receiveRequestId},{default:p(()=>[qt]),_:1},8,["user-id","participant-group-id","onMfaCallbackFunc","onCancelMfaCallbackFunc","onRequestId"])):u("",!0),t.currentCode==="ft_c200"?(o(),h($,{key:10,ref:"manualMfaMethod","user-id":t.participantKeyword,"participant-group-id":t.participantGroupId,onMfaCallbackFunc:a.CompleteAuth,onCancelMfaCallbackFunc:a.cancelAuth,onRequestId:a.receiveRequestId},null,8,["user-id","participant-group-id","onMfaCallbackFunc","onCancelMfaCallbackFunc","onRequestId"])):u("",!0),t.currentCode==="anshu_a5"?(o(),h(ee,{key:11,ref:"manualMfaMethod","user-id":t.participantKeyword,"participant-group-id":t.participantGroupId,onMfaCallbackFunc:a.CompleteAuth,onCancelMfaCallbackFunc:a.cancelAuth,onRequestId:a.receiveRequestId},null,8,["user-id","participant-group-id","onMfaCallbackFunc","onCancelMfaCallbackFunc","onRequestId"])):u("",!0),t.currentCode==="escuotp1"?(o(),h(te,{key:12,ref:"manualMfaMethod","user-id":t.participantKeyword,"participant-group-id":t.participantGroupId,onMfaCallbackFunc:a.CompleteAuth,onCancelMfaCallbackFunc:a.cancelAuth,onRequestId:a.receiveRequestId},null,8,["user-id","participant-group-id","onMfaCallbackFunc","onCancelMfaCallbackFunc","onRequestId"])):u("",!0),t.currentCode==="wechat_otp"?(o(),h(ae,{key:13,ref:"manualMfaMethod","user-id":t.participantKeyword,"participant-group-id":t.participantGroupId,onMfaCallbackFunc:a.CompleteAuth,onCancelMfaCallbackFunc:a.cancelAuth,onRequestId:a.receiveRequestId},null,8,["user-id","participant-group-id","onMfaCallbackFunc","onCancelMfaCallbackFunc","onRequestId"])):u("",!0),t.currentCode==="et_z203"?(o(),h(se,{key:14,ref:"manualMfaMethod","user-id":t.participantKeyword,"participant-group-id":t.participantGroupId,onMfaCallbackFunc:a.CompleteAuth,onCancelMfaCallbackFunc:a.cancelAuth,onRequestId:a.receiveRequestId},null,8,["user-id","participant-group-id","onMfaCallbackFunc","onCancelMfaCallbackFunc","onRequestId"])):u("",!0),t.currentCode==="ukey_bjca"?(o(),h(ie,{key:15,ref:"pollMfaMethod","user-id":t.participantKeyword,"participant-group-id":t.participantGroupId,onMfaCallbackFunc:a.CompleteAuth,onCancelMfaCallbackFunc:a.cancelAuth,onRequestId:a.receiveRequestId},null,8,["user-id","participant-group-id","onMfaCallbackFunc","onCancelMfaCallbackFunc","onRequestId"])):u("",!0),t.currentCode==="ukey_wq"?(o(),h(ne,{key:16,ref:"pollMfaMethod","user-id":t.participantKeyword,"participant-group-id":t.participantGroupId,onMfaCallbackFunc:a.CompleteAuth,onCancelMfaCallbackFunc:a.cancelAuth,onRequestId:a.receiveRequestId},null,8,["user-id","participant-group-id","onMfaCallbackFunc","onCancelMfaCallbackFunc","onRequestId"])):u("",!0)])]),_:1},8,["modelValue","title","before-close"])])),[[ce,t.loading,void 0,{fullscreen:!0,lock:!0}]])}const Lt=de(Ze,[["render",Dt],["__scopeId","data-v-ef934711"]]);export{Lt as default};

<template>
  <div
    v-loading.fullscreen.lock="loading"
    class="app-content"
    element-loading-text="连接中..."
    element-loading-background="rgba(0, 0, 0, 0.5)"
  >
    <!-- <headerBar /> -->
    <el-form
      ref="ruleForm"
      :model="ruleForm"
      :rules="rules"
      label-width="110px"
      class="changePwd-Form"
    >
      <el-form-item
        class="form-item"
        label="旧密码 :"
        prop="oldPassword"
        :error="errorMsg"
      >
        <el-input
          v-model="ruleForm.oldPassword"
          class="form-input"
          type="password"
          placeholder="请输入旧密码"
          autocomplete="off"
          clearable
          show-password
        />
      </el-form-item>
      <el-form-item
        class="form-item"
        label="设置新密码 :"
        prop="newPassword"
        :error="newerrorMsg"
      >
        <el-input
          v-model="ruleForm.newPassword"
          class="form-input"
          placeholder="请输入新密码"
          type="password"
          autocomplete="off"
          clearable
          show-password
        />
      </el-form-item>
      <el-form-item
        class="form-item"
        label="新密码确认 :"
        prop="newPasswordRepeat"
      >
        <el-input
          v-model="ruleForm.newPasswordRepeat"
          class="form-input"
          type="password"
          placeholder="请再次输入新密码"
          autocomplete="off"
          clearable
          show-password
        />
      </el-form-item>
      <div class="tipStarthome">
        <!-- {{ tip }} -->
        <strong style="font-size: 15px" class="f_w_no">密码需要满足以下条件：</strong>
        <p v-for="item in tip" :key="item">{{ item }}</p>
      </div>
      <el-form-item>
        <el-button type="primary" class="form-btn" @click="submitForm" round>
          确定
        </el-button>
      </el-form-item>
    </el-form>
    <el-dialog
      v-model="showChangePwdSeccess"
      title="修改密码"
      width="30%"
      center
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :before-close="closeChangePwdSuccessDialog"
    >
      <p style="padding: 30px 0; text-align: center">密码重置成功</p>
      <template #footer>
        <span class="dialog-footer">
          <el-button round type="primary" @click="closeChangePwdSuccessDialog">
            确定
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { pwdPolicy, change_password } from "@/api/service";
import { listen } from "@tauri-apps/api/event";
import headerBar from "@/components/Header.vue";
import { invoke } from "@tauri-apps/api/tauri";
import { computed } from "vue";
import { useStore } from "vuex";

export default {
  name: "ChangePwd",
  components: {
    headerBar,
  },
  props: ["username"],
  setup() {
    const store = useStore();
    return {
      loading: computed(() => store.getters.changePwdLoading),
      ctl: computed(() => store.getters.ctl),
      tenant: computed(() => store.getters.tenant),
    };
  },
  data() {
    var validatePass2 = (rule, value, callback) => {
      if (value === "") {
        callback(new Error("请再次输入新密码"));
      } else if (value !== this.ruleForm.newPassword) {
        callback(new Error("两次输入密码不一致!"));
      } else {
        callback();
      }
    };
    return {
      ruleForm: {
        oldPassword: "",
        newPassword: "",
        newPasswordRepeat: "",
      },
      verifyCode: "",
      tip: "",
      errorMsg: "",
      newerrorMsg: "",
      showChangePwdSeccess: false,
      rules: {
        oldPassword: [
          { required: true, message: "请输入旧密码", trigger: "blur" },
          // { min: 6, max: 20, message: '长度在 6 到 20 位', trigger: 'blur' }
        ],
        newPassword: [
          { required: true, message: "请输入新密码", trigger: "blur" },
          // { min: 6, max: 20, message: '长度在 6 到 20 位', trigger: 'blur' }
        ],
        newPasswordRepeat: [
          { required: true, validator: validatePass2, trigger: "blur" },
          // { min: 6, max: 20, message: '长度在 6 到 20 位', trigger: 'blur' }
        ],
      },
      changePwdListener: null,
    };
  },
  created() {
    this.pwdPolicy();
  },
  methods: {
    submitForm() {
      this.errorMsg = "";
      this.newerrorMsg = "";
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          this.$store.commit("updateChangePwdLoading", true);
          const data = {
            username: this.username,
            old: this.ruleForm.oldPassword,
            password: this.ruleForm.newPassword,
          }
          change_password(data).then(res => {
            // this.alert.success("修改密码成功");
            this.showChangePwdSeccess = true;
          }).finally(()=>{
            this.$store.commit("updateChangePwdLoading", false);
          })
        } else {
          return false;
        }
      });
    },
    // 获取密码策略
    pwdPolicy() {
      pwdPolicy({
        userKeyword: this.username,
        userTypes: ["03"],
      }).then((res) => {
        this.tip = res.data.tipKey.split("\n");
      });
    },
    // 密码修改成功, 回调处理
    closeChangePwdSuccessDialog() {
      invoke("plugin:sdp|logout");
      // 清空保存的密码
      let users = {};
      users[CONFIG.selected_tenant.code] = {
        password: null,
        remember_me: false,
        auto_connect: false,
      };
      invoke("patch", {
        value: {
          users: users,
        },
      })
        .then((config) => {
          window.CONFIG = config;
        })
        .finally(() => {
          this.$router.push({
            path: "/login",
            query: { date: new Date().getTime() },
          });
        });
    },
  },
};
</script>

<style lang="less" scoped>
.changePwd-Form {
  padding: 10px 20px;

  :deep(.el-input__inner) {
    height: 35px;
    line-height: 40px;
  }

  .form-btn {
    display: block;
    width: 68px;
    height: 34px;
    line-height: 34px;
    padding: 0;
    margin-top: 10px;
    background-image: linear-gradient(to right, #fbaa66, #f9780c);
  }

  .tipStart {
    margin-left: 100px;
    padding: 12px;
    font-size: 14px;
    font-weight: normal;
    color: #53565d;
    min-height: 130px;
    max-height: 270px;
    overflow-y: auto;
  }

  .tipStarthome {
    margin-left: 100px;
    padding: 12px;
    font-size: 14px;
    font-weight: normal;
    color: #53565d;
    min-height: 130px;
    max-height: 200px;
    overflow-y: auto;
  }

  // .el-form-item{
  //     margin-bottom: 25px;
  // }
  :deep(.el-form-item__label) {
    height: 55px;
    align-items: center;
    color: #25282f;
  }

  :deep(.el-form-item__content) {
    display: block;
    line-height: 55px;
  }

  .el-button:hover {
    color: #fff;
  }
}
</style>

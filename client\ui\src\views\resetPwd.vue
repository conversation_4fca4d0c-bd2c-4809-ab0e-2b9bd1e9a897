<template>
  <div>
    <el-dialog
      :model-value="show"
      title="修改密码"
      width="80%"
      center
      :before-close="closeDialog"
      destroy-on-close
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <div
        v-loading.fullscreen.lock="loading"
        element-loading-text="加载中..."
        element-loading-background="rgba(0, 0, 0, 0.5)"
      >
        <el-form
          ref="ruleForm"
          :model="ruleForm"
          :rules="rules"
          label-width="110px"
          class="changePwd-Form"
        >
          <el-form-item
            v-if="!verifyCode"
            class="form-item"
            label="旧密码 :"
            prop="oldPassword"
            :error="errorMsg"
          >
            <el-input
              v-model="ruleForm.oldPassword"
              class="form-input"
              type="password"
              placeholder="请输入旧密码"
              autocomplete="off"
              clearable
              show-password
            />
          </el-form-item>
          <el-form-item
            class="form-item"
            label="设置新密码 :"
            prop="newPassword"
            :error="newerrorMsg"
          >
            <el-input
              v-model="ruleForm.newPassword"
              class="form-input"
              placeholder="请输入新密码"
              type="password"
              autocomplete="off"
              clearable
              show-password
            />
          </el-form-item>
          <el-form-item
            class="form-item"
            label="新密码确认 :"
            prop="newPasswordRepeat"
          >
            <el-input
              v-model="ruleForm.newPasswordRepeat"
              class="form-input"
              type="password"
              placeholder="请再次输入新密码"
              autocomplete="off"
              clearable
              show-password
            />
          </el-form-item>
          <div
            class="tipStart"
            :style="verifyCode ? 'height: 128px;' : 'height: 55px;'"
          >
            <!-- {{ tip }} -->
            <strong style="font-size: 15px" class="f_w_no">密码需要满足以下条件：</strong>
            <p v-for="item in tip" :key="item">{{ item }}</p>
          </div>
        </el-form>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button round @click="closeDialog">取消</el-button>
          <el-button
            ref="resetPwdBtn"
            v-if="verifyCode"
            round
            type="primary"
            @click="VCodeSubmitForm"
          >
            确认
          </el-button>
          <el-button
            ref="resetPwdBtn"
            v-else
            round
            type="primary"
            @click="submitForm"
          >
            确认
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { pwdPolicy, resetPassword, change_password } from "@/api/service";
import { invoke } from "@tauri-apps/api/tauri";
import { listen } from "@tauri-apps/api/event";
import { computed } from "vue";
import { useStore } from "vuex";

export default {
  name: "resetPwd",
  props: {
    show: {
      type: Boolean,
      default: false,
    },
  },
  setup() {
    const store = useStore();
    return {
      loading: computed(() => store.getters.changePwdLoading),
    };
  },
  data() {
    var validatePass2 = (rule, value, callback) => {
      if (value === "") {
        callback(new Error("请再次输入新密码"));
      } else if (value !== this.ruleForm.newPassword) {
        callback(new Error("两次输入密码不一致!"));
      } else {
        callback();
      }
    };
    return {
      ruleForm: {
        username: "",
        oldPassword: "",
        newPassword: "",
        newPasswordRepeat: "",
      },
      secret: '',
      verifyCode: "",
      tip: "",
      newerrorMsg: "",
      errorMsg: "",
      changePwdListener: null,
      rules: {
        oldPassword: [
          { required: true, message: "请输入旧密码", trigger: "blur" },
          // { min: 6, max: 20, message: '长度在 6 到 20 位', trigger: 'blur' }
        ],
        newPassword: [
          { required: true, message: "请输入新密码", trigger: "blur" },
          // { min: 6, max: 20, message: '长度在 6 到 20 位', trigger: 'blur' }
        ],
        newPasswordRepeat: [
          { required: true, validator: validatePass2, trigger: "blur" },
          // { min: 6, max: 20, message: '长度在 6 到 20 位', trigger: 'blur' }
        ],
      },
    };
  },
  created() {},

  methods: {
    async VCodeSubmitForm() {
      this.newerrorMsg = "";
      this.$refs.ruleForm.validate(async (valid) => {
        if (valid) {
          this.$store.commit("updateChangePwdLoading", true);
          resetPassword({
            password: this.ruleForm.newPassword,
            verifyCode: this.verifyCode
          }).then(() => {
              this.$store.commit("updateChangePwdLoading", false);
              // 执行回调函数
              this.$emit("successCallback", "密码重置成功");
              this.errorMsg = "";
              this.newerrorMsg = "";
              this.ruleForm.username = "";
              this.ruleForm.oldPassword = "";
              this.ruleForm.newPassword = "";
              this.ruleForm.newPasswordRepeat = "";
            })
            .catch((err) => {
              this.$store.commit("updateChangePwdLoading", false);
              this.newerrorMsg = err.msg;
            });
        } else {
          return false;
        }
      });
    },
    submitForm() {
      this.errorMsg = "";
      this.newerrorMsg = "";
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          this.$store.commit("updateChangePwdLoading", true);
          const data = {
            username: this.ruleForm.username,
            old: this.ruleForm.oldPassword,
            password: this.ruleForm.newPassword,
          }
          change_password(data).then(() => {
            // 执行回调函数
            this.$emit("successCallback", "密码重置成功");
          }).finally(()=>{
            this.$store.commit("updateChangePwdLoading", false);
          })
        } else {
          return false;
        }
      });
    },
    // 获取密码策略
    pwdPolicy() {
      pwdPolicy({
        userKeyword: this.ruleForm.username,
        userTypes: ["03"],
      }).then((res) => {
        this.tip = res.data.tipKey.split("\n");
      });
    },
    init(res) {
      this.secret = res.secret;
      this.verifyCode = res.verifyCode;
      this.ruleForm.username = res.username;
      this.pwdPolicy();
    },
    closeDialog() {
      if (!this.verifyCode) {
        invoke("plugin:sdp|logout");
      }
      this.$emit("close");
      this.errorMsg = "";
      this.newerrorMsg = "";
      this.ruleForm.username = "";
      this.ruleForm.oldPassword = "";
      this.ruleForm.newPassword = "";
      this.ruleForm.newPasswordRepeat = "";
    },
  },
};
</script>

<style lang="less" scoped>
.changePwd-Form {
  padding: 20px 60px 0;
  overflow-y: auto;

  :deep(.el-input__inner) {
    height: 35px;
    line-height: 40px;
  }

  .form-btn {
    display: block;
    width: 68px;
    height: 34px;
    line-height: 34px;
    padding: 0;
    margin-top: 10px;
    background-image: linear-gradient(to right, #fbaa66, #f9780c);
  }

  .tipStart {
    margin-left: 100px;
    padding: 12px;
    font-size: 14px;
    font-weight: normal;
    color: #53565d;
    // max-height: 128px;
    overflow-y: auto;
  }

  :deep(.el-form-item__label) {
    height: 55px;
    align-items: center;
    color: #25282f;
  }

  :deep(.el-form-item__content) {
    display: block;
    line-height: 55px;
  }

  .el-button:hover {
    color: #fff;
  }
}
</style>

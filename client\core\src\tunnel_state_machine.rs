use std::{
    collections::HashSet,
    net::{IpAddr, Ipv4Addr, Ipv6Addr},
    sync::Arc,
    time::Duration,
};

use futures::{
    channel::{mpsc, oneshot},
    stream, StreamExt,
};
use ipnet::IpNet;
use tokio::sync::broadcast;
use types::{
    net::Connectivity,
    tunnel::{ErrorStateCause, TunnelStateTransition},
};

use crate::{
    dns::DnsMonitor,
    mpsc::Sender,
    offline,
    routing::{self, RouteManagerHandle},
    tunnel::TunnelParameters,
};

use self::{
    connected_state::ConnectedState, connecting_state::ConnectingState,
    disconnected_state::DisconnectedState, error_state::ErrorState,
};

mod connected_state;
mod connecting_state;
mod disconnected_state;
mod error_state;

const TUNNEL_STATE_MACHINE_SHUTDOWN_TIMEOUT: Duration = Duration::from_secs(5);

#[derive(thiserror::Error, Debug)]
pub enum Error {
    /// Unable to spawn offline state monitor
    #[error("Unable to spawn offline state monitor")]
    OfflineMonitorError(#[from] offline::Error),

    /// Failed to initialize the system DNS manager and monitor.
    #[error("Failed to initialize the system DNS manager and monitor")]
    InitDnsMonitorError(#[from] crate::dns::Error),

    /// Failed to initialize the route manager.
    #[error("Failed to initialize the route manager")]
    InitRouteManagerError(#[from] routing::Error),

    /// Failed to send state change event to listener
    #[error("Failed to send state change event to listener")]
    SendStateChange,
}

pub enum ResourceEvent {
    AddBlock(Option<IpNet>),
    DeleteBlock(Option<IpNet>, oneshot::Sender<bool>),
    GetFirstBlock(oneshot::Sender<Option<Option<IpNet>>>),
    AddResources(HashSet<IpNet>),
    DeleteResource(HashSet<IpNet>),
    Clear,
}

pub enum TunnelCommand {
    /// Sent when the connection is established.
    Up {
        ipv4: Option<Ipv4Addr>,
        ipv6: Option<Ipv6Addr>,
    },
    /// Sent when the connection is lost.
    Down,
    /// Resource event.
    Resource(ResourceEvent),
    /// Set DNS servers to use.
    Dns(Option<Vec<IpAddr>>),
    /// Reset DNS to default.
    ResetDns(Option<oneshot::Sender<()>>),
    /// Set system routes.
    Routes((Option<HashSet<IpNet>>, Option<oneshot::Sender<()>>)),
    /// Ask is offline
    AskIsOffline(oneshot::Sender<bool>),
    /// Open tunnel connection.
    Connect,
    /// Close tunnel connection.
    Disconnect(Option<oneshot::Sender<()>>),
    /// Backend channel packets.
    Packet(Vec<u8>),
    /// Is the traceability function available?
    #[cfg(feature = "traceability")]
    TraceabilityFunction(bool),
}

/// Identifiers for various network resources that should be unique to a given instance of a tunnel
/// state machine.
#[cfg(target_os = "linux")]
pub struct LinuxNetworkingIdentifiers {
    /// Firewall mark is used to mark traffic which should be able to bypass the tunnel
    pub fwmark: u32,
    /// The table ID will be used for the routing table that will route all traffic through the
    /// tunnel interface.
    pub table_id: u32,
}

pub async fn spawn(
    tunnel_parameters: TunnelParameters,
    tunnel_up_sender: broadcast::Sender<bool>,
    state_change_listener: impl Sender<TunnelStateTransition> + Send + 'static,
    offline_state_listener: mpsc::UnboundedSender<Connectivity>,
    #[cfg(target_os = "linux")] linux_ids: LinuxNetworkingIdentifiers,
) -> Result<TunnelStateMachineHandle, Error> {
    let (command_tx, command_rx) = mpsc::unbounded();
    let command_tx = Arc::new(command_tx);

    let (shutdown_tx, shutdown_rx) = oneshot::channel();

    #[cfg(target_os = "macos")]
    let weak_command_tx = Arc::downgrade(&command_tx);
    let init_args = TunnelStateMachineInitArgs {
        #[cfg(target_os = "macos")]
        command_tx: weak_command_tx,
        offline_state_tx: offline_state_listener,
        tunnel_parameters,
        commands_rx: command_rx,
        #[cfg(target_os = "android")]
        android_context,
        #[cfg(target_os = "linux")]
        linux_ids,
        tunnel_up_sender,
    };

    let state_machine = TunnelStateMachine::new(init_args).await?;

    tokio::spawn(async move {
        state_machine.run(state_change_listener).await;
        if shutdown_tx.send(()).is_err() {
            log::error!("Can't send shutdown completion to core");
        }
    });

    Ok(TunnelStateMachineHandle {
        command_tx,
        shutdown_rx,
    })
}

/// Arguments for creating a tunnel.
pub struct TunnelArgs {
    /// Callback function called when an event happens.
    pub event_tx: mpsc::Sender<TunnelEvent>,
}

/// Information about a SDP tunnel.
#[derive(Debug, Clone, Eq, PartialEq, Hash)]
pub struct TunnelMetadata {
    /// The name of the device which the tunnel is running on.
    pub interface: String,
}

/// Possible events from the SDP tunnel and the child process managing it.
#[derive(Debug, Clone)]
pub enum TunnelEvent {
    /// Sent when the tunnel is started.
    Created(TunnelMetadata),
    /// Sent when the tunnel goes down, but before destroying the tunnel device.
    Down,
}

type TunnelCommandReceiver = stream::Fuse<mpsc::UnboundedReceiver<TunnelCommand>>;

enum EventResult {
    Command(Option<TunnelCommand>),
    Event(Option<TunnelEvent>),
}

/// Tunnel state machine initialization arguments arguments
struct TunnelStateMachineInitArgs {
    #[cfg(target_os = "macos")]
    command_tx: std::sync::Weak<mpsc::UnboundedSender<TunnelCommand>>,
    offline_state_tx: mpsc::UnboundedSender<Connectivity>,
    tunnel_parameters: TunnelParameters,
    commands_rx: mpsc::UnboundedReceiver<TunnelCommand>,
    #[cfg(target_os = "android")]
    android_context: AndroidContext,
    #[cfg(target_os = "linux")]
    linux_ids: LinuxNetworkingIdentifiers,
    tunnel_up_sender: broadcast::Sender<bool>,
}

struct TunnelStateMachine {
    current_state: Option<TunnelStateWrapper>,
    commands: TunnelCommandReceiver,
    shared_values: SharedTunnelStateValues,
}

impl TunnelStateMachine {
    async fn new(args: TunnelStateMachineInitArgs) -> Result<Self, Error> {
        let route_manager = RouteManagerHandle::spawn(
            #[cfg(target_os = "linux")]
            args.linux_ids.fwmark,
            #[cfg(target_os = "linux")]
            args.linux_ids.table_id,
        )
        .await
        .map_err(Error::InitRouteManagerError)?;

        let dns_monitor = DnsMonitor::new(
            #[cfg(target_os = "linux")]
            route_manager.clone(),
            #[cfg(target_os = "macos")]
            args.command_tx.clone(),
        )
        .await
        .map_err(Error::InitDnsMonitorError)?;

        let (offline_tx, mut offline_rx) = mpsc::unbounded();
        tokio::spawn(async move {
            while let Some(connectivity) = offline_rx.next().await {
                let _ = args.offline_state_tx.unbounded_send(connectivity);
            }
        });

        let offline_monitor = offline::spawn_monitor(
            offline_tx,
            route_manager.clone(),
            #[cfg(target_os = "linux")]
            Some(args.linux_ids.fwmark),
        )
        .await;

        let mut shared_values = SharedTunnelStateValues {
            dns_monitor,
            route_manager,
            offline_monitor,
            dns_servers: None,
            tunnel_parameters: Some(args.tunnel_parameters),
            tunnel_up_sender: args.tunnel_up_sender,
        };

        let (initial_state, _) = DisconnectedState::enter(&mut shared_values, ()).await;

        Ok(TunnelStateMachine {
            current_state: Some(initial_state),
            commands: args.commands_rx.fuse(),
            shared_values,
        })
    }

    async fn run(mut self, change_listener: impl Sender<TunnelStateTransition> + Send + 'static) {
        use EventConsequence::*;

        while let Some(state_wrapper) = self.current_state.take() {
            match state_wrapper
                .handle_event(&mut self.commands, &mut self.shared_values)
                .await
            {
                NewState((state, transition)) => {
                    self.current_state = Some(state);

                    if let Err(error) = change_listener
                        .send(transition)
                        .map_err(|_| Error::SendStateChange)
                    {
                        log::error!("{}", error);
                        break;
                    }
                }
                SameState(state) => {
                    self.current_state = Some(state);
                }
                Finished => (),
            }
        }

        log::debug!("Exiting tunnel state machine loop");
    }
}

/// Values that are common to all tunnel states.
struct SharedTunnelStateValues {
    dns_monitor: DnsMonitor,
    route_manager: RouteManagerHandle,
    offline_monitor: offline::MonitorHandle,

    /// DNS servers to use (overriding default).
    dns_servers: Option<Vec<IpAddr>>,

    tunnel_parameters: Option<TunnelParameters>,
    tunnel_up_sender: broadcast::Sender<bool>,
}

impl SharedTunnelStateValues {
    pub fn set_dns_servers(
        &mut self,
        dns_servers: Option<Vec<IpAddr>>,
    ) -> Result<bool, ErrorStateCause> {
        if self.dns_servers != dns_servers {
            self.dns_servers = dns_servers;
            Ok(true)
        } else {
            Ok(false)
        }
    }

    /// NetworkManager's connectivity check can get hung when DNS requests fail, thus the TSM
    /// should always disable it before applying firewall rules. The connectivity check should be
    /// reset whenever the firewall is cleared.
    // #[cfg(target_os = "linux")]
    // pub fn disable_connectivity_check(&mut self) {
    //     if self.connectivity_check_was_enabled.is_none() {
    //         if let Ok(nm) = talpid_dbus::network_manager::NetworkManager::new() {
    //             self.connectivity_check_was_enabled = nm.disable_connectivity_check();
    //         }
    //     } else {
    //         log::trace!("Daemon already disabled connectivity check");
    //     }
    // }

    /// Reset NetworkManager's connectivity check if it was disabled.
    // #[cfg(target_os = "linux")]
    // pub fn reset_connectivity_check(&mut self) {
    //     if self.connectivity_check_was_enabled.take() == Some(true) {
    //         if let Ok(nm) = talpid_dbus::network_manager::NetworkManager::new() {
    //             nm.enable_connectivity_check();
    //         }
    //     } else {
    //         log::trace!("Connectivity check wasn't disabled by the daemon");
    //     }
    // }

    #[cfg(target_os = "android")]
    pub fn bypass_socket(&mut self, fd: RawFd, tx: oneshot::Sender<()>) {
        if let Err(err) = self.tun_provider.lock().unwrap().bypass(fd) {
            log::error!("Failed to bypass socket {}", err);
        }
        let _ = tx.send(());
    }
}

/// Asynchronous result of an attempt to progress a state.
enum EventConsequence {
    /// Transition to a new state.
    NewState((TunnelStateWrapper, TunnelStateTransition)),
    /// An event was received, but it was ignored by the state so no transition is performed.
    SameState(TunnelStateWrapper),
    /// The state machine has finished its execution.
    Finished,
}

/// Trait that contains the method all states should implement to handle an event and advance the
/// state machine.
#[async_trait::async_trait]
trait TunnelState: Into<TunnelStateWrapper> + Sized {
    /// Type representing extra information required for entering the state.
    type Bootstrap;

    /// Constructor function.
    ///
    /// This is the state entry point. It attempts to enter the state, and may fail by entering an
    /// error or fallback state instead.
    async fn enter(
        shared_values: &mut SharedTunnelStateValues,
        bootstrap: Self::Bootstrap,
    ) -> (TunnelStateWrapper, TunnelStateTransition);

    /// Main state function.
    ///
    /// This is state exit point. It consumes itself and returns the next state to advance to when
    /// it has completed, or itself if it wants to ignore a received event or if no events were
    /// ready to be received. See [`EventConsequence`] for more details.
    ///
    /// An implementation can handle events from many sources, but it should also handle command
    /// events received through the provided `commands` stream.
    ///
    /// [`EventConsequence`]: enum.EventConsequence.html
    async fn handle_event(
        self,
        commands: &mut TunnelCommandReceiver,
        shared_values: &mut SharedTunnelStateValues,
    ) -> EventConsequence;
}

macro_rules! state_wrapper {
    (enum $wrapper_name:ident { $($state_variant:ident($state_type:ident)),* $(,)* }) => {
        /// Valid states of the tunnel.
        ///
        /// All implementations must implement `TunnelState` so that they can handle events and
        /// commands in order to advance the state machine.
        enum $wrapper_name {
            $($state_variant($state_type),)*
        }

        $(impl From<$state_type> for $wrapper_name {
            fn from(state: $state_type) -> Self {
                $wrapper_name::$state_variant(state)
            }
        })*

        impl $wrapper_name {
            async fn handle_event(
                self,
                commands: &mut TunnelCommandReceiver,
                shared_values: &mut SharedTunnelStateValues,
            ) -> EventConsequence {
                match self {
                    $($wrapper_name::$state_variant(state) => {
                        state.handle_event(commands, shared_values).await
                    })*
                }
            }
        }
    }
}

state_wrapper! {
    enum TunnelStateWrapper {
        Disconnected(DisconnectedState),
        Connecting(ConnectingState),
        Connected(ConnectedState),
        Error(ErrorState),
    }
}

pub struct TunnelStateMachineHandle {
    command_tx: Arc<mpsc::UnboundedSender<TunnelCommand>>,
    shutdown_rx: oneshot::Receiver<()>,
}

impl TunnelStateMachineHandle {
    /// Waits for the tunnel state machine to shut down.
    /// This may fail after a timeout of `TUNNEL_STATE_MACHINE_SHUTDOWN_TIMEOUT`.
    pub async fn try_join(self) {
        drop(self.command_tx);

        match tokio::time::timeout(TUNNEL_STATE_MACHINE_SHUTDOWN_TIMEOUT, self.shutdown_rx).await {
            Ok(_) => log::info!("Tunnel state machine shut down"),
            Err(_) => log::error!("Tunnel state machine did not shut down gracefully"),
        }
    }

    pub fn command_tx(&self) -> Arc<mpsc::UnboundedSender<TunnelCommand>> {
        self.command_tx.clone()
    }
}

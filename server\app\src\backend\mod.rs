pub mod client;
pub mod health_check;
pub mod proxy;
mod setup;

pub use setup::setup;
use tracing::error;

use serde::{de::DeserializeOwned, Deserialize, Serialize};
use serde_json::Value;

const ENDPOINT_SUCCEED: &str = "SUCCESS";
const ENDPOINT_FAILED: &str = "APP_FAILED";
const JSON_PARSE_ERROR: &str = "JSON_PARSE_FAILED";
const JSON_PARSE_ERROR_MSG: &str = "json parse failed";

const SDP_SERVICE_SECRET: &str = "access-sdp-sso";

const SYSTEM_ERROR: &str = "SYSTEM-ERROR";
const SYSTEM_ERROR_MESSAGE: &str = "系统繁忙, 请稍后再试";

// ----------------- 登录相关接口 -------------------
// 认证接口
const SDP_AUTH: &str = "/web/auth?lang=zh";

// MFA认证接口
const SDP_MFA_AUTH: &str = "/web/auth/mfa?lang=zh";

// SMS认证接口
const SDP_SMS_AUTH: &str = "/web/auth/sms?lang=zh";

// 联动登录认证接口
const SDP_LINKED_AUTH: &str = "/web/auth/loginLinked/ticketAuth?lang=zh";

// 获取secret
const SDP_EXCHANGE_SECRET: &str = "/web/auth/exchange_secret?lang=zh";

// 联动登录获取票据接口
const SDP_LINKED_GET: &str = "/web/auth/refresh_connect?lang=zh";

// 退出
const SDP_LOGOUT: &str = "/web/auth/logout?lang=zh";

// 获取票据
const SDP_LINKED_TICKET: &str = "/web/sso/login/getLinkedTicket?lang=zh";

// 通过验证码修改密码
const SDP_CHANGE_PWD_BY_VERIFY_CODE: &str = "/web/sdp/password";

// 通过旧密码修改密码
const SDP_CHANGE_PWD_BY_OLD: &str = "/web/sdp/password/pwd";

// 下发策略
const CLIENT_EXECUTION_POLICY: &str = "/web/client/getClientExecutionPolicy?lang=zh";

// ----------------- SDP管理相关接口 -------------------

// 资源列表
const SDP_RESOURCE_LIST: &str = "/micro/client/resource/list?lang=zh";

// ----------------- MFA相关接口 -------------------
// 查询认证结果
const MFA_AUTH_RESULT: &str = "/v1/micro/mfas/auth/";

// 接口返回数据
#[derive(Deserialize, Serialize)]
pub struct RestResult {
    pub code: String,
    pub data: Value,
}

impl RestResult {
    pub fn ok(data: Value) -> Self {
        RestResult {
            code: ENDPOINT_SUCCEED.to_string(),
            data,
        }
    }

    pub fn failed(code: &str, msg: &str) -> Self {
        RestResult {
            code: ENDPOINT_FAILED.to_string(),
            data: Error {
                code: code.to_string(),
                msg: msg.to_string(),
            }
            .as_value(),
        }
    }

    pub fn failed_with_err(err: Error) -> Self {
        RestResult {
            code: ENDPOINT_FAILED.to_string(),
            data: err.as_value(),
        }
    }

    pub fn as_bytes(&self) -> Vec<u8> {
        serde_json::to_vec(self).unwrap()
    }
}

#[derive(Serialize)]
pub struct Error {
    pub code: String,
    #[serde(rename = "errorMsg")]
    pub msg: String,
}

impl Error {
    pub fn new(code: &str, msg: &str) -> Error {
        Error {
            code: code.to_string(),
            msg: msg.to_string(),
        }
    }

    pub fn as_value(&self) -> Value {
        serde_json::to_value(self).unwrap()
    }
}

impl From<&Value> for Error {
    fn from(v: &Value) -> Self {
        error!("error response: {}", v);
        if v.is_null() {
            return Error::new(ENDPOINT_FAILED, JSON_PARSE_ERROR);
        }
        let code = v["code"].as_str().unwrap_or(ENDPOINT_FAILED);
        let msg = v["errorMsg"].as_str().unwrap_or(JSON_PARSE_ERROR);

        Error::new(code, msg)
    }
}

impl std::fmt::Display for Error {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        self.msg.fmt(f)
    }
}

impl std::fmt::Debug for Error {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        f.debug_struct("Error")
            .field("code", &self.code)
            .field("message", &self.msg)
            .finish()
    }
}

fn parse_result<T>(buf: &[u8]) -> Result<Option<T>, Error>
where
    T: DeserializeOwned,
{
    let mut result: Value = match serde_json::from_slice(buf) {
        Ok(value) => value,
        Err(_) => {
            return Err(Error::new(JSON_PARSE_ERROR, JSON_PARSE_ERROR_MSG));
        }
    };
    if result["code"] == ENDPOINT_SUCCEED {
        if result["data"].is_null() {
            return Ok(None);
        }
        let result = match serde_json::from_value::<T>(result["data"].take()) {
            Ok(result) => result,
            Err(_) => {
                return Err(Error::new(JSON_PARSE_ERROR, JSON_PARSE_ERROR_MSG));
            }
        };
        return Ok(Some(result));
    }

    Err((&result["data"]).into())
}

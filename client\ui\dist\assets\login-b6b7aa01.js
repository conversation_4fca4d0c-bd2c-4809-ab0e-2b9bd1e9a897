import{B as vo,a as ko,C as Io,D as To,E as <PERSON>,b as <PERSON><PERSON>,c as <PERSON>,F as Ie,K as Ro,R as <PERSON>,S as <PERSON>,d as <PERSON>,T as Ao,e as qo,f as <PERSON>,g as <PERSON><PERSON>,h as <PERSON>,i as No,V as Vo,j as <PERSON>o,k as <PERSON>,l as zo,m as <PERSON>,n as jo,o as Zo,p as Wo,q as Ae,r as Xo,s as E,t as M,u as S,v as y,w as Yo,x as Qo,y as <PERSON>,z as $o,A as bn,G as ei,H as q,I as m,J as ti,L as ni,M as si,N as oi,O as ii,P as ri,Q as ai,U as li,W as ci,X as ui,Y as di,Z as hi,_ as fi,$ as mi,a0 as pi,a1 as gi,a2 as Ci,a3 as bi,a4 as _i,a5 as wi,a6 as yi,a7 as xi,a8 as Fi,a9 as vi,aa as _n,ab as ki,ac as Ii,ad as Ti,ae as <PERSON>,af as E<PERSON>,ag as <PERSON>,ah as Ri,ai as <PERSON>,aj as <PERSON><PERSON>,ak as <PERSON>,al as <PERSON>,am as qi,an as <PERSON>,ao as <PERSON>,ap as Oi,aq as pe,ar as Gi,as as Ni,at as Vi,au as Ui,av as Hi,aw as zi,ax as Ki,ay as ji,az as Zi,aA as Wi,aB as Xi,aC as Yi,aD as Qi,aE as p,aF as Ke,aG as Ji,aH as $i,aI as je,aJ as er,aK as tr,aL as nr,aM as sr,aN as or,aO as ir,aP as Te,aQ as rr,aR as g,aS as qe,aT as ar,aU as lr,aV as cr,aW as ur,aX as dr,aY as hr,aZ as fr,a_ as mr,a$ as pr,b0 as gr,b1 as Cr,b2 as br,b3 as Y,b4 as _r,b5 as wr,b6 as yr,b7 as xr,b8 as Fr,b9 as vr,ba as kr,bb as Ir,bc as Tr,bd as Mr,be as Er,bf as Sr,bg as Rr,bh as Pr,bi as Br,bj as Lr,bk as Ar,bl as qr,bm as Dr,bn as Or,bo as Dt,bp as Ot,bq as Gr,br as Nr,bs as Vr,bt as Ur,bu as Hr,bv as zr,bw as Kr,bx as C,by as jr,bz as de,bA as wn,bB as Zr,bC as Wr,bD as Xr,bE as Oe,bF as Yr,bG as Qr,bH as Jr,bI as $r,bJ as ht,bK as ea,bL as yn,bM as ta,bN as na,bO as sa,bP as oa,bQ as ia,bR as ra,bS as aa,bT as la,bU as O,bV as fe,bW as j,bX as De,bY as ge,bZ as $e,b_ as Ut,b$ as ca,c0 as ua,c1 as N,c2 as da,c3 as ha,c4 as nn,c5 as fa,c6 as sn,c7 as ma,c8 as pa,c9 as ga,ca as Ca,cb as ba,cc as _a,cd as wa,ce as ya}from"./index-8ff3976b.js";import{u as Ht,_ as xa,F as xn,A as Fn,S as vn,a as kn,V as In,M as Tn,N as Mn,E as En,Q as Sn,T as Rn,b as Pn,c as Bn,W as Fa,d as Ln,U as An,e as qn,f as Dn}from"./wechatOtp-757be8f5.js";import{u as va}from"./updater-d62bd7a9.js";import{t as ka}from"./terminalPage-4138c85a.js";const Ia=()=>{},Ta=Object.freeze(Object.defineProperty({__proto__:null,BaseTransition:vo,BaseTransitionPropsValidators:ko,Comment:Io,DeprecationTypes:To,EffectScope:Mo,ErrorCodes:Eo,ErrorTypeStrings:So,Fragment:Ie,KeepAlive:Ro,ReactiveEffect:Po,Static:Bo,Suspense:Lo,Teleport:Ao,Text:qo,TrackOpTypes:Do,Transition:Oo,TransitionGroup:Go,TriggerOpTypes:No,VueElement:Vo,assertNumber:Uo,callWithAsyncErrorHandling:Ho,callWithErrorHandling:zo,camelize:Ko,capitalize:jo,cloneVNode:Zo,compatUtils:Wo,compile:Ia,computed:Ae,createApp:Xo,createBlock:E,createCommentVNode:M,createElementBlock:S,createElementVNode:y,createHydrationRenderer:Yo,createPropsRestProxy:Qo,createRenderer:Jo,createSSRApp:$o,createSlots:bn,createStaticVNode:ei,createTextVNode:q,createVNode:m,customRef:ti,defineAsyncComponent:ni,defineComponent:si,defineCustomElement:oi,defineEmits:ii,defineExpose:ri,defineModel:ai,defineOptions:li,defineProps:ci,defineSSRCustomElement:ui,defineSlots:di,devtools:hi,effect:fi,effectScope:mi,getCurrentInstance:pi,getCurrentScope:gi,getTransitionRawChildren:Ci,guardReactiveProps:bi,h:_i,handleError:wi,hasInjectionContext:yi,hydrate:xi,initCustomFormatter:Fi,initDirectivesForSSR:vi,inject:_n,isMemoSame:ki,isProxy:Ii,isReactive:Ti,isReadonly:Mi,isRef:Ei,isRuntimeOnly:Si,isShallow:Ri,isVNode:Pi,markRaw:Bi,mergeDefaults:Li,mergeModels:Ai,mergeProps:qi,nextTick:Di,normalizeClass:Se,normalizeProps:Oi,normalizeStyle:pe,onActivated:Gi,onBeforeMount:Ni,onBeforeUnmount:Vi,onBeforeUpdate:Ui,onDeactivated:Hi,onErrorCaptured:zi,onMounted:Ki,onRenderTracked:ji,onRenderTriggered:Zi,onScopeDispose:Wi,onServerPrefetch:Xi,onUnmounted:Yi,onUpdated:Qi,openBlock:p,popScopeId:Ke,provide:Ji,proxyRefs:$i,pushScopeId:je,queuePostFlushCb:er,reactive:tr,readonly:nr,ref:sr,registerRuntimeCompiler:or,render:ir,renderList:Te,renderSlot:rr,resolveComponent:g,resolveDirective:qe,resolveDynamicComponent:ar,resolveFilter:lr,resolveTransitionHooks:cr,setBlockTracking:ur,setDevtoolsHook:dr,setTransitionHooks:hr,shallowReactive:fr,shallowReadonly:mr,shallowRef:pr,ssrContextKey:gr,ssrUtils:Cr,stop:br,toDisplayString:Y,toHandlerKey:_r,toHandlers:wr,toRaw:yr,toRef:xr,toRefs:Fr,toValue:vr,transformVNodeArgs:kr,triggerRef:Ir,unref:Tr,useAttrs:Mr,useCssModule:Er,useCssVars:Sr,useModel:Rr,useSSRContext:Pr,useSlots:Br,useTransitionState:Lr,vModelCheckbox:Ar,vModelDynamic:qr,vModelRadio:Dr,vModelSelect:Or,vModelText:Dt,vShow:Ot,version:Gr,warn:Nr,watch:Vr,watchEffect:Ur,watchPostEffect:Hr,watchSyncEffect:zr,withAsyncContext:Kr,withCtx:C,withDefaults:jr,withDirectives:de,withKeys:wn,withMemo:Zr,withModifiers:Wr,withScopeId:Xr},Symbol.toStringTag,{value:"Module"}));const Ma={props:{successFun:{type:Function,default:()=>{}},successIcon:{type:String,default:"fa-check"},successText:{type:String,default:"验证成功"},startIcon:{type:String,default:"fa-angle-double-right"},startText:{type:String,default:"请拖住滑块，拖动到最右边"},errorFun:{type:Function,default:()=>{}},status:{type:String,default:""}},data(){return{disX:0,rangeStatus:!1,ele:null}},methods:{rangeMove(e){this.ele=e.target;let t=e.target,r=e.clientX,a=t.offsetWidth,n=t.parentElement.offsetWidth-a;if(this.rangeStatus)return!1;document.onmousemove=u=>{let b=u.clientX;this.disX=b-r,this.disX<=0&&(this.disX=0),this.disX>=n-a&&(this.disX=n),t.style.transition=".1s all",t.style.transform="translateX("+this.disX+"px)",u.preventDefault()},document.onmouseup=()=>{this.disX!==n?(t.style.transition=".5s all",t.style.transform="translateX(0)",this.errorFun&&this.errorFun()):(this.rangeStatus=!0,this.status&&(this.$parent[this.status]=!0),this.successFun&&this.successFun()),document.onmousemove=null,document.onmouseup=null}},init(){this.rangeStatus=!1,this.ele.style.transform="translateX(0px)"}}},Ea={class:"slider-check-box"};function Sa(e,t,r,a,s,n){return p(),S("div",Ea,[y("div",{class:Se(["slider-check",s.rangeStatus?"success":""])},[y("em",{class:Se(["fa",s.rangeStatus?r.successIcon:r.startIcon]),onMousedown:t[0]||(t[0]=(...u)=>n.rangeMove&&n.rangeMove(...u))},null,34),q(" "+Y(s.rangeStatus?r.successText:r.startText),1)],2)])}const Ra=Oe(Ma,[["render",Sa],["__scopeId","data-v-989078b7"]]);var On={exports:{}};(function(e,t){(function(){var r,a=0xdeadbeefcafe,s=(a&16777215)==15715070;function n(o,i,l){o!=null&&(typeof o=="number"?this.fromNumber(o,i,l):i==null&&typeof o!="string"?this.fromString(o,256):this.fromString(o,i))}function u(){return new n(null)}function b(o,i,l,c,d,f){for(;--f>=0;){var w=i*this[o++]+l[c]+d;d=Math.floor(w/67108864),l[c++]=w&67108863}return d}function h(o,i,l,c,d,f){for(var w=i&32767,F=i>>15;--f>=0;){var D=this[o]&32767,K=this[o++]>>15,se=F*D+K*w;D=w*D+((se&32767)<<15)+l[c]+(d&1073741823),d=(D>>>30)+(se>>>15)+F*K+(d>>>30),l[c++]=D&1073741823}return d}function I(o,i,l,c,d,f){for(var w=i&16383,F=i>>14;--f>=0;){var D=this[o]&16383,K=this[o++]>>14,se=F*D+K*w;D=w*D+((se&16383)<<14)+l[c]+d,d=(D>>28)+(se>>14)+F*K,l[c++]=D&268435455}return d}var _=typeof navigator<"u";_&&s&&navigator.appName=="Microsoft Internet Explorer"?(n.prototype.am=h,r=30):_&&s&&navigator.appName!="Netscape"?(n.prototype.am=b,r=26):(n.prototype.am=I,r=28),n.prototype.DB=r,n.prototype.DM=(1<<r)-1,n.prototype.DV=1<<r;var v=52;n.prototype.FV=Math.pow(2,v),n.prototype.F1=v-r,n.prototype.F2=2*r-v;var k="0123456789abcdefghijklmnopqrstuvwxyz",T=new Array,x,P;for(x="0".charCodeAt(0),P=0;P<=9;++P)T[x++]=P;for(x="a".charCodeAt(0),P=10;P<36;++P)T[x++]=P;for(x="A".charCodeAt(0),P=10;P<36;++P)T[x++]=P;function A(o){return k.charAt(o)}function V(o,i){var l=T[o.charCodeAt(i)];return l??-1}function U(o){for(var i=this.t-1;i>=0;--i)o[i]=this[i];o.t=this.t,o.s=this.s}function W(o){this.t=1,this.s=o<0?-1:0,o>0?this[0]=o:o<-1?this[0]=o+this.DV:this.t=0}function H(o){var i=u();return i.fromInt(o),i}function ie(o,i){var l;if(i==16)l=4;else if(i==8)l=3;else if(i==256)l=8;else if(i==2)l=1;else if(i==32)l=5;else if(i==4)l=2;else{this.fromRadix(o,i);return}this.t=0,this.s=0;for(var c=o.length,d=!1,f=0;--c>=0;){var w=l==8?o[c]&255:V(o,c);if(w<0){o.charAt(c)=="-"&&(d=!0);continue}d=!1,f==0?this[this.t++]=w:f+l>this.DB?(this[this.t-1]|=(w&(1<<this.DB-f)-1)<<f,this[this.t++]=w>>this.DB-f):this[this.t-1]|=w<<f,f+=l,f>=this.DB&&(f-=this.DB)}l==8&&o[0]&128&&(this.s=-1,f>0&&(this[this.t-1]|=(1<<this.DB-f)-1<<f)),this.clamp(),d&&n.ZERO.subTo(this,this)}function re(){for(var o=this.s&this.DM;this.t>0&&this[this.t-1]==o;)--this.t}function G(o){if(this.s<0)return"-"+this.negate().toString(o);var i;if(o==16)i=4;else if(o==8)i=3;else if(o==2)i=1;else if(o==32)i=5;else if(o==4)i=2;else return this.toRadix(o);var l=(1<<i)-1,c,d=!1,f="",w=this.t,F=this.DB-w*this.DB%i;if(w-- >0)for(F<this.DB&&(c=this[w]>>F)>0&&(d=!0,f=A(c));w>=0;)F<i?(c=(this[w]&(1<<F)-1)<<i-F,c|=this[--w]>>(F+=this.DB-i)):(c=this[w]>>(F-=i)&l,F<=0&&(F+=this.DB,--w)),c>0&&(d=!0),d&&(f+=A(c));return d?f:"0"}function we(){var o=u();return n.ZERO.subTo(this,o),o}function ye(){return this.s<0?this.negate():this}function xe(o){var i=this.s-o.s;if(i!=0)return i;var l=this.t;if(i=l-o.t,i!=0)return this.s<0?-i:i;for(;--l>=0;)if((i=this[l]-o[l])!=0)return i;return 0}function B(o){var i=1,l;return(l=o>>>16)!=0&&(o=l,i+=16),(l=o>>8)!=0&&(o=l,i+=8),(l=o>>4)!=0&&(o=l,i+=4),(l=o>>2)!=0&&(o=l,i+=2),(l=o>>1)!=0&&(o=l,i+=1),i}function me(){return this.t<=0?0:this.DB*(this.t-1)+B(this[this.t-1]^this.s&this.DM)}function J(o,i){var l;for(l=this.t-1;l>=0;--l)i[l+o]=this[l];for(l=o-1;l>=0;--l)i[l]=0;i.t=this.t+o,i.s=this.s}function tt(o,i){for(var l=o;l<this.t;++l)i[l-o]=this[l];i.t=Math.max(this.t-o,0),i.s=this.s}function mt(o,i){var l=o%this.DB,c=this.DB-l,d=(1<<c)-1,f=Math.floor(o/this.DB),w=this.s<<l&this.DM,F;for(F=this.t-1;F>=0;--F)i[F+f+1]=this[F]>>c|w,w=(this[F]&d)<<l;for(F=f-1;F>=0;--F)i[F]=0;i[f]=w,i.t=this.t+f+1,i.s=this.s,i.clamp()}function pt(o,i){i.s=this.s;var l=Math.floor(o/this.DB);if(l>=this.t){i.t=0;return}var c=o%this.DB,d=this.DB-c,f=(1<<c)-1;i[0]=this[l]>>c;for(var w=l+1;w<this.t;++w)i[w-l-1]|=(this[w]&f)<<d,i[w-l]=this[w]>>c;c>0&&(i[this.t-l-1]|=(this.s&f)<<d),i.t=this.t-l,i.clamp()}function gt(o,i){for(var l=0,c=0,d=Math.min(o.t,this.t);l<d;)c+=this[l]-o[l],i[l++]=c&this.DM,c>>=this.DB;if(o.t<this.t){for(c-=o.s;l<this.t;)c+=this[l],i[l++]=c&this.DM,c>>=this.DB;c+=this.s}else{for(c+=this.s;l<o.t;)c-=o[l],i[l++]=c&this.DM,c>>=this.DB;c-=o.s}i.s=c<0?-1:0,c<-1?i[l++]=this.DV+c:c>0&&(i[l++]=c),i.t=l,i.clamp()}function Ct(o,i){var l=this.abs(),c=o.abs(),d=l.t;for(i.t=d+c.t;--d>=0;)i[d]=0;for(d=0;d<c.t;++d)i[d+l.t]=l.am(0,c[d],i,d,0,l.t);i.s=0,i.clamp(),this.s!=o.s&&n.ZERO.subTo(i,i)}function bt(o){for(var i=this.abs(),l=o.t=2*i.t;--l>=0;)o[l]=0;for(l=0;l<i.t-1;++l){var c=i.am(l,i[l],o,2*l,0,1);(o[l+i.t]+=i.am(l+1,2*i[l],o,2*l+1,c,i.t-l-1))>=i.DV&&(o[l+i.t]-=i.DV,o[l+i.t+1]=1)}o.t>0&&(o[o.t-1]+=i.am(l,i[l],o,2*l,0,1)),o.s=0,o.clamp()}function _t(o,i,l){var c=o.abs();if(!(c.t<=0)){var d=this.abs();if(d.t<c.t){i?.fromInt(0),l!=null&&this.copyTo(l);return}l==null&&(l=u());var f=u(),w=this.s,F=o.s,D=this.DB-B(c[c.t-1]);D>0?(c.lShiftTo(D,f),d.lShiftTo(D,l)):(c.copyTo(f),d.copyTo(l));var K=f.t,se=f[K-1];if(se!=0){var ee=se*(1<<this.F1)+(K>1?f[K-2]>>this.F2:0),_e=this.FV/ee,st=(1<<this.F1)/ee,le=1<<this.F2,ce=l.t,ot=ce-K,Fe=i??u();for(f.dlShiftTo(ot,Fe),l.compareTo(Fe)>=0&&(l[l.t++]=1,l.subTo(Fe,l)),n.ONE.dlShiftTo(K,Fe),Fe.subTo(f,f);f.t<K;)f[f.t++]=0;for(;--ot>=0;){var Bt=l[--ce]==se?this.DM:Math.floor(l[ce]*_e+(l[ce-1]+le)*st);if((l[ce]+=f.am(0,Bt,l,ot,0,K))<Bt)for(f.dlShiftTo(ot,Fe),l.subTo(Fe,l);l[ce]<--Bt;)l.subTo(Fe,l)}i!=null&&(l.drShiftTo(K,i),w!=F&&n.ZERO.subTo(i,i)),l.t=K,l.clamp(),D>0&&l.rShiftTo(D,l),w<0&&n.ZERO.subTo(l,l)}}}function wt(o){var i=u();return this.abs().divRemTo(o,null,i),this.s<0&&i.compareTo(n.ZERO)>0&&o.subTo(i,i),i}function Ce(o){this.m=o}function yt(o){return o.s<0||o.compareTo(this.m)>=0?o.mod(this.m):o}function xt(o){return o}function Re(o){o.divRemTo(this.m,null,o)}function Xe(o,i,l){o.multiplyTo(i,l),this.reduce(l)}function Ft(o,i){o.squareTo(i),this.reduce(i)}Ce.prototype.convert=yt,Ce.prototype.revert=xt,Ce.prototype.reduce=Re,Ce.prototype.mulTo=Xe,Ce.prototype.sqrTo=Ft;function vt(){if(this.t<1)return 0;var o=this[0];if(!(o&1))return 0;var i=o&3;return i=i*(2-(o&15)*i)&15,i=i*(2-(o&255)*i)&255,i=i*(2-((o&65535)*i&65535))&65535,i=i*(2-o*i%this.DV)%this.DV,i>0?this.DV-i:-i}function be(o){this.m=o,this.mp=o.invDigit(),this.mpl=this.mp&32767,this.mph=this.mp>>15,this.um=(1<<o.DB-15)-1,this.mt2=2*o.t}function kt(o){var i=u();return o.abs().dlShiftTo(this.m.t,i),i.divRemTo(this.m,null,i),o.s<0&&i.compareTo(n.ZERO)>0&&this.m.subTo(i,i),i}function It(o){var i=u();return o.copyTo(i),this.reduce(i),i}function Tt(o){for(;o.t<=this.mt2;)o[o.t++]=0;for(var i=0;i<this.m.t;++i){var l=o[i]&32767,c=l*this.mpl+((l*this.mph+(o[i]>>15)*this.mpl&this.um)<<15)&o.DM;for(l=i+this.m.t,o[l]+=this.m.am(0,c,o,i,0,this.m.t);o[l]>=o.DV;)o[l]-=o.DV,o[++l]++}o.clamp(),o.drShiftTo(this.m.t,o),o.compareTo(this.m)>=0&&o.subTo(this.m,o)}function Mt(o,i){o.squareTo(i),this.reduce(i)}function Et(o,i,l){o.multiplyTo(i,l),this.reduce(l)}be.prototype.convert=kt,be.prototype.revert=It,be.prototype.reduce=Tt,be.prototype.mulTo=Et,be.prototype.sqrTo=Mt;function L(){return(this.t>0?this[0]&1:this.s)==0}function ne(o,i){if(o>4294967295||o<1)return n.ONE;var l=u(),c=u(),d=i.convert(this),f=B(o)-1;for(d.copyTo(l);--f>=0;)if(i.sqrTo(l,c),(o&1<<f)>0)i.mulTo(c,d,l);else{var w=l;l=c,c=w}return i.revert(l)}function Wt(o,i){var l;return o<256||i.isEven()?l=new Ce(i):l=new be(i),this.exp(o,l)}n.prototype.copyTo=U,n.prototype.fromInt=W,n.prototype.fromString=ie,n.prototype.clamp=re,n.prototype.dlShiftTo=J,n.prototype.drShiftTo=tt,n.prototype.lShiftTo=mt,n.prototype.rShiftTo=pt,n.prototype.subTo=gt,n.prototype.multiplyTo=Ct,n.prototype.squareTo=bt,n.prototype.divRemTo=_t,n.prototype.invDigit=vt,n.prototype.isEven=L,n.prototype.exp=ne,n.prototype.toString=G,n.prototype.negate=we,n.prototype.abs=ye,n.prototype.compareTo=xe,n.prototype.bitLength=me,n.prototype.mod=wt,n.prototype.modPowInt=Wt,n.ZERO=H(0),n.ONE=H(1);function fs(){var o=u();return this.copyTo(o),o}function ms(){if(this.s<0){if(this.t==1)return this[0]-this.DV;if(this.t==0)return-1}else{if(this.t==1)return this[0];if(this.t==0)return 0}return(this[1]&(1<<32-this.DB)-1)<<this.DB|this[0]}function ps(){return this.t==0?this.s:this[0]<<24>>24}function gs(){return this.t==0?this.s:this[0]<<16>>16}function Cs(o){return Math.floor(Math.LN2*this.DB/Math.log(o))}function bs(){return this.s<0?-1:this.t<=0||this.t==1&&this[0]<=0?0:1}function _s(o){if(o==null&&(o=10),this.signum()==0||o<2||o>36)return"0";var i=this.chunkSize(o),l=Math.pow(o,i),c=H(l),d=u(),f=u(),w="";for(this.divRemTo(c,d,f);d.signum()>0;)w=(l+f.intValue()).toString(o).substr(1)+w,d.divRemTo(c,d,f);return f.intValue().toString(o)+w}function ws(o,i){this.fromInt(0),i==null&&(i=10);for(var l=this.chunkSize(i),c=Math.pow(i,l),d=!1,f=0,w=0,F=0;F<o.length;++F){var D=V(o,F);if(D<0){o.charAt(F)=="-"&&this.signum()==0&&(d=!0);continue}w=i*w+D,++f>=l&&(this.dMultiply(c),this.dAddOffset(w,0),f=0,w=0)}f>0&&(this.dMultiply(Math.pow(i,f)),this.dAddOffset(w,0)),d&&n.ZERO.subTo(this,this)}function ys(o,i,l){if(typeof i=="number")if(o<2)this.fromInt(1);else for(this.fromNumber(o,l),this.testBit(o-1)||this.bitwiseTo(n.ONE.shiftLeft(o-1),St,this),this.isEven()&&this.dAddOffset(1,0);!this.isProbablePrime(i);)this.dAddOffset(2,0),this.bitLength()>o&&this.subTo(n.ONE.shiftLeft(o-1),this);else{var c=new Array,d=o&7;c.length=(o>>3)+1,i.nextBytes(c),d>0?c[0]&=(1<<d)-1:c[0]=0,this.fromString(c,256)}}function xs(){var o=this.t,i=new Array;i[0]=this.s;var l=this.DB-o*this.DB%8,c,d=0;if(o-- >0)for(l<this.DB&&(c=this[o]>>l)!=(this.s&this.DM)>>l&&(i[d++]=c|this.s<<this.DB-l);o>=0;)l<8?(c=(this[o]&(1<<l)-1)<<8-l,c|=this[--o]>>(l+=this.DB-8)):(c=this[o]>>(l-=8)&255,l<=0&&(l+=this.DB,--o)),c&128&&(c|=-256),d==0&&(this.s&128)!=(c&128)&&++d,(d>0||c!=this.s)&&(i[d++]=c);return i}function Fs(o){return this.compareTo(o)==0}function vs(o){return this.compareTo(o)<0?this:o}function ks(o){return this.compareTo(o)>0?this:o}function Is(o,i,l){var c,d,f=Math.min(o.t,this.t);for(c=0;c<f;++c)l[c]=i(this[c],o[c]);if(o.t<this.t){for(d=o.s&this.DM,c=f;c<this.t;++c)l[c]=i(this[c],d);l.t=this.t}else{for(d=this.s&this.DM,c=f;c<o.t;++c)l[c]=i(d,o[c]);l.t=o.t}l.s=i(this.s,o.s),l.clamp()}function Ts(o,i){return o&i}function Ms(o){var i=u();return this.bitwiseTo(o,Ts,i),i}function St(o,i){return o|i}function Es(o){var i=u();return this.bitwiseTo(o,St,i),i}function Xt(o,i){return o^i}function Ss(o){var i=u();return this.bitwiseTo(o,Xt,i),i}function Yt(o,i){return o&~i}function Rs(o){var i=u();return this.bitwiseTo(o,Yt,i),i}function Ps(){for(var o=u(),i=0;i<this.t;++i)o[i]=this.DM&~this[i];return o.t=this.t,o.s=~this.s,o}function Bs(o){var i=u();return o<0?this.rShiftTo(-o,i):this.lShiftTo(o,i),i}function Ls(o){var i=u();return o<0?this.lShiftTo(-o,i):this.rShiftTo(o,i),i}function As(o){if(o==0)return-1;var i=0;return o&65535||(o>>=16,i+=16),o&255||(o>>=8,i+=8),o&15||(o>>=4,i+=4),o&3||(o>>=2,i+=2),o&1||++i,i}function qs(){for(var o=0;o<this.t;++o)if(this[o]!=0)return o*this.DB+As(this[o]);return this.s<0?this.t*this.DB:-1}function Ds(o){for(var i=0;o!=0;)o&=o-1,++i;return i}function Os(){for(var o=0,i=this.s&this.DM,l=0;l<this.t;++l)o+=Ds(this[l]^i);return o}function Gs(o){var i=Math.floor(o/this.DB);return i>=this.t?this.s!=0:(this[i]&1<<o%this.DB)!=0}function Ns(o,i){var l=n.ONE.shiftLeft(o);return this.bitwiseTo(l,i,l),l}function Vs(o){return this.changeBit(o,St)}function Us(o){return this.changeBit(o,Yt)}function Hs(o){return this.changeBit(o,Xt)}function zs(o,i){for(var l=0,c=0,d=Math.min(o.t,this.t);l<d;)c+=this[l]+o[l],i[l++]=c&this.DM,c>>=this.DB;if(o.t<this.t){for(c+=o.s;l<this.t;)c+=this[l],i[l++]=c&this.DM,c>>=this.DB;c+=this.s}else{for(c+=this.s;l<o.t;)c+=o[l],i[l++]=c&this.DM,c>>=this.DB;c+=o.s}i.s=c<0?-1:0,c>0?i[l++]=c:c<-1&&(i[l++]=this.DV+c),i.t=l,i.clamp()}function Ks(o){var i=u();return this.addTo(o,i),i}function js(o){var i=u();return this.subTo(o,i),i}function Zs(o){var i=u();return this.multiplyTo(o,i),i}function Ws(){var o=u();return this.squareTo(o),o}function Xs(o){var i=u();return this.divRemTo(o,i,null),i}function Ys(o){var i=u();return this.divRemTo(o,null,i),i}function Qs(o){var i=u(),l=u();return this.divRemTo(o,i,l),new Array(i,l)}function Js(o){this[this.t]=this.am(0,o-1,this,0,0,this.t),++this.t,this.clamp()}function $s(o,i){if(o!=0){for(;this.t<=i;)this[this.t++]=0;for(this[i]+=o;this[i]>=this.DV;)this[i]-=this.DV,++i>=this.t&&(this[this.t++]=0),++this[i]}}function Ye(){}function Qt(o){return o}function eo(o,i,l){o.multiplyTo(i,l)}function to(o,i){o.squareTo(i)}Ye.prototype.convert=Qt,Ye.prototype.revert=Qt,Ye.prototype.mulTo=eo,Ye.prototype.sqrTo=to;function no(o){return this.exp(o,new Ye)}function so(o,i,l){var c=Math.min(this.t+o.t,i);for(l.s=0,l.t=c;c>0;)l[--c]=0;var d;for(d=l.t-this.t;c<d;++c)l[c+this.t]=this.am(0,o[c],l,c,0,this.t);for(d=Math.min(o.t,i);c<d;++c)this.am(0,o[c],l,c,0,i-c);l.clamp()}function oo(o,i,l){--i;var c=l.t=this.t+o.t-i;for(l.s=0;--c>=0;)l[c]=0;for(c=Math.max(i-this.t,0);c<o.t;++c)l[this.t+c-i]=this.am(i-c,o[c],l,0,0,this.t+c-i);l.clamp(),l.drShiftTo(1,l)}function Pe(o){this.r2=u(),this.q3=u(),n.ONE.dlShiftTo(2*o.t,this.r2),this.mu=this.r2.divide(o),this.m=o}function io(o){if(o.s<0||o.t>2*this.m.t)return o.mod(this.m);if(o.compareTo(this.m)<0)return o;var i=u();return o.copyTo(i),this.reduce(i),i}function ro(o){return o}function ao(o){for(o.drShiftTo(this.m.t-1,this.r2),o.t>this.m.t+1&&(o.t=this.m.t+1,o.clamp()),this.mu.multiplyUpperTo(this.r2,this.m.t+1,this.q3),this.m.multiplyLowerTo(this.q3,this.m.t+1,this.r2);o.compareTo(this.r2)<0;)o.dAddOffset(1,this.m.t+1);for(o.subTo(this.r2,o);o.compareTo(this.m)>=0;)o.subTo(this.m,o)}function lo(o,i){o.squareTo(i),this.reduce(i)}function co(o,i,l){o.multiplyTo(i,l),this.reduce(l)}Pe.prototype.convert=io,Pe.prototype.revert=ro,Pe.prototype.reduce=ao,Pe.prototype.mulTo=co,Pe.prototype.sqrTo=lo;function uo(o,i){var l=o.bitLength(),c,d=H(1),f;if(l<=0)return d;l<18?c=1:l<48?c=3:l<144?c=4:l<768?c=5:c=6,l<8?f=new Ce(i):i.isEven()?f=new Pe(i):f=new be(i);var w=new Array,F=3,D=c-1,K=(1<<c)-1;if(w[1]=f.convert(this),c>1){var se=u();for(f.sqrTo(w[1],se);F<=K;)w[F]=u(),f.mulTo(se,w[F-2],w[F]),F+=2}var ee=o.t-1,_e,st=!0,le=u(),ce;for(l=B(o[ee])-1;ee>=0;){for(l>=D?_e=o[ee]>>l-D&K:(_e=(o[ee]&(1<<l+1)-1)<<D-l,ee>0&&(_e|=o[ee-1]>>this.DB+l-D)),F=c;!(_e&1);)_e>>=1,--F;if((l-=F)<0&&(l+=this.DB,--ee),st)w[_e].copyTo(d),st=!1;else{for(;F>1;)f.sqrTo(d,le),f.sqrTo(le,d),F-=2;F>0?f.sqrTo(d,le):(ce=d,d=le,le=ce),f.mulTo(le,w[_e],d)}for(;ee>=0&&!(o[ee]&1<<l);)f.sqrTo(d,le),ce=d,d=le,le=ce,--l<0&&(l=this.DB-1,--ee)}return f.revert(d)}function ho(o){var i=this.s<0?this.negate():this.clone(),l=o.s<0?o.negate():o.clone();if(i.compareTo(l)<0){var c=i;i=l,l=c}var d=i.getLowestSetBit(),f=l.getLowestSetBit();if(f<0)return i;for(d<f&&(f=d),f>0&&(i.rShiftTo(f,i),l.rShiftTo(f,l));i.signum()>0;)(d=i.getLowestSetBit())>0&&i.rShiftTo(d,i),(d=l.getLowestSetBit())>0&&l.rShiftTo(d,l),i.compareTo(l)>=0?(i.subTo(l,i),i.rShiftTo(1,i)):(l.subTo(i,l),l.rShiftTo(1,l));return f>0&&l.lShiftTo(f,l),l}function fo(o){if(o<=0)return 0;var i=this.DV%o,l=this.s<0?o-1:0;if(this.t>0)if(i==0)l=this[0]%o;else for(var c=this.t-1;c>=0;--c)l=(i*l+this[c])%o;return l}function mo(o){var i=o.isEven();if(this.isEven()&&i||o.signum()==0)return n.ZERO;for(var l=o.clone(),c=this.clone(),d=H(1),f=H(0),w=H(0),F=H(1);l.signum()!=0;){for(;l.isEven();)l.rShiftTo(1,l),i?((!d.isEven()||!f.isEven())&&(d.addTo(this,d),f.subTo(o,f)),d.rShiftTo(1,d)):f.isEven()||f.subTo(o,f),f.rShiftTo(1,f);for(;c.isEven();)c.rShiftTo(1,c),i?((!w.isEven()||!F.isEven())&&(w.addTo(this,w),F.subTo(o,F)),w.rShiftTo(1,w)):F.isEven()||F.subTo(o,F),F.rShiftTo(1,F);l.compareTo(c)>=0?(l.subTo(c,l),i&&d.subTo(w,d),f.subTo(F,f)):(c.subTo(l,c),i&&w.subTo(d,w),F.subTo(f,F))}if(c.compareTo(n.ONE)!=0)return n.ZERO;if(F.compareTo(o)>=0)return F.subtract(o);if(F.signum()<0)F.addTo(o,F);else return F;return F.signum()<0?F.add(o):F}var X=[2,3,5,7,11,13,17,19,23,29,31,37,41,43,47,53,59,61,67,71,73,79,83,89,97,101,103,107,109,113,127,131,137,139,149,151,157,163,167,173,179,181,191,193,197,199,211,223,227,229,233,239,241,251,257,263,269,271,277,281,283,293,307,311,313,317,331,337,347,349,353,359,367,373,379,383,389,397,401,409,419,421,431,433,439,443,449,457,461,463,467,479,487,491,499,503,509,521,523,541,547,557,563,569,571,577,587,593,599,601,607,613,617,619,631,641,643,647,653,659,661,673,677,683,691,701,709,719,727,733,739,743,751,757,761,769,773,787,797,809,811,821,823,827,829,839,853,857,859,863,877,881,883,887,907,911,919,929,937,941,947,953,967,971,977,983,991,997],po=(1<<26)/X[X.length-1];function go(o){var i,l=this.abs();if(l.t==1&&l[0]<=X[X.length-1]){for(i=0;i<X.length;++i)if(l[0]==X[i])return!0;return!1}if(l.isEven())return!1;for(i=1;i<X.length;){for(var c=X[i],d=i+1;d<X.length&&c<po;)c*=X[d++];for(c=l.modInt(c);i<d;)if(c%X[i++]==0)return!1}return l.millerRabin(o)}function Co(o){var i=this.subtract(n.ONE),l=i.getLowestSetBit();if(l<=0)return!1;var c=i.shiftRight(l);o=o+1>>1,o>X.length&&(o=X.length);for(var d=u(),f=0;f<o;++f){d.fromInt(X[Math.floor(Math.random()*X.length)]);var w=d.modPow(c,this);if(w.compareTo(n.ONE)!=0&&w.compareTo(i)!=0){for(var F=1;F++<l&&w.compareTo(i)!=0;)if(w=w.modPowInt(2,this),w.compareTo(n.ONE)==0)return!1;if(w.compareTo(i)!=0)return!1}}return!0}n.prototype.chunkSize=Cs,n.prototype.toRadix=_s,n.prototype.fromRadix=ws,n.prototype.fromNumber=ys,n.prototype.bitwiseTo=Is,n.prototype.changeBit=Ns,n.prototype.addTo=zs,n.prototype.dMultiply=Js,n.prototype.dAddOffset=$s,n.prototype.multiplyLowerTo=so,n.prototype.multiplyUpperTo=oo,n.prototype.modInt=fo,n.prototype.millerRabin=Co,n.prototype.clone=fs,n.prototype.intValue=ms,n.prototype.byteValue=ps,n.prototype.shortValue=gs,n.prototype.signum=bs,n.prototype.toByteArray=xs,n.prototype.equals=Fs,n.prototype.min=vs,n.prototype.max=ks,n.prototype.and=Ms,n.prototype.or=Es,n.prototype.xor=Ss,n.prototype.andNot=Rs,n.prototype.not=Ps,n.prototype.shiftLeft=Bs,n.prototype.shiftRight=Ls,n.prototype.getLowestSetBit=qs,n.prototype.bitCount=Os,n.prototype.testBit=Gs,n.prototype.setBit=Vs,n.prototype.clearBit=Us,n.prototype.flipBit=Hs,n.prototype.add=Ks,n.prototype.subtract=js,n.prototype.multiply=Zs,n.prototype.divide=Xs,n.prototype.remainder=Ys,n.prototype.divideAndRemainder=Qs,n.prototype.modPow=uo,n.prototype.modInverse=mo,n.prototype.pow=no,n.prototype.gcd=ho,n.prototype.isProbablePrime=go,n.prototype.square=Ws,n.prototype.Barrett=Pe;var nt,$,z;function bo(o){$[z++]^=o&255,$[z++]^=o>>8&255,$[z++]^=o>>16&255,$[z++]^=o>>24&255,z>=Pt&&(z-=Pt)}function Jt(){bo(new Date().getTime())}if($==null){$=new Array,z=0;var ae;if(typeof window<"u"&&window.crypto){if(window.crypto.getRandomValues){var $t=new Uint8Array(32);for(window.crypto.getRandomValues($t),ae=0;ae<32;++ae)$[z++]=$t[ae]}else if(navigator.appName=="Netscape"&&navigator.appVersion<"5"){var en=window.crypto.random(32);for(ae=0;ae<en.length;++ae)$[z++]=en.charCodeAt(ae)&255}}for(;z<Pt;)ae=Math.floor(65536*Math.random()),$[z++]=ae>>>8,$[z++]=ae&255;z=0,Jt()}function _o(){if(nt==null){for(Jt(),nt=Fo(),nt.init($),z=0;z<$.length;++z)$[z]=0;z=0}return nt.next()}function wo(o){var i;for(i=0;i<o.length;++i)o[i]=_o()}function tn(){}tn.prototype.nextBytes=wo;function Rt(){this.i=0,this.j=0,this.S=new Array}function yo(o){var i,l,c;for(i=0;i<256;++i)this.S[i]=i;for(l=0,i=0;i<256;++i)l=l+this.S[i]+o[i%o.length]&255,c=this.S[i],this.S[i]=this.S[l],this.S[l]=c;this.i=0,this.j=0}function xo(){var o;return this.i=this.i+1&255,this.j=this.j+this.S[this.i]&255,o=this.S[this.i],this.S[this.i]=this.S[this.j],this.S[this.j]=o,this.S[o+this.S[this.i]&255]}Rt.prototype.init=yo,Rt.prototype.next=xo;function Fo(){return new Rt}var Pt=256;e.exports={default:n,BigInteger:n,SecureRandom:tn}}).call(Yr)})(On);var ft=On.exports;const{BigInteger:He}=ft;function Pa(e){let t=e.toString(16);if(t[0]!=="-")t.length%2===1?t="0"+t:t.match(/^[0-7]/)||(t="00"+t);else{t=t.substr(1);let r=t.length;r%2===1?r+=1:t.match(/^[0-7]/)||(r+=2);let a="";for(let s=0;s<r;s++)a+="f";a=new He(a,16),t=a.xor(e).add(He.ONE),t=t.toString(16).replace(/^-/,"")}return t}class Gn{constructor(){this.tlv=null,this.t="00",this.l="00",this.v=""}getEncodedHex(){return this.tlv||(this.v=this.getValue(),this.l=this.getLength(),this.tlv=this.t+this.l+this.v),this.tlv}getLength(){const t=this.v.length/2;let r=t.toString(16);return r.length%2===1&&(r="0"+r),t<128?r:(128+r.length/2).toString(16)+r}getValue(){return""}}class on extends Gn{constructor(t){super(),this.t="02",t&&(this.v=Pa(t))}getValue(){return this.v}}class Ba extends Gn{constructor(t){super(),this.t="30",this.asn1Array=t}getValue(){return this.v=this.asn1Array.map(t=>t.getEncodedHex()).join(""),this.v}}function Nn(e,t){return+e[t+2]<8?1:+e.substr(t+2,2)&127+1}function rn(e,t){const r=Nn(e,t),a=e.substr(t+2,r*2);return a?(+a[0]<8?new He(a,16):new He(a.substr(2),16)).intValue():-1}function Lt(e,t){const r=Nn(e,t);return t+(r+1)*2}var La={encodeDer(e,t){const r=new on(e),a=new on(t);return new Ba([r,a]).getEncodedHex()},decodeDer(e){const t=Lt(e,0),r=Lt(e,t),a=rn(e,t),s=e.substr(r,a*2),n=r+s.length,u=Lt(e,n),b=rn(e,n),h=e.substr(u,b*2),I=new He(s,16),_=new He(h,16);return{r:I,s:_}}};const{BigInteger:Q}=ft,an=new Q("2"),ln=new Q("3");class ke{constructor(t,r){this.x=r,this.q=t}equals(t){return t===this?!0:this.q.equals(t.q)&&this.x.equals(t.x)}toBigInteger(){return this.x}negate(){return new ke(this.q,this.x.negate().mod(this.q))}add(t){return new ke(this.q,this.x.add(t.toBigInteger()).mod(this.q))}subtract(t){return new ke(this.q,this.x.subtract(t.toBigInteger()).mod(this.q))}multiply(t){return new ke(this.q,this.x.multiply(t.toBigInteger()).mod(this.q))}divide(t){return new ke(this.q,this.x.multiply(t.toBigInteger().modInverse(this.q)).mod(this.q))}square(){return new ke(this.q,this.x.square().mod(this.q))}}class Ee{constructor(t,r,a,s){this.curve=t,this.x=r,this.y=a,this.z=s??Q.ONE,this.zinv=null}getX(){return this.zinv===null&&(this.zinv=this.z.modInverse(this.curve.q)),this.curve.fromBigInteger(this.x.toBigInteger().multiply(this.zinv).mod(this.curve.q))}getY(){return this.zinv===null&&(this.zinv=this.z.modInverse(this.curve.q)),this.curve.fromBigInteger(this.y.toBigInteger().multiply(this.zinv).mod(this.curve.q))}equals(t){return t===this?!0:this.isInfinity()?t.isInfinity():t.isInfinity()?this.isInfinity():t.y.toBigInteger().multiply(this.z).subtract(this.y.toBigInteger().multiply(t.z)).mod(this.curve.q).equals(Q.ZERO)?t.x.toBigInteger().multiply(this.z).subtract(this.x.toBigInteger().multiply(t.z)).mod(this.curve.q).equals(Q.ZERO):!1}isInfinity(){return this.x===null&&this.y===null?!0:this.z.equals(Q.ZERO)&&!this.y.toBigInteger().equals(Q.ZERO)}negate(){return new Ee(this.curve,this.x,this.y.negate(),this.z)}add(t){if(this.isInfinity())return t;if(t.isInfinity())return this;const r=this.x.toBigInteger(),a=this.y.toBigInteger(),s=this.z,n=t.x.toBigInteger(),u=t.y.toBigInteger(),b=t.z,h=this.curve.q,I=r.multiply(b).mod(h),_=n.multiply(s).mod(h),v=I.subtract(_),k=a.multiply(b).mod(h),T=u.multiply(s).mod(h),x=k.subtract(T);if(Q.ZERO.equals(v))return Q.ZERO.equals(x)?this.twice():this.curve.infinity;const P=I.add(_),A=s.multiply(b).mod(h),V=v.square().mod(h),U=v.multiply(V).mod(h),W=A.multiply(x.square()).subtract(P.multiply(V)).mod(h),H=v.multiply(W).mod(h),ie=x.multiply(V.multiply(I).subtract(W)).subtract(k.multiply(U)).mod(h),re=U.multiply(A).mod(h);return new Ee(this.curve,this.curve.fromBigInteger(H),this.curve.fromBigInteger(ie),re)}twice(){if(this.isInfinity())return this;if(!this.y.toBigInteger().signum())return this.curve.infinity;const t=this.x.toBigInteger(),r=this.y.toBigInteger(),a=this.z,s=this.curve.q,n=this.curve.a.toBigInteger(),u=t.square().multiply(ln).add(n.multiply(a.square())).mod(s),b=r.shiftLeft(1).multiply(a).mod(s),h=r.square().mod(s),I=h.multiply(t).multiply(a).mod(s),_=b.square().mod(s),v=u.square().subtract(I.shiftLeft(3)).mod(s),k=b.multiply(v).mod(s),T=u.multiply(I.shiftLeft(2).subtract(v)).subtract(_.shiftLeft(1).multiply(h)).mod(s),x=b.multiply(_).mod(s);return new Ee(this.curve,this.curve.fromBigInteger(k),this.curve.fromBigInteger(T),x)}multiply(t){if(this.isInfinity())return this;if(!t.signum())return this.curve.infinity;const r=t.multiply(ln),a=this.negate();let s=this;for(let n=r.bitLength()-2;n>0;n--){s=s.twice();const u=r.testBit(n),b=t.testBit(n);u!==b&&(s=s.add(u?this:a))}return s}}let Aa=class{constructor(t,r,a){this.q=t,this.a=this.fromBigInteger(r),this.b=this.fromBigInteger(a),this.infinity=new Ee(this,null,null)}equals(t){return t===this?!0:this.q.equals(t.q)&&this.a.equals(t.a)&&this.b.equals(t.b)}fromBigInteger(t){return new ke(this.q,t)}decodePointHex(t){switch(parseInt(t.substr(0,2),16)){case 0:return this.infinity;case 2:case 3:const r=this.fromBigInteger(new Q(t.substr(2),16));let a=this.fromBigInteger(r.multiply(r.square()).add(r.multiply(this.a)).add(this.b).toBigInteger().modPow(this.q.divide(new Q("4")).add(Q.ONE),this.q));return a.toBigInteger().mod(an).equals(new Q(t.substr(0,2),16).subtract(an))||(a=a.negate()),new Ee(this,r,a);case 4:case 6:case 7:const s=(t.length-2)/2,n=t.substr(2,s),u=t.substr(s+2,s);return new Ee(this,this.fromBigInteger(new Q(n,16)),this.fromBigInteger(new Q(u,16)));default:return null}}};var qa={ECPointFp:Ee,ECCurveFp:Aa};const{BigInteger:he,SecureRandom:Da}=ft,{ECCurveFp:Oa}=qa,Ga=new Da,{curve:Ve,G:Na,n:cn}=Vn();function Va(){return Ve}function Vn(){const e=new he("FFFFFFFEFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF00000000FFFFFFFFFFFFFFFF",16),t=new he("FFFFFFFEFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF00000000FFFFFFFFFFFFFFFC",16),r=new he("28E9FA9E9D9F5E344D5A9E4BCF6509A7F39789F515AB8F92DDBCBD414D940E93",16),a=new Oa(e,t,r),s="32C4AE2C1F1981195F9904466A39C9948FE30BBFF2660BE1715A4589334C74C7",n="BC3736A2F4F6779C59BDCEE36B692153D0A9877CC62A474002DF32E52139F0A0",u=a.decodePointHex("04"+s+n),b=new he("FFFFFFFEFFFFFFFFFFFFFFFFFFFFFFFF7203DF6B21C6052B53BBF40939D54123",16);return{curve:a,G:u,n:b}}function Ua(e,t,r){const s=(e?new he(e,t,r):new he(cn.bitLength(),Ga)).mod(cn.subtract(he.ONE)).add(he.ONE),n=Je(s.toString(16),64),u=Na.multiply(s),b=Je(u.getX().toBigInteger().toString(16),64),h=Je(u.getY().toBigInteger().toString(16),64),I="04"+b+h;return{privateKey:n,publicKey:I}}function Ha(e){if(e.length!==130)throw new Error("Invalid public key to compress");const t=(e.length-2)/2,r=e.substr(2,t),a=new he(e.substr(t+2,t),16);let s="03";return a.mod(new he("2")).equals(he.ZERO)&&(s="02"),s+r}function za(e){e=unescape(encodeURIComponent(e));const t=e.length,r=[];for(let s=0;s<t;s++)r[s>>>2]|=(e.charCodeAt(s)&255)<<24-s%4*8;const a=[];for(let s=0;s<t;s++){const n=r[s>>>2]>>>24-s%4*8&255;a.push((n>>>4).toString(16)),a.push((n&15).toString(16))}return a.join("")}function Je(e,t){return e.length>=t?e:new Array(t-e.length+1).join("0")+e}function Ka(e){return e.map(t=>(t=t.toString(16),t.length===1?"0"+t:t)).join("")}function ja(e){const t=[];let r=0;for(let a=0;a<e.length*2;a+=2)t[a>>>3]|=parseInt(e[r],10)<<24-a%8*4,r++;try{const a=[];for(let s=0;s<e.length;s++){const n=t[s>>>2]>>>24-s%4*8&255;a.push(String.fromCharCode(n))}return decodeURIComponent(escape(a.join("")))}catch{throw new Error("Malformed UTF-8 data")}}function Za(e){const t=[];let r=e.length;r%2!==0&&(e=Je(e,r+1)),r=e.length;for(let a=0;a<r;a+=2)t.push(parseInt(e.substr(a,2),16));return t}function Wa(e){const t=Ve.decodePointHex(e);if(!t)return!1;const r=t.getX();return t.getY().square().equals(r.multiply(r.square()).add(r.multiply(Ve.a)).add(Ve.b))}function Xa(e,t){const r=Ve.decodePointHex(e);if(!r)return!1;const a=Ve.decodePointHex(t);return a?r.equals(a):!1}var Ya={getGlobalCurve:Va,generateEcparam:Vn,generateKeyPairHex:Ua,compressPublicKeyHex:Ha,utf8ToHex:za,leftPad:Je,arrayToHex:Ka,arrayToUtf8:ja,hexToArray:Za,verifyPublicKey:Wa,comparePublicKeyHex:Xa};const ue=new Uint32Array(68),At=new Uint32Array(64);function oe(e,t){const r=t&31;return e<<r|e>>>32-r}function un(e,t){const r=[];for(let a=e.length-1;a>=0;a--)r[a]=(e[a]^t[a])&255;return r}function Qa(e){return e^oe(e,9)^oe(e,17)}function Ja(e){return e^oe(e,15)^oe(e,23)}function ut(e){let t=e.length*8,r=t%512;r=r>=448?512-r%448-1:448-r-1;const a=new Array((r-7)/8),s=new Array(8);for(let _=0,v=a.length;_<v;_++)a[_]=0;for(let _=0,v=s.length;_<v;_++)s[_]=0;t=t.toString(2);for(let _=7;_>=0;_--)if(t.length>8){const v=t.length-8;s[_]=parseInt(t.substr(v),2),t=t.substr(0,v)}else t.length>0&&(s[_]=parseInt(t,2),t="");const n=new Uint8Array([...e,128,...a,...s]),u=new DataView(n.buffer,0),b=n.length/64,h=new Uint32Array([1937774191,1226093241,388252375,3666478592,2842636476,372324522,3817729613,2969243214]);for(let _=0;_<b;_++){ue.fill(0),At.fill(0);const v=16*_;for(let B=0;B<16;B++)ue[B]=u.getUint32((v+B)*4,!1);for(let B=16;B<68;B++)ue[B]=Ja(ue[B-16]^ue[B-9]^oe(ue[B-3],15))^oe(ue[B-13],7)^ue[B-6];for(let B=0;B<64;B++)At[B]=ue[B]^ue[B+4];const k=2043430169,T=2055708042;let x=h[0],P=h[1],A=h[2],V=h[3],U=h[4],W=h[5],H=h[6],ie=h[7],re,G,we,ye,xe;for(let B=0;B<64;B++)xe=B>=0&&B<=15?k:T,re=oe(oe(x,12)+U+oe(xe,B),7),G=re^oe(x,12),we=(B>=0&&B<=15?x^P^A:x&P|x&A|P&A)+V+G+At[B],ye=(B>=0&&B<=15?U^W^H:U&W|~U&H)+ie+re+ue[B],V=A,A=oe(P,9),P=x,x=we,ie=H,H=oe(W,19),W=U,U=Qa(ye);h[0]^=x,h[1]^=P,h[2]^=A,h[3]^=V,h[4]^=U,h[5]^=W,h[6]^=H,h[7]^=ie}const I=[];for(let _=0,v=h.length;_<v;_++){const k=h[_];I.push((k&4278190080)>>>24,(k&16711680)>>>16,(k&65280)>>>8,k&255)}return I}const et=64,Un=new Uint8Array(et),Hn=new Uint8Array(et);for(let e=0;e<et;e++)Un[e]=54,Hn[e]=92;function $a(e,t){for(t.length>et&&(t=ut(t));t.length<et;)t.push(0);const r=un(t,Un),a=un(t,Hn),s=ut([...r,...e]);return ut([...a,...s])}var zn={sm3:ut,hmac:$a};const{BigInteger:te}=ft,{encodeDer:el,decodeDer:tl}=La,R=Ya,ze=zn.sm3,{G:Le,curve:Kn,n:Ge}=R.generateEcparam(),jn=0;function nl(e,t,r=1){e=typeof e=="string"?R.hexToArray(R.utf8ToHex(e)):Array.prototype.slice.call(e),t=R.getGlobalCurve().decodePointHex(t);const a=R.generateKeyPairHex(),s=new te(a.privateKey,16);let n=a.publicKey;n.length>128&&(n=n.substr(n.length-128));const u=t.multiply(s),b=R.hexToArray(R.leftPad(u.getX().toBigInteger().toRadix(16),64)),h=R.hexToArray(R.leftPad(u.getY().toBigInteger().toRadix(16),64)),I=R.arrayToHex(ze([].concat(b,e,h)));let _=1,v=0,k=[];const T=[].concat(b,h),x=()=>{k=ze([...T,_>>24&255,_>>16&255,_>>8&255,_&255]),_++,v=0};x();for(let A=0,V=e.length;A<V;A++)v===k.length&&x(),e[A]^=k[v++]&255;const P=R.arrayToHex(e);return r===jn?n+P+I:n+I+P}function sl(e,t,r=1,{output:a="string"}={}){t=new te(t,16);let s=e.substr(128,64),n=e.substr(128+64);r===jn&&(s=e.substr(e.length-64),n=e.substr(128,e.length-128-64));const u=R.hexToArray(n),h=R.getGlobalCurve().decodePointHex("04"+e.substr(0,128)).multiply(t),I=R.hexToArray(R.leftPad(h.getX().toBigInteger().toRadix(16),64)),_=R.hexToArray(R.leftPad(h.getY().toBigInteger().toRadix(16),64));let v=1,k=0,T=[];const x=[].concat(I,_),P=()=>{T=ze([...x,v>>24&255,v>>16&255,v>>8&255,v&255]),v++,k=0};P();for(let V=0,U=u.length;V<U;V++)k===T.length&&P(),u[V]^=T[k++]&255;return R.arrayToHex(ze([].concat(I,u,_)))===s.toLowerCase()?a==="array"?u:R.arrayToUtf8(u):a==="array"?[]:""}function ol(e,t,{pointPool:r,der:a,hash:s,publicKey:n,userId:u}={}){let b=typeof e=="string"?R.utf8ToHex(e):R.arrayToHex(e);s&&(n=n||Wn(t),b=Zn(b,n,u));const h=new te(t,16),I=new te(b,16);let _=null,v=null,k=null;do{do{let T;r&&r.length?T=r.pop():T=Xn(),_=T.k,v=I.add(T.x1).mod(Ge)}while(v.equals(te.ZERO)||v.add(_).equals(Ge));k=h.add(te.ONE).modInverse(Ge).multiply(_.subtract(v.multiply(h))).mod(Ge)}while(k.equals(te.ZERO));return a?el(v,k):R.leftPad(v.toString(16),64)+R.leftPad(k.toString(16),64)}function il(e,t,r,{der:a,hash:s,userId:n}={}){let u=typeof e=="string"?R.utf8ToHex(e):R.arrayToHex(e);s&&(u=Zn(u,r,n));let b,h;if(a){const x=tl(t);b=x.r,h=x.s}else b=new te(t.substring(0,64),16),h=new te(t.substring(64),16);const I=Kn.decodePointHex(r),_=new te(u,16),v=b.add(h).mod(Ge);if(v.equals(te.ZERO))return!1;const k=Le.multiply(h).add(I.multiply(v)),T=_.add(k.getX().toBigInteger()).mod(Ge);return b.equals(T)}function Zn(e,t,r="1234567812345678"){r=R.utf8ToHex(r);const a=R.leftPad(Le.curve.a.toBigInteger().toRadix(16),64),s=R.leftPad(Le.curve.b.toBigInteger().toRadix(16),64),n=R.leftPad(Le.getX().toBigInteger().toRadix(16),64),u=R.leftPad(Le.getY().toBigInteger().toRadix(16),64);let b,h;if(t.length===128)b=t.substr(0,64),h=t.substr(64,64);else{const k=Le.curve.decodePointHex(t);b=R.leftPad(k.getX().toBigInteger().toRadix(16),64),h=R.leftPad(k.getY().toBigInteger().toRadix(16),64)}const I=R.hexToArray(r+a+s+n+u+b+h),_=r.length*4;I.unshift(_&255),I.unshift(_>>8&255);const v=ze(I);return R.arrayToHex(ze(v.concat(R.hexToArray(e))))}function Wn(e){const t=Le.multiply(new te(e,16)),r=R.leftPad(t.getX().toBigInteger().toString(16),64),a=R.leftPad(t.getY().toBigInteger().toString(16),64);return"04"+r+a}function Xn(){const e=R.generateKeyPairHex(),t=Kn.decodePointHex(e.publicKey);return e.k=new te(e.privateKey,16),e.x1=t.getX().toBigInteger(),e}var rl={generateKeyPairHex:R.generateKeyPairHex,compressPublicKeyHex:R.compressPublicKeyHex,comparePublicKeyHex:R.comparePublicKeyHex,doEncrypt:nl,doDecrypt:sl,doSignature:ol,doVerifySignature:il,getPublicKeyFromPrivateKey:Wn,getPoint:Xn,verifyPublicKey:R.verifyPublicKey};const{sm3:al,hmac:ll}=zn;function cl(e,t){return e.length>=t?e:new Array(t-e.length+1).join("0")+e}function dn(e){return e.map(t=>(t=t.toString(16),t.length===1?"0"+t:t)).join("")}function ul(e){const t=[];let r=e.length;r%2!==0&&(e=cl(e,r+1)),r=e.length;for(let a=0;a<r;a+=2)t.push(parseInt(e.substr(a,2),16));return t}function dl(e){const t=[];for(let r=0,a=e.length;r<a;r++){const s=e.codePointAt(r);if(s<=127)t.push(s);else if(s<=2047)t.push(192|s>>>6),t.push(128|s&63);else if(s<=55295||s>=57344&&s<=65535)t.push(224|s>>>12),t.push(128|s>>>6&63),t.push(128|s&63);else if(s>=65536&&s<=1114111)r++,t.push(240|s>>>18&28),t.push(128|s>>>12&63),t.push(128|s>>>6&63),t.push(128|s&63);else throw t.push(s),new Error("input is not supported")}return t}var hl=function(e,t){if(e=typeof e=="string"?dl(e):Array.prototype.slice.call(e),t){if((t.mode||"hmac")!=="hmac")throw new Error("invalid mode");let a=t.key;if(!a)throw new Error("invalid key");return a=typeof a=="string"?ul(a):Array.prototype.slice.call(a),dn(ll(e,a))}return dn(al(e))};const ve=0,fl=32,Be=16,it=[214,144,233,254,204,225,61,183,22,182,20,194,40,251,44,5,43,103,154,118,42,190,4,195,170,68,19,38,73,134,6,153,156,66,80,244,145,239,152,122,51,84,11,67,237,207,172,98,228,179,28,169,201,8,232,149,128,223,148,250,117,143,63,166,71,7,167,252,243,115,23,186,131,89,60,25,230,133,79,168,104,107,129,178,113,100,218,139,248,235,15,75,112,86,157,53,30,36,14,94,99,88,209,162,37,34,124,59,1,33,120,135,212,0,70,87,159,211,39,82,76,54,2,231,160,196,200,158,234,191,138,210,64,199,56,181,163,247,242,206,249,97,21,161,224,174,93,164,155,52,26,85,173,147,50,48,245,140,177,227,29,246,226,46,130,102,202,96,192,41,35,171,13,83,78,111,213,219,55,69,222,253,142,47,3,255,106,114,109,108,91,81,141,27,175,146,187,221,188,127,17,217,92,65,31,16,90,216,10,193,49,136,165,205,123,189,45,116,208,18,184,229,180,176,137,105,151,74,12,150,119,126,101,185,241,9,197,110,198,132,24,240,125,236,58,220,77,32,121,238,95,62,215,203,57,72],rt=[462357,472066609,943670861,1415275113,1886879365,2358483617,2830087869,3301692121,3773296373,4228057617,404694573,876298825,1347903077,1819507329,2291111581,2762715833,3234320085,3705924337,4177462797,337322537,808926789,1280531041,1752135293,2223739545,2695343797,3166948049,3638552301,4110090761,269950501,741554753,1213159005,1684763257];function qt(e){const t=[];for(let r=0,a=e.length;r<a;r+=2)t.push(parseInt(e.substr(r,2),16));return t}function ml(e){return e.map(t=>(t=t.toString(16),t.length===1?"0"+t:t)).join("")}function pl(e){const t=[];for(let r=0,a=e.length;r<a;r++){const s=e.codePointAt(r);if(s<=127)t.push(s);else if(s<=2047)t.push(192|s>>>6),t.push(128|s&63);else if(s<=55295||s>=57344&&s<=65535)t.push(224|s>>>12),t.push(128|s>>>6&63),t.push(128|s&63);else if(s>=65536&&s<=1114111)r++,t.push(240|s>>>18&28),t.push(128|s>>>12&63),t.push(128|s>>>6&63),t.push(128|s&63);else throw t.push(s),new Error("input is not supported")}return t}function gl(e){const t=[];for(let r=0,a=e.length;r<a;r++)e[r]>=240&&e[r]<=247?(t.push(String.fromCodePoint(((e[r]&7)<<18)+((e[r+1]&63)<<12)+((e[r+2]&63)<<6)+(e[r+3]&63))),r+=3):e[r]>=224&&e[r]<=239?(t.push(String.fromCodePoint(((e[r]&15)<<12)+((e[r+1]&63)<<6)+(e[r+2]&63))),r+=2):e[r]>=192&&e[r]<=223?(t.push(String.fromCodePoint(((e[r]&31)<<6)+(e[r+1]&63))),r++):t.push(String.fromCodePoint(e[r]));return t.join("")}function Ne(e,t){const r=t&31;return e<<r|e>>>32-r}function Me(e){return(it[e>>>24&255]&255)<<24|(it[e>>>16&255]&255)<<16|(it[e>>>8&255]&255)<<8|it[e&255]&255}function at(e){return e^Ne(e,2)^Ne(e,10)^Ne(e,18)^Ne(e,24)}function lt(e){return e^Ne(e,13)^Ne(e,23)}function Cl(e,t,r){const a=new Array(4),s=new Array(4);for(let n=0;n<4;n++)s[0]=e[4*n]&255,s[1]=e[4*n+1]&255,s[2]=e[4*n+2]&255,s[3]=e[4*n+3]&255,a[n]=s[0]<<24|s[1]<<16|s[2]<<8|s[3];for(let n=0,u;n<32;n+=4)u=a[1]^a[2]^a[3]^r[n+0],a[0]^=at(Me(u)),u=a[2]^a[3]^a[0]^r[n+1],a[1]^=at(Me(u)),u=a[3]^a[0]^a[1]^r[n+2],a[2]^=at(Me(u)),u=a[0]^a[1]^a[2]^r[n+3],a[3]^=at(Me(u));for(let n=0;n<16;n+=4)t[n]=a[3-n/4]>>>24&255,t[n+1]=a[3-n/4]>>>16&255,t[n+2]=a[3-n/4]>>>8&255,t[n+3]=a[3-n/4]&255}function bl(e,t,r){const a=new Array(4),s=new Array(4);for(let n=0;n<4;n++)s[0]=e[0+4*n]&255,s[1]=e[1+4*n]&255,s[2]=e[2+4*n]&255,s[3]=e[3+4*n]&255,a[n]=s[0]<<24|s[1]<<16|s[2]<<8|s[3];a[0]^=2746333894,a[1]^=1453994832,a[2]^=1736282519,a[3]^=2993693404;for(let n=0,u;n<32;n+=4)u=a[1]^a[2]^a[3]^rt[n+0],t[n+0]=a[0]^=lt(Me(u)),u=a[2]^a[3]^a[0]^rt[n+1],t[n+1]=a[1]^=lt(Me(u)),u=a[3]^a[0]^a[1]^rt[n+2],t[n+2]=a[2]^=lt(Me(u)),u=a[0]^a[1]^a[2]^rt[n+3],t[n+3]=a[3]^=lt(Me(u));if(r===ve)for(let n=0,u;n<16;n++)u=t[n],t[n]=t[31-n],t[31-n]=u}function hn(e,t,r,{padding:a="pkcs#7",mode:s,iv:n=[],output:u="string"}={}){if(s==="cbc"&&(typeof n=="string"&&(n=qt(n)),n.length!==128/8))throw new Error("iv is invalid");if(typeof t=="string"&&(t=qt(t)),t.length!==128/8)throw new Error("key is invalid");if(typeof e=="string"?r!==ve?e=pl(e):e=qt(e):e=[...e],(a==="pkcs#5"||a==="pkcs#7")&&r!==ve){const k=Be-e.length%Be;for(let T=0;T<k;T++)e.push(k)}const b=new Array(fl);bl(t,b,r);const h=[];let I=n,_=e.length,v=0;for(;_>=Be;){const k=e.slice(v,v+16),T=new Array(16);if(s==="cbc")for(let x=0;x<Be;x++)r!==ve&&(k[x]^=I[x]);Cl(k,T,b);for(let x=0;x<Be;x++)s==="cbc"&&r===ve&&(T[x]^=I[x]),h[v+x]=T[x];s==="cbc"&&(r!==ve?I=T:I=k),_-=Be,v+=Be}if((a==="pkcs#5"||a==="pkcs#7")&&r===ve){const k=h.length,T=h[k-1];for(let x=1;x<=T;x++)if(h[k-x]!==T)throw new Error("padding is invalid");h.splice(k-T,T)}return u!=="array"?r!==ve?ml(h):gl(h):h}var _l={encrypt(e,t,r){return hn(e,t,1,r)},decrypt(e,t,r){return hn(e,t,0,r)}},Gt={sm2:rl,sm3:hl,sm4:_l};const Yn="3.7.5",wl=Yn,yl=typeof atob=="function",xl=typeof btoa=="function",Ze=typeof Buffer=="function",fn=typeof TextDecoder=="function"?new TextDecoder:void 0,mn=typeof TextEncoder=="function"?new TextEncoder:void 0,Fl="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",Qe=Array.prototype.slice.call(Fl),ct=(e=>{let t={};return e.forEach((r,a)=>t[r]=a),t})(Qe),vl=/^(?:[A-Za-z\d+\/]{4})*?(?:[A-Za-z\d+\/]{2}(?:==)?|[A-Za-z\d+\/]{3}=?)?$/,Z=String.fromCharCode.bind(String),pn=typeof Uint8Array.from=="function"?Uint8Array.from.bind(Uint8Array):e=>new Uint8Array(Array.prototype.slice.call(e,0)),Qn=e=>e.replace(/=/g,"").replace(/[+\/]/g,t=>t=="+"?"-":"_"),Jn=e=>e.replace(/[^A-Za-z0-9\+\/]/g,""),$n=e=>{let t,r,a,s,n="";const u=e.length%3;for(let b=0;b<e.length;){if((r=e.charCodeAt(b++))>255||(a=e.charCodeAt(b++))>255||(s=e.charCodeAt(b++))>255)throw new TypeError("invalid character found");t=r<<16|a<<8|s,n+=Qe[t>>18&63]+Qe[t>>12&63]+Qe[t>>6&63]+Qe[t&63]}return u?n.slice(0,u-3)+"===".substring(u):n},zt=xl?e=>btoa(e):Ze?e=>Buffer.from(e,"binary").toString("base64"):$n,Nt=Ze?e=>Buffer.from(e).toString("base64"):e=>{let r=[];for(let a=0,s=e.length;a<s;a+=4096)r.push(Z.apply(null,e.subarray(a,a+4096)));return zt(r.join(""))},dt=(e,t=!1)=>t?Qn(Nt(e)):Nt(e),kl=e=>{if(e.length<2){var t=e.charCodeAt(0);return t<128?e:t<2048?Z(192|t>>>6)+Z(128|t&63):Z(224|t>>>12&15)+Z(128|t>>>6&63)+Z(128|t&63)}else{var t=65536+(e.charCodeAt(0)-55296)*1024+(e.charCodeAt(1)-56320);return Z(240|t>>>18&7)+Z(128|t>>>12&63)+Z(128|t>>>6&63)+Z(128|t&63)}},Il=/[\uD800-\uDBFF][\uDC00-\uDFFFF]|[^\x00-\x7F]/g,es=e=>e.replace(Il,kl),gn=Ze?e=>Buffer.from(e,"utf8").toString("base64"):mn?e=>Nt(mn.encode(e)):e=>zt(es(e)),Ue=(e,t=!1)=>t?Qn(gn(e)):gn(e),Cn=e=>Ue(e,!0),Tl=/[\xC0-\xDF][\x80-\xBF]|[\xE0-\xEF][\x80-\xBF]{2}|[\xF0-\xF7][\x80-\xBF]{3}/g,Ml=e=>{switch(e.length){case 4:var t=(7&e.charCodeAt(0))<<18|(63&e.charCodeAt(1))<<12|(63&e.charCodeAt(2))<<6|63&e.charCodeAt(3),r=t-65536;return Z((r>>>10)+55296)+Z((r&1023)+56320);case 3:return Z((15&e.charCodeAt(0))<<12|(63&e.charCodeAt(1))<<6|63&e.charCodeAt(2));default:return Z((31&e.charCodeAt(0))<<6|63&e.charCodeAt(1))}},ts=e=>e.replace(Tl,Ml),ns=e=>{if(e=e.replace(/\s+/g,""),!vl.test(e))throw new TypeError("malformed base64.");e+="==".slice(2-(e.length&3));let t,r="",a,s;for(let n=0;n<e.length;)t=ct[e.charAt(n++)]<<18|ct[e.charAt(n++)]<<12|(a=ct[e.charAt(n++)])<<6|(s=ct[e.charAt(n++)]),r+=a===64?Z(t>>16&255):s===64?Z(t>>16&255,t>>8&255):Z(t>>16&255,t>>8&255,t&255);return r},Kt=yl?e=>atob(Jn(e)):Ze?e=>Buffer.from(e,"base64").toString("binary"):ns,ss=Ze?e=>pn(Buffer.from(e,"base64")):e=>pn(Kt(e).split("").map(t=>t.charCodeAt(0))),os=e=>ss(is(e)),El=Ze?e=>Buffer.from(e,"base64").toString("utf8"):fn?e=>fn.decode(ss(e)):e=>ts(Kt(e)),is=e=>Jn(e.replace(/[-_]/g,t=>t=="-"?"+":"/")),Vt=e=>El(is(e)),Sl=e=>{if(typeof e!="string")return!1;const t=e.replace(/\s+/g,"").replace(/={0,2}$/,"");return!/[^\s0-9a-zA-Z\+/]/.test(t)||!/[^\s0-9a-zA-Z\-_]/.test(t)},rs=e=>({value:e,enumerable:!1,writable:!0,configurable:!0}),as=function(){const e=(t,r)=>Object.defineProperty(String.prototype,t,rs(r));e("fromBase64",function(){return Vt(this)}),e("toBase64",function(t){return Ue(this,t)}),e("toBase64URI",function(){return Ue(this,!0)}),e("toBase64URL",function(){return Ue(this,!0)}),e("toUint8Array",function(){return os(this)})},ls=function(){const e=(t,r)=>Object.defineProperty(Uint8Array.prototype,t,rs(r));e("toBase64",function(t){return dt(this,t)}),e("toBase64URI",function(){return dt(this,!0)}),e("toBase64URL",function(){return dt(this,!0)})},Rl=()=>{as(),ls()},Pl={version:Yn,VERSION:wl,atob:Kt,atobPolyfill:ns,btoa:zt,btoaPolyfill:$n,fromBase64:Vt,toBase64:Ue,encode:Ue,encodeURI:Cn,encodeURL:Cn,utob:es,btou:ts,decode:Vt,isValid:Sl,fromUint8Array:dt,toUint8Array:os,extendString:as,extendUint8Array:ls,extendBuiltins:Rl},Bl="/assets/network_error.png";const Ll={name:"forgetPwd",props:{show:{type:Boolean,default:!1}},data(){var e=(t,r,a)=>{r===""?a(new Error("请再次输入新密码")):r!==this.ruleForm.newPassword?a(new Error("两次输入密码不一致!")):a()};return{loading:!1,networkError:!1,loading_text:"加载中...",ruleForm:{mobile:"",newPassword:"",newPassword2:"",smsCode:"",emilCode:""},sendCode:"获取验证码",sendCodeEmail:"获取验证码",disabled:!1,defaultTime:60,disabledEmail:!1,defaultTimeEmail:60,verifyCode:"",countdown:void 0,countdownEmail:void 0,tip:"",newerrorMsg:"",errorMsg:"",changePwdListener:null,rules:{mobile:[{required:!0,message:"请输入手机号码",trigger:"blur"}],emilCode:[{required:!0,message:"请输入邮箱号码",trigger:"blur"}],smsCode:[{required:!0,message:"请输入验证码",trigger:"blur"}],newPassword:[{required:!0,message:"请输入新密码",trigger:"blur"}],newPassword2:[{required:!0,validator:e,trigger:"blur"}]},activeForgetMethodName:"MOBILE"}},created(){this.closeDialog()},methods:{async VCodeSubmitForm(){this.newerrorMsg="",this.$refs.ruleForm.validate(async e=>{if(e){this.loading=!0,this.loading_text="提交中...";let t={oldVerifyMethod:this.activeForgetMethodName==="MOBILE"?"MOBILE":"EMAIL",oldVerifyAccount:this.activeForgetMethodName==="MOBILE"?this.ruleForm.mobile:this.ruleForm.emilCode,oldVerifyCode:this.ruleForm.smsCode,newPwd:this.ruleForm.newPassword2};Qr(t).then(r=>{this.loading=!1,this.loading_text="加载中...",this.$emit("successCallback","密码修改成功")}).catch(r=>{this.loading=!1,this.loading_text="加载中..."})}else return!1})},pwdPolicy(){this.loading=!0,Jr().then(e=>{this.networkError=!1,this.tip=e.data.tipKey.split(`
`)}).catch(()=>{this.networkError=!0}).finally(()=>{this.loading=!1})},init(){this.pwdPolicy()},closeDialog(){this.networkError=!1,this.$emit("close"),this.errorMsg="",this.newerrorMsg="",this.ruleForm.mobile="",this.ruleForm.smsCode="",this.ruleForm.emilCode="",this.ruleForm.newPassword="",this.ruleForm.newPassword2="",this.sendCode="获取验证码",this.sendCodeEmail="获取验证码",this.disabled=!1,this.disabledEmail=!1,this.defaultTime=60,this.defaultTimeEmail=60,this.activeForgetMethodName="MOBILE",clearInterval(this.countdown),clearInterval(this.countdownEmail),this.countdown=void 0,this.countdownEmail=void 0},getSMSCode(){if(!this.ruleForm.mobile&&this.activeForgetMethodName=="MOBILE"){this.errorMsg="请输入手机号码";return}if(!this.ruleForm.emilCode&&this.activeForgetMethodName=="EMAIL"){this.errorMsg="请输入邮箱号码";return}let e={method:this.activeForgetMethodName=="MOBILE"?"MOBILE":"EMAIL",account:this.activeForgetMethodName=="MOBILE"?this.ruleForm.mobile:this.ruleForm.emilCode};$r(e).then(t=>{let r=this.activeForgetMethodName=="MOBILE"?this.ruleForm.mobile:this.ruleForm.emilCode;this.activeForgetMethodName=="MOBILE"?this.alert.success("已发送至手机"+r+"，请注意查收。"):this.alert.success("已发送至邮箱"+r+"，请注意查收。"),this.activeForgetMethodName=="MOBILE"?this.countdown=setInterval(this.timer,1e3):this.countdownEmail=setInterval(this.timerEmail,1e3)}).catch(t=>{})},timer(){this.disabled=!0,this.sendCode="重新获取("+this.defaultTime+")",this.defaultTime--,this.defaultTime<0&&(clearInterval(this.countdown),this.disabled=!1,this.sendCode="重新获取",this.defaultTime=60)},timerEmail(){this.disabledEmail=!0,this.sendCodeEmail="重新获取("+this.defaultTimeEmail+")",this.defaultTimeEmail--,this.defaultTimeEmail<0&&(clearInterval(this.countdownEmail),this.disabledEmail=!1,this.sendCodeEmail="重新获取",this.defaultTimeEmail=60)},handleClick(e,t){this.activeForgetMethodName=e.props.name,this.errorMsg="",this.newerrorMsg="",this.$refs.ruleForm.resetFields()}}},Al=e=>(je("data-v-fd70aa96"),e=e(),Ke(),e),ql={key:0,style:{"margin-top":"10px"}},Dl={"element-loading-text":"加载中...","element-loading-background":"rgba(0, 0, 0, 0.5)"},Ol={key:2,class:"tipStart",style:{"max-height":"80px","font-size":"12px","text-align":"left","font-weight":"normal",width:"316px","margin-left":"110px",background:"#ffedde","border-radius":"5px","margin-top":"15px"}},Gl={key:1,class:"network-error"},Nl=Al(()=>y("div",null,[y("img",{src:Bl})],-1)),Vl={class:"dialog-footer"};function Ul(e,t,r,a,s,n){const u=g("el-tab-pane"),b=g("el-tabs"),h=g("el-input"),I=g("el-form-item"),_=g("el-button"),v=g("el-form"),k=g("Refresh"),T=g("el-icon"),x=g("el-dialog"),P=qe("loading");return p(),S("div",null,[m(x,{"model-value":r.show,title:"忘记密码",width:"80%",center:"","before-close":n.closeDialog,"destroy-on-close":"","close-on-click-modal":!1,"close-on-press-escape":!1},bn({default:C(()=>[s.networkError?M("",!0):(p(),S("div",ql,[m(b,{modelValue:s.activeForgetMethodName,"onUpdate:modelValue":t[0]||(t[0]=A=>s.activeForgetMethodName=A),type:"card",class:"demo-tabs",onTabClick:n.handleClick},{default:C(()=>[m(u,{label:"短信重置密码",name:"MOBILE"}),m(u,{label:"邮箱重置密码",name:"EMAIL"})]),_:1},8,["modelValue","onTabClick"]),de((p(),S("div",Dl,[m(v,{ref:"ruleForm",model:s.ruleForm,rules:s.rules,"label-width":"110px",class:"changePwd-Form"},{default:C(()=>[s.activeForgetMethodName==="MOBILE"?(p(),E(I,{key:0,class:"form-item",label:"手机号码 :",prop:"mobile",error:s.errorMsg},{default:C(()=>[m(h,{modelValue:s.ruleForm.mobile,"onUpdate:modelValue":t[1]||(t[1]=A=>s.ruleForm.mobile=A),class:"form-input",placeholder:"请输入手机号码",autocomplete:"off",clearable:""},null,8,["modelValue"])]),_:1},8,["error"])):M("",!0),s.activeForgetMethodName==="EMAIL"?(p(),E(I,{key:1,class:"form-item",label:"邮箱号码 :",prop:"emilCode",error:s.errorMsg},{default:C(()=>[m(h,{modelValue:s.ruleForm.emilCode,"onUpdate:modelValue":t[2]||(t[2]=A=>s.ruleForm.emilCode=A),class:"form-input",placeholder:"请输入邮箱号码",autocomplete:"off",clearable:""},null,8,["modelValue"])]),_:1},8,["error"])):M("",!0),m(I,{class:"form-item",label:"验证码 :",prop:"smsCode"},{default:C(()=>[m(h,{modelValue:s.ruleForm.smsCode,"onUpdate:modelValue":t[3]||(t[3]=A=>s.ruleForm.smsCode=A),placeholder:"请输入验证码",class:"form-input",maxlength:"6"},{append:C(()=>[de(m(_,{disabled:s.disabled,size:"small",onClick:n.getSMSCode},{default:C(()=>[q(Y(s.sendCode),1)]),_:1},8,["disabled","onClick"]),[[Ot,s.activeForgetMethodName==="MOBILE"]]),de(m(_,{disabled:s.disabledEmail,size:"small",onClick:n.getSMSCode},{default:C(()=>[q(Y(s.sendCodeEmail),1)]),_:1},8,["disabled","onClick"]),[[Ot,s.activeForgetMethodName==="EMAIL"]])]),_:1},8,["modelValue"])]),_:1}),s.tip?(p(),S("div",Ol,Y(s.tip[0]),1)):M("",!0),m(I,{class:"form-item",label:"设置新密码 :",prop:"newPassword",error:s.newerrorMsg},{default:C(()=>[m(h,{modelValue:s.ruleForm.newPassword,"onUpdate:modelValue":t[4]||(t[4]=A=>s.ruleForm.newPassword=A),class:"form-input",placeholder:"请输入新密码",type:"password",autocomplete:"off",clearable:"","show-password":""},null,8,["modelValue"])]),_:1},8,["error"]),m(I,{class:"form-item",label:"新密码确认 :",prop:"newPassword2"},{default:C(()=>[m(h,{modelValue:s.ruleForm.newPassword2,"onUpdate:modelValue":t[5]||(t[5]=A=>s.ruleForm.newPassword2=A),class:"form-input",type:"password",placeholder:"请再次输入新密码",autocomplete:"off",clearable:"","show-password":""},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])])),[[P,s.loading,void 0,{fullscreen:!0,lock:!0}]])])),s.networkError?(p(),S("div",Gl,[Nl,y("div",null,[s.networkError?(p(),E(_,{key:0,type:"primary",onClick:n.pwdPolicy},{default:C(()=>[m(T,null,{default:C(()=>[m(k)]),_:1}),q("刷新 ")]),_:1},8,["onClick"])):M("",!0)])])):M("",!0)]),_:2},[s.networkError?void 0:{name:"footer",fn:C(()=>[y("span",Vl,[m(_,{ref:"forgetPwdBtn",round:"",type:"primary",onClick:n.VCodeSubmitForm},{default:C(()=>[q(" 确认修改 ")]),_:1},8,["onClick"])])]),key:"0"}]),1032,["model-value","before-close"])])}const cs=Oe(Ll,[["render",Ul],["__scopeId","data-v-fd70aa96"]]);const Hl={components:{userAgreement:Ht,forgetPwd:cs},props:{participantGroupId:{type:String,default:""},userObj:{type:Object,default:()=>{}},tenant:{type:Array,default:()=>{}},disableLoginButton:{type:Boolean,default:!1},showForgetPwdDialog:{type:Boolean,default:!1},type:{type:String,default:""},name:{type:String,default:""}},setup(){const e=ht();return e.commit("updateIsLogin",!1),{connectState:Ae(()=>e.getters.connectState)}},inject:["reload"],data(){return{loading:!1,throttle:!1,publicKey:CONFIG.secretKey?CONFIG.secretKey:null,user:this.name?{username:this.name}:this.userObj}},created(){this.PublicKeyInit()},methods:{login(){this.throttle||(this.throttle=!0,this.doLogin(),setTimeout(()=>{this.throttle=!1},1e3))},doLogin(){if(!this.user.username){this.alert.error("请输入用户名");return}if(!this.user.password){this.alert.error("请输入密码");return}this.loading=!0,this.$emit("loading");let e="04645629ffb19130f9a8a5338c64148ee819fd273d9b69b5aba97ae25a9f917d10549c62e81c1c87a91067174fb1603dfa9113e526c85cded7a6fdd9c6bab9c5c5";this.publicKey&&this.publicKey.dataTransmitType==1&&(e=this.publicKey.publicKeyStr);let t="04"+Gt.sm2.doEncrypt(Pl.encode(this.user.password),e,0),r={participantKeyword:this.user.username,participantTypes:"03",participantGroupId:this.participantGroupId,password:t};ea(r).then(()=>{this.$emit("mfaCallbackFunc",{type:"qrcode"},this.user)}).catch(a=>{this.$emit("generateRequestIdFailCallbackFunc",a),this.loading=!1})},changeRemember(){this.user.remember_me||(this.user.auto_connect=!1)},changeAutoConnect(){this.user.remember_me=!0},openForgetPwdDialog(){this.$emit("openForgetPwdDialog"),this.$refs.forgetPwd.init()},closeForgetPwdDialog(){this.$emit("closeForgetPwdDialog")},forgetPwdCallback(e){this.$emit("forgetPwdCallback",e)},PublicKeyInit(){yn().then(e=>{let t=e.data;t.dataTransmitType==1?this.publicKeyStr=t.publicKeyStr:this.publicKeyStr=""})}}},zl={key:0,id:"pwdBox"},Kl={class:"inputContent"},jl={class:"inputContent"},Zl={class:"login-form-bottom"},Wl={key:1,class:"mfa-content"},Xl={style:{"margin-top":"61px","padding-bottom":"61px"}},Yl={class:"auth-code-content border"};function Ql(e,t,r,a,s,n){const u=g("el-input"),b=g("el-checkbox"),h=g("el-link"),I=g("el-button"),_=g("userAgreement"),v=g("forget-pwd"),k=qe("loading");return de((p(),S("div",{class:Se(["pwdArea",a.connectState.state&&"pswbox"]),"element-loading-text":"登录中...","element-loading-background":"rgba(0, 0, 0, 0.5)"},[r.type?(p(),S("div",zl,[y("div",{class:"inputItem",style:pe(a.connectState.state&&"margin-top: 0;")},[y("div",Kl,[m(u,{ref:"username",modelValue:s.user.username,"onUpdate:modelValue":t[0]||(t[0]=T=>s.user.username=T),placeholder:"请输入用户名"},null,8,["modelValue"])])],4),y("div",{class:"inputItem",style:pe(a.connectState.state&&"margin-top: 0;")},[y("div",jl,[m(u,{ref:"password",modelValue:s.user.password,"onUpdate:modelValue":t[1]||(t[1]=T=>s.user.password=T),placeholder:"请输入密码",class:"input-pwd","show-password":""},null,8,["modelValue"])])],4),y("div",Zl,[m(b,{modelValue:s.user.remember_me,"onUpdate:modelValue":t[2]||(t[2]=T=>s.user.remember_me=T),label:"记住密码",onChange:n.changeRemember},null,8,["modelValue","onChange"]),m(b,{modelValue:s.user.auto_connect,"onUpdate:modelValue":t[3]||(t[3]=T=>s.user.auto_connect=T),label:"自动登录",onChange:n.changeAutoConnect},null,8,["modelValue","onChange"]),m(h,{class:"login-bottom-item",underline:!1,onClick:n.openForgetPwdDialog},{default:C(()=>[q(" 忘记密码? ")]),_:1},8,["onClick"])]),y("div",null,[m(I,{ref:"confirmBtn",type:"primary",class:"loginBtn",style:pe(a.connectState.state?"margin: 7px 0 0 0;":"margin: 20px 0 5px;"),disabled:r.disableLoginButton,onClick:t[4]||(t[4]=T=>n.login())},{default:C(()=>[q(" 登录 ")]),_:1},8,["style","disabled"]),m(_)])])):(p(),S("div",Wl,[y("div",Xl,[y("div",Yl,[m(u,{ref:"password",modelValue:s.user.password,"onUpdate:modelValue":t[5]||(t[5]=T=>s.user.password=T),placeholder:"请输入密码",class:"auth-input",style:{"padding-right":"10px"},"show-password":""},null,8,["modelValue"])]),m(I,{ref:"confirmBtn",type:"primary",class:"mfa-confirm-btn",onClick:t[6]||(t[6]=T=>n.login())},{default:C(()=>[q("登录")]),_:1},512)])])),m(v,{ref:"forgetPwd",onSuccessCallback:n.forgetPwdCallback,show:r.showForgetPwdDialog,onClose:n.closeForgetPwdDialog},null,8,["onSuccessCallback","show","onClose"])],2)),[[k,s.loading,void 0,{fullscreen:!0,lock:!0}]])}const us=Oe(Hl,[["render",Ql],["__scopeId","data-v-eec72377"]]);const Jl={components:{userAgreement:Ht},props:{participantGroupId:{type:String,default:""},type:{type:String,default:""},userId:{type:String,default:""}},setup(){return ht().commit("updateIsLogin",!1),{}},data(){return{user:{username:"",password:""},loading:!1,address:"",lessee:"",certID:"",polling:null,code:"ukey",ukeyStart:!0,submitting:!1}},watch:{},created(){this.polling=setInterval(()=>{this.ukeycard()},2e3)},methods:{ukeycard(){ta(this.code).then(e=>{if(!e.data.length){this.ukeyStart=!1;return}this.ukeyStart=!0,this.stopPolling(),e&&e.code===0&&(this.user.username=e.data[0].name,this.certID=e.data[0].id)}).catch(()=>{this.alert.error("获取证书失败")})},stopPolling(){this.polling&&clearInterval(this.polling)},async submitAuth(){try{this.subbmitting=!0,await this.clickregister()}finally{this.subbmitting=!1}},clickregister(){if(!this.user.username){this.alert.error("请插入证书");return}if(!this.user.password||this.user.password.length<6){this.alert.error("请输入六位密码");return}this.loading=!0;let e={participantTypes:this.type==="login"?"03":"01,03",participantKeyword:this.userId||this.user.username.split("(")[0]};return na(this.participantGroupId,e.participantTypes,e.participantKeyword,this.code).then(t=>{const r={id:this.certID,password:this.user.password,random_str:t.data.requestId,der_format:"false",ukey_type:this.code};return sa(r).then(a=>{if(a.data.retry_count==0){this.loading=!1,this.alert.error("您的证书密码已被锁死,请联系管理员进行解锁!");return}if(!a.data.result){this.loading=!1,this.alert.error("校验证书密码失败!您还有"+a.data.retry_count+"次机会重试!");return}oa(t.data.requestId,a.data.signature,a.data.certificate).then(()=>{this.$emit("mfaCallbackFunc",{requestId:t.data.requestId,type:this.code})}).catch(s=>{this.loading=!1,this.$emit("childEventCancel",t.data.requestId);let n=JSON.parse(s.message);this.alert.error(n.data.errorMsg)})}).catch(a=>{console.log("err: ",a),this.loading=!1})}).catch(t=>{let r=JSON.parse(t.message);this.alert.error(r.data.errorMsg),this.loading=!1,this.$emit("generateRequestIdFailCallbackFunc",error)})},search(){this.clickregister()},cancel(){this.stopPolling()}}},jt=e=>(je("data-v-956dad9b"),e=e(),Ke(),e),$l={class:"content","element-loading-text":"登录中...","element-loading-background":"rgba(0, 0, 0, 0.5)"},ec={key:0},tc={key:1,style:{margin:"30px 0"}},nc={class:"inputContent"},sc={class:"inputContent"},oc={style:{"text-align":"center"}},ic=jt(()=>y("img",{src:xa,width:"265",height:"200",alt:""},null,-1)),rc=jt(()=>y("p",{style:{"font-size":"14px"}},"未找到可用的ukey证书",-1)),ac=jt(()=>y("p",{style:{"font-size":"14px",color:"#fda43d"}},"请插入ukey证书",-1)),lc=[ic,rc,ac];function cc(e,t,r,a,s,n){const u=g("el-input"),b=g("el-button"),h=g("userAgreement"),I=qe("loading");return de((p(),S("div",$l,[r.type?(p(),S("h3",ec,"Ukey登录")):M("",!0),s.ukeyStart?(p(),S("div",tc,[y("div",{class:"inputItem",style:pe(!r.type&&"width: 60%; margin: 15px auto;")},[y("div",nc,[m(u,{ref:"username",modelValue:s.user.username,"onUpdate:modelValue":t[0]||(t[0]=_=>s.user.username=_),readonly:"",placeholder:"请插入证书"},null,8,["modelValue"])])],4),y("div",{class:"inputItem",style:pe(!r.type&&"width: 60%; margin: 15px auto;")},[y("div",sc,[m(u,{ref:"password",modelValue:s.user.password,"onUpdate:modelValue":t[1]||(t[1]=_=>s.user.password=_),placeholder:"请输入密码","show-password":"",onKeyup:wn(n.search,["enter"])},null,8,["modelValue","onKeyup"])])],4),y("div",oc,[m(b,{ref:"confirmBtn",type:"primary",class:Se({loginBtn:r.type==="login","mfa-confirm-btn":r.type!=="login"}),disabled:r.type==="login"&&!s.user.username||!s.user.password||s.submitting,onClick:n.submitAuth},{default:C(()=>[q(Y(r.type==="login"?"登录":"确定"),1)]),_:1},8,["class","disabled","onClick"])]),r.type?(p(),E(h,{key:0})):M("",!0)])):(p(),S("div",{key:2,class:Se(r.type=="login"?"ukeystart":"ukeystyle")},lc,2))])),[[I,s.loading,void 0,{fullscreen:!0,lock:!0}]])}const uc=Oe(Jl,[["render",cc],["__scopeId","data-v-956dad9b"]]);var ds={},hs={exports:{}};(function(e,t){var r=function(a){this.opt=a||{},this.favicon=this.opt.favicon||"/favicon.ico",this.timeout=this.opt.timeout||0,this.logError=this.opt.logError||!1};r.prototype.ping=function(a,s){var n,u,b;typeof Promise<"u"&&(n=new Promise(function(x,P){u=x,b=P}));var h=this;h.wasSuccess=!1,h.img=new Image,h.img.onload=v,h.img.onerror=k;var I,_=new Date;function v(x){h.wasSuccess=!0,T.call(h,x)}function k(x){h.wasSuccess=!1,T.call(h,x)}h.timeout&&(I=setTimeout(function(){T.call(h,void 0)},h.timeout));function T(){I&&clearTimeout(I);var x=new Date-_;if(s){if(typeof s=="function")return this.wasSuccess?(n&&u(x),s(null,x)):(h.logError&&console.error("error loading resource"),n&&b(x),s("error",x));throw new Error("Callback is not a function.")}else{if(n)return this.wasSuccess?u(x):b(x);throw new Error("Promise is not supported by your browser. Use callback instead.")}}return h.img.src=a+h.favicon+"?"+ +new Date,n},e.exports&&(e.exports=r)})(hs);var dc=hs.exports,hc=dc,fc=hc;const mc=ia(Ta);(function(e){/*!
 * v-offline v3.4.0
 * (c) 2023 Vinayak Kulkarni
 * @license MIT
 */Object.defineProperties(e,{__esModule:{value:!0},[Symbol.toStringTag]:{value:"Module"}});const t=fc,r=mc,a=r.defineComponent({name:"VOffline",props:{onlineClass:{type:String,required:!1,default:""},offlineClass:{type:String,required:!1,default:""},pingUrl:{type:String,required:!1,default:"https://google.com"}},emits:["detected-condition"],setup(b,{slots:h,emit:I}){const _=r.ref(navigator.onLine||!1),v=r.ref(["online","offline","load"]),k=r.ref(b.pingUrl||"https://google.com"),T=r.computed(()=>_.value?typeof b.onlineClass=="string"?b.onlineClass:"":typeof b.offlineClass=="string"?b.offlineClass:"");v.value.forEach(P=>window.addEventListener(P,x)),r.onBeforeUnmount(()=>{v.value.forEach(P=>window.removeEventListener(P,x))});async function x(){const P=new t;try{(await P.ping(k.value)||navigator.onLine)&&(_.value=!0,I("detected-condition",_.value))}catch(A){(A||!navigator.onLine)&&(_.value=!1,I("detected-condition",_.value))}}return()=>r.h("div",{class:T.value},h)}});let s=!1;const n=b=>{s||(b.component("VOffline",a),s=!0)},u=n;e.VOffline=a,e.default=u})(ds);const pc={name:"resetPwd",props:{show:{type:Boolean,default:!1}},setup(){const e=ht();return{loading:Ae(()=>e.getters.changePwdLoading)}},data(){var e=(t,r,a)=>{r===""?a(new Error("请再次输入新密码")):r!==this.ruleForm.newPassword?a(new Error("两次输入密码不一致!")):a()};return{ruleForm:{username:"",oldPassword:"",newPassword:"",newPasswordRepeat:""},secret:"",verifyCode:"",tip:"",newerrorMsg:"",errorMsg:"",changePwdListener:null,rules:{oldPassword:[{required:!0,message:"请输入旧密码",trigger:"blur"}],newPassword:[{required:!0,message:"请输入新密码",trigger:"blur"}],newPasswordRepeat:[{required:!0,validator:e,trigger:"blur"}]}}},created(){},methods:{async VCodeSubmitForm(){this.newerrorMsg="",this.$refs.ruleForm.validate(async e=>{if(e)this.$store.commit("updateChangePwdLoading",!0),ra({password:this.ruleForm.newPassword,verifyCode:this.verifyCode}).then(()=>{this.$store.commit("updateChangePwdLoading",!1),this.$emit("successCallback","密码重置成功"),this.errorMsg="",this.newerrorMsg="",this.ruleForm.username="",this.ruleForm.oldPassword="",this.ruleForm.newPassword="",this.ruleForm.newPasswordRepeat=""}).catch(t=>{this.$store.commit("updateChangePwdLoading",!1),this.newerrorMsg=t.msg});else return!1})},submitForm(){this.errorMsg="",this.newerrorMsg="",this.$refs.ruleForm.validate(e=>{if(e){this.$store.commit("updateChangePwdLoading",!0);const t={username:this.ruleForm.username,old:this.ruleForm.oldPassword,password:this.ruleForm.newPassword};aa(t).then(()=>{this.$emit("successCallback","密码重置成功")}).finally(()=>{this.$store.commit("updateChangePwdLoading",!1)})}else return!1})},pwdPolicy(){la({userKeyword:this.ruleForm.username,userTypes:["03"]}).then(e=>{this.tip=e.data.tipKey.split(`
`)})},init(e){this.secret=e.secret,this.verifyCode=e.verifyCode,this.ruleForm.username=e.username,this.pwdPolicy()},closeDialog(){this.verifyCode||O("plugin:sdp|logout"),this.$emit("close"),this.errorMsg="",this.newerrorMsg="",this.ruleForm.username="",this.ruleForm.oldPassword="",this.ruleForm.newPassword="",this.ruleForm.newPasswordRepeat=""}}},gc=e=>(je("data-v-113753cc"),e=e(),Ke(),e),Cc={"element-loading-text":"加载中...","element-loading-background":"rgba(0, 0, 0, 0.5)"},bc=gc(()=>y("strong",{style:{"font-size":"15px"},class:"f_w_no"},"密码需要满足以下条件：",-1)),_c={class:"dialog-footer"};function wc(e,t,r,a,s,n){const u=g("el-input"),b=g("el-form-item"),h=g("el-form"),I=g("el-button"),_=g("el-dialog"),v=qe("loading");return p(),S("div",null,[m(_,{"model-value":r.show,title:"修改密码",width:"80%",center:"","before-close":n.closeDialog,"destroy-on-close":"","close-on-click-modal":!1,"close-on-press-escape":!1},{footer:C(()=>[y("span",_c,[m(I,{round:"",onClick:n.closeDialog},{default:C(()=>[q("取消")]),_:1},8,["onClick"]),s.verifyCode?(p(),E(I,{key:0,ref:"resetPwdBtn",round:"",type:"primary",onClick:n.VCodeSubmitForm},{default:C(()=>[q(" 确认 ")]),_:1},8,["onClick"])):(p(),E(I,{key:1,ref:"resetPwdBtn",round:"",type:"primary",onClick:n.submitForm},{default:C(()=>[q(" 确认 ")]),_:1},8,["onClick"]))])]),default:C(()=>[de((p(),S("div",Cc,[m(h,{ref:"ruleForm",model:s.ruleForm,rules:s.rules,"label-width":"110px",class:"changePwd-Form"},{default:C(()=>[s.verifyCode?M("",!0):(p(),E(b,{key:0,class:"form-item",label:"旧密码 :",prop:"oldPassword",error:s.errorMsg},{default:C(()=>[m(u,{modelValue:s.ruleForm.oldPassword,"onUpdate:modelValue":t[0]||(t[0]=k=>s.ruleForm.oldPassword=k),class:"form-input",type:"password",placeholder:"请输入旧密码",autocomplete:"off",clearable:"","show-password":""},null,8,["modelValue"])]),_:1},8,["error"])),m(b,{class:"form-item",label:"设置新密码 :",prop:"newPassword",error:s.newerrorMsg},{default:C(()=>[m(u,{modelValue:s.ruleForm.newPassword,"onUpdate:modelValue":t[1]||(t[1]=k=>s.ruleForm.newPassword=k),class:"form-input",placeholder:"请输入新密码",type:"password",autocomplete:"off",clearable:"","show-password":""},null,8,["modelValue"])]),_:1},8,["error"]),m(b,{class:"form-item",label:"新密码确认 :",prop:"newPasswordRepeat"},{default:C(()=>[m(u,{modelValue:s.ruleForm.newPasswordRepeat,"onUpdate:modelValue":t[2]||(t[2]=k=>s.ruleForm.newPasswordRepeat=k),class:"form-input",type:"password",placeholder:"请再次输入新密码",autocomplete:"off",clearable:"","show-password":""},null,8,["modelValue"])]),_:1}),y("div",{class:"tipStart",style:pe(s.verifyCode?"height: 128px;":"height: 55px;")},[bc,(p(!0),S(Ie,null,Te(s.tip,k=>(p(),S("p",{key:k},Y(k),1))),128))],4)]),_:1},8,["model","rules"])])),[[v,a.loading,void 0,{fullscreen:!0,lock:!0}]])]),_:1},8,["model-value","before-close"])])}const yc=Oe(pc,[["render",wc],["__scopeId","data-v-113753cc"]]);async function xc(e,t,r,a,s,n,u,b){return O("plugin:sdp|login",{request:{connection_config:{ip:CONFIG.node.ip,host:CONFIG.node.host,port:parseInt(CONFIG.node.port),spa_port:parseInt(CONFIG.node.spa_port),authorization:{tenant:a,secret:t,username:e}},payload:{type:s,ticket:n,signature:u,tenant:a,password:r||"",authResultId:b||""}}})}(async()=>await fe("cluster_config",async()=>{const e=new Event("showClusterConfig");window.dispatchEvent(e),await j.unminimize(),await j.show(),await j.setFocus()}))();(async()=>await fe("logout",async e=>{e.payload*1!==-1&&De.commit("updateIsLogin",!1);let t="连接已断开";if(e.payload)switch(e.payload*1){case 0:t="设备已禁用";break;case 1:t="用户在其他地方登录";break;case 2:t="访问失败！请与管理员确认您的，安全访问条件。";break;case 3:t="用户被禁用";break;case 4:t="用户不存在";break;case 5:t="用户未生效";break;case 6:t="用户已失效";break;case 7:t="您的密码已更新，需要重新登录";break;case 8:t="管理员已重置您的绑定码，请重新登录";break;case 10:t="您的连接已断开，请重新登录";break;case 11:t="您当前登录的单位被禁用，请联系管理员";break;case 12:t="检测到您的访问可能存在风险，管理员已为您强制退出登录，如需访问资源请稍后重新登录";break;case 13:t="您当前登录的设备已被移除，请重新登录";break}e.payload*1===-1?De.commit("setConnectState",{state:!0,title:"服务器连接异常"}):e.payload*1>=0&&(await j.unminimize(),await j.show(),await j.setFocus(),ge.close(),ge.confirm(t,"提示",{confirmButtonText:"确定",roundButton:!0,center:!0,showClose:!1,showCancelButton:!1,closeOnClickModal:!1,closeOnPressEscape:!1}).then(()=>{let r={};e.payload*1===7&&(r.users={},r.users[CONFIG.selected_tenant.code]={password:null,remember_me:!1,auto_connect:!1}),e.payload*1===11&&(r.selected_tenant=null),O("patch",{value:r}).then(a=>{window.CONFIG=a,$e.push("/login")})}))}))();function Fc(){j.unminimize(),j.show(),j.setFocus(),!window.processMfaAuthState&&window.invokeMfaAuth()}(async()=>await fe("mfa",async()=>{await $e.push("/"),Fc()}))();(async()=>await fe("denied_access",async e=>{await $e.push("/"),await j.unminimize(),await j.show(),await j.setFocus();let t=e.payload?"("+e.payload+")":"";ge.close(),ge.alert('<div class="NoActionMsg"> <img src="/assets/images/jurisdiction.png" alt="" /> <span>抱歉，您目前无法访问'+t+"<br/>请稍后重新访问或联系管理员配置相关权限</span></div>","无权访问",{dangerouslyUseHTMLString:!0,showConfirmButton:!1,center:!0,roundButton:!0})}))();(async()=>await fe("network_changed",async e=>{let t=e.payload*1;De.commit("setNetworkStatus",t===1),De.commit("setConnectState",{state:t!==1,title:"网络连接异常, 请检查网络设置"}),new Ut().emit("reload")}))();(async()=>await fe("request_exit",async()=>{await j.unminimize(),await j.show(),await j.setFocus(),ge.close(),ge.confirm("确定退出?","提示",{confirmButtonText:"确定",showCancelButton:!0,cancelButtonText:"取消",roundButton:!0,center:!0,closeOnClickModal:!1,closeOnPressEscape:!1}).then(()=>{O("graceful_exit")},()=>{console.log("cancel exit")})}))();(async()=>await fe("environment",async e=>{console.log("environment",e.payload),ca(JSON.parse(e.payload)).then(t=>{console.log("res:日志 ",t)}).catch(t=>{console.log("err:日志 ",t)})}))();(async()=>await fe("web_login_ticket",async e=>{console.log("web_login_ticket",e.payload),ua().then(t=>{console.log("res:联动票据 ",t),N(e.payload.callback_event_name,{code:200,data:t})}).catch(t=>{console.log("err:联动票据 ",t),N(e.payload.callback_event_name,{code:500,data:t})})}))();(async()=>await fe("notification",async e=>{console.log("res:后台断开的通知",e.payload),e.payload*1===0?De.commit("setConnectState",{state:!0,title:"服务器连接异常"}):De.commit("setConnectState",{state:!1,title:"服务器连接异常"})}))();(async()=>await fe("config_updated",async e=>{console.log("update config",e.payload),window.CONFIG=e.payload,window.__VUE_APP__.config.globalProperties.$router.currentRoute._value.name==="login"&&new Ut().emit("reload")}))();(async()=>await fe("recconect_status",async e=>{if(e.payload.status)console.log("重连成功"),De.commit("setConnectState",{state:!1,title:"网络连接异常, 请检查网络设置"}),await $e.push("/"),new Ut().emit("reload");else{console.log("重连失败");let r="连接已断开";e.payload.show_long_time_inacitivity_tip&&(r="检测到长时间未使用, 系统已退出"),await j.unminimize(),await j.show(),await j.setFocus(),ge.close(),ge.confirm(r,"提示",{confirmButtonText:"确定",roundButton:!0,center:!0,showCancelButton:!1,closeOnClickModal:!1,closeOnPressEscape:!1}).finally(()=>{O("plugin:sdp|logout").then(()=>{let a={};a.users={},a.users[CONFIG.selected_tenant.code]={secret:null,password:null},O("patch",{value:a}).then(s=>{window.CONFIG=s}).finally(()=>{$e.push("/login")})})})}}))();const vc={name:"mfa",components:{FACE:xn,AUTHCODE:Fn,SMSOTP:vn,FINGERPRINT:kn,VOICE:In,MOBILECLICK:Tn,NATIVEPASS:Mn,EMAILOTP:En,QRCODE:Sn,TFC200:Rn,ANSHUA5:Pn,ESCUOTP1:Bn,WECHATOTP:Fa,ETZ203:Ln,UKEY:An,UKEYWQ:qn,FACIAL:Dn,MFAPWD:us},data(){return{mfaAuthDialog:!1,mfaAuthDetailDialog:!1,title:"身份验证提示",mfaTypes:[],currentCode:"",currentName:"",authResultId:"",participantGroupId:"",username:"",sms:""}},computed:{carouselMfaGroupMethods(){return this.groupOfThrees(this.mfaTypes)}},methods:{init(e){e.mfaTypes.length>1?this.mfaAuthDialog=!0:(this.mfaAuthDetailDialog=!0,this.currentName=e.mfaTypes[0].method.name,this.currentCode=e.mfaTypes[0].method.code),this.mfaTypes=e.mfaTypes,this.participantGroupId=e.participantGroupId,this.username=e.username,this.authResultId=e.authResultId},closeBox(e){this.$refs.pollMfaMethod&&this.$refs.pollMfaMethod.cancel(),this.mfaAuthDetailDialog=!1,this.mfaAuthDialog=!1,this.currentCode="",e&&this.$emit("close")},showHighlightedImage(e){this.currentCode=e},selectMfaType(e,t){e=="facial_recognition"?O("plugin:bio|is_support_facial_recognition").then(r=>{if(!r){this.alert.error("检测到设备不支持人脸识别，请换其他方式认证");return}this.currentName=t,this.currentCode=e,this.mfaAuthDialog=!1,this.mfaAuthDetailDialog=!0}):(this.currentName=t,this.currentCode=e,this.mfaAuthDialog=!1,this.mfaAuthDetailDialog=!0)},mfaLoginCallback(e,t){this.closeBox(),this.$emit("succeed",e,t,this.authResultId)},generateRequestIdFailCallback(e){this.closeBox(),this.$emit("error",e)},errorLog(e){this.closeBox(),this.$emit("errMsg",e)},groupOfThrees(e){const t=[];for(let r=0;r<e.length;r+=3)t.push(e.slice(r,r+3));return t}}},Zt=e=>(je("data-v-fe96422d"),e=e(),Ke(),e),kc={style:{"padding-top":"10px"}},Ic=Zt(()=>y("div",{class:"mfa-model-text",style:{"text-align":"center",color:"#232733"}}," 为了保障您的账户安全，系统要求你完成二次身份认证 ",-1)),Tc={class:"block",style:{margin:"40px 0"}},Mc=["src","onClick"],Ec=["title"],Sc={class:"auth-model"},Rc=Zt(()=>y("div",{style:{"text-align":"center",color:"#232733"}},"为了保障您的账户安全，系统要求您完成二次身份验证",-1)),Pc=Zt(()=>y("div",{class:"auth-tip"},[q(" 请打开"),y("span",{class:"mfa-text"},"【安全令APP】"),q("进行扫码认证 ")],-1));function Bc(e,t,r,a,s,n){const u=g("el-carousel-item"),b=g("el-carousel"),h=g("el-scrollbar"),I=g("el-dialog"),_=g("FACE"),v=g("FACIAL"),k=g("AUTHCODE"),T=g("SMSOTP"),x=g("FINGERPRINT"),P=g("VOICE"),A=g("MOBILECLICK"),V=g("NATIVEPASS"),U=g("EMAILOTP"),W=g("QRCODE"),H=g("TFC200"),ie=g("ANSHUA5"),re=g("ESCUOTP1"),G=g("WECHATOTP"),we=g("ETZ203"),ye=g("UKEY"),xe=g("UKEYWQ"),B=g("MFAPWD");return p(),S("div",null,[m(I,{modelValue:s.mfaAuthDialog,"onUpdate:modelValue":t[0]||(t[0]=me=>s.mfaAuthDialog=me),title:s.title,width:"50%","destroy-on-close":"","before-close":n.closeBox,center:"","close-on-click-modal":!1,"close-on-press-escape":!1},{default:C(()=>[m(h,null,{default:C(()=>[y("div",kc,[Ic,y("div",Tc,[m(b,{trigger:"click",class:"approveTab",height:"80px",autoplay:!1,loop:!1,arrow:s.mfaTypes.length>1?"always":"never"},{default:C(()=>[(p(!0),S(Ie,null,Te(n.carouselMfaGroupMethods,me=>(p(),E(u,{key:me,class:""},{default:C(()=>[(p(!0),S(Ie,null,Te(me,J=>(p(),S("div",{key:J.method.code,class:"carouselCode"},[y("img",{width:"40",height:"40",alt:"",src:s.currentCode!==J.method.code?"assets/images/defaultlogin/"+J.method.code+".png":"/assets/images/mfalogin/"+J.method.code+".png",onClick:tt=>n.selectMfaType(J.method.code,J.method.name),style:pe(s.currentCode===J.method.code&&"pointer-events: none;")},null,12,Mc),y("span",{title:J.method.name},Y(J.method.name),9,Ec)]))),128))]),_:2},1024))),128))]),_:1},8,["arrow"])])])]),_:1})]),_:1},8,["modelValue","title","before-close"]),m(I,{modelValue:s.mfaAuthDetailDialog,"onUpdate:modelValue":t[1]||(t[1]=me=>s.mfaAuthDetailDialog=me),width:"50%",title:s.currentName+"认证","before-close":n.closeBox,"destroy-on-close":"",center:"","close-on-click-modal":!1,"close-on-press-escape":!1},{default:C(()=>[y("div",Sc,[Rc,s.currentCode==="face"?(p(),E(_,{key:0,ref:"pollMfaMethod",name:s.username,"participant-group-id":s.participantGroupId,onMfaCallbackFunc:n.mfaLoginCallback,onGenerateRequestIdFailCallbackFunc:n.generateRequestIdFailCallback,onChildEventCancel:n.errorLog},null,8,["name","participant-group-id","onMfaCallbackFunc","onGenerateRequestIdFailCallbackFunc","onChildEventCancel"])):M("",!0),s.currentCode==="facial_recognition"?(p(),E(v,{key:1,ref:"pollMfaMethod",name:s.username,"participant-group-id":s.participantGroupId,onMfaCallbackFunc:n.mfaLoginCallback,onGenerateRequestIdFailCallbackFunc:n.generateRequestIdFailCallback,onChildEventCancel:n.errorLog},null,8,["name","participant-group-id","onMfaCallbackFunc","onGenerateRequestIdFailCallbackFunc","onChildEventCancel"])):M("",!0),s.currentCode==="auth_code"?(p(),E(k,{key:2,ref:"manualMfaMethod",name:s.username,"participant-group-id":s.participantGroupId,onMfaCallbackFunc:n.mfaLoginCallback,onGenerateRequestIdFailCallbackFunc:n.generateRequestIdFailCallback,onChildEventCancel:n.errorLog},null,8,["name","participant-group-id","onMfaCallbackFunc","onGenerateRequestIdFailCallbackFunc","onChildEventCancel"])):M("",!0),s.currentCode==="sms_otp"?(p(),E(T,{key:3,ref:"manualMfaMethod",name:s.username,"participant-group-id":s.participantGroupId,onMfaCallbackFunc:n.mfaLoginCallback,onGenerateRequestIdFailCallbackFunc:n.generateRequestIdFailCallback,onChildEventCancel:n.errorLog},null,8,["name","participant-group-id","onMfaCallbackFunc","onGenerateRequestIdFailCallbackFunc","onChildEventCancel"])):M("",!0),s.currentCode==="fingerprint"?(p(),E(x,{key:4,ref:"pollMfaMethod",name:s.username,"participant-group-id":s.participantGroupId,onMfaCallbackFunc:n.mfaLoginCallback,onGenerateRequestIdFailCallbackFunc:n.generateRequestIdFailCallback,onChildEventCancel:n.errorLog},null,8,["name","participant-group-id","onMfaCallbackFunc","onGenerateRequestIdFailCallbackFunc","onChildEventCancel"])):M("",!0),s.currentCode==="voice"?(p(),E(P,{key:5,ref:"pollMfaMethod",name:s.username,"participant-group-id":s.participantGroupId,onMfaCallbackFunc:n.mfaLoginCallback,onGenerateRequestIdFailCallbackFunc:n.generateRequestIdFailCallback,onChildEventCancel:n.errorLog},null,8,["name","participant-group-id","onMfaCallbackFunc","onGenerateRequestIdFailCallbackFunc","onChildEventCancel"])):M("",!0),s.currentCode==="mobileclick"?(p(),E(A,{key:6,ref:"pollMfaMethod",name:s.username,"participant-group-id":s.participantGroupId,onMfaCallbackFunc:n.mfaLoginCallback,onGenerateRequestIdFailCallbackFunc:n.generateRequestIdFailCallback,onChildEventCancel:n.errorLog},null,8,["name","participant-group-id","onMfaCallbackFunc","onGenerateRequestIdFailCallbackFunc","onChildEventCancel"])):M("",!0),s.currentCode==="nativepass"?(p(),E(V,{key:7,ref:"pollMfaMethod",name:s.username,"participant-group-id":s.participantGroupId,onMfaCallbackFunc:n.mfaLoginCallback,onGenerateRequestIdFailCallbackFunc:n.generateRequestIdFailCallback,onChildEventCancel:n.errorLog},null,8,["name","participant-group-id","onMfaCallbackFunc","onGenerateRequestIdFailCallbackFunc","onChildEventCancel"])):M("",!0),s.currentCode==="email_otp"?(p(),E(U,{key:8,ref:"manualMfaMethod",name:s.username,"participant-group-id":s.participantGroupId,onMfaCallbackFunc:n.mfaLoginCallback,onGenerateRequestIdFailCallbackFunc:n.generateRequestIdFailCallback,onChildEventCancel:n.errorLog},null,8,["name","participant-group-id","onMfaCallbackFunc","onGenerateRequestIdFailCallbackFunc","onChildEventCancel"])):M("",!0),s.currentCode==="qrcode"?(p(),E(W,{key:9,ref:"pollMfaMethod",name:s.username,"participant-group-id":s.participantGroupId,onMfaCallbackFunc:n.mfaLoginCallback,onGenerateRequestIdFailCallbackFunc:n.generateRequestIdFailCallback,onChildEventCancel:n.errorLog},{default:C(()=>[Pc]),_:1},8,["name","participant-group-id","onMfaCallbackFunc","onGenerateRequestIdFailCallbackFunc","onChildEventCancel"])):M("",!0),s.currentCode==="ft_c200"?(p(),E(H,{key:10,ref:"manualMfaMethod",name:s.username,"participant-group-id":s.participantGroupId,onMfaCallbackFunc:n.mfaLoginCallback,onGenerateRequestIdFailCallbackFunc:n.generateRequestIdFailCallback,onChildEventCancel:n.errorLog},null,8,["name","participant-group-id","onMfaCallbackFunc","onGenerateRequestIdFailCallbackFunc","onChildEventCancel"])):M("",!0),s.currentCode==="anshu_a5"?(p(),E(ie,{key:11,ref:"manualMfaMethod",name:s.username,"participant-group-id":s.participantGroupId,onMfaCallbackFunc:n.mfaLoginCallback,onGenerateRequestIdFailCallbackFunc:n.generateRequestIdFailCallback,onChildEventCancel:n.errorLog},null,8,["name","participant-group-id","onMfaCallbackFunc","onGenerateRequestIdFailCallbackFunc","onChildEventCancel"])):M("",!0),s.currentCode==="escuotp1"?(p(),E(re,{key:12,ref:"manualMfaMethod",name:s.username,"participant-group-id":s.participantGroupId,onMfaCallbackFunc:n.mfaLoginCallback,onGenerateRequestIdFailCallbackFunc:n.generateRequestIdFailCallback,onChildEventCancel:n.errorLog},null,8,["name","participant-group-id","onMfaCallbackFunc","onGenerateRequestIdFailCallbackFunc","onChildEventCancel"])):M("",!0),s.currentCode==="wechat_otp"?(p(),E(G,{key:13,ref:"manualMfaMethod",name:s.username,"participant-group-id":s.participantGroupId,onMfaCallbackFunc:n.mfaLoginCallback,onGenerateRequestIdFailCallbackFunc:n.generateRequestIdFailCallback,onChildEventCancel:n.errorLog},null,8,["name","participant-group-id","onMfaCallbackFunc","onGenerateRequestIdFailCallbackFunc","onChildEventCancel"])):M("",!0),s.currentCode==="et_z203"?(p(),E(we,{key:14,ref:"manualMfaMethod",name:s.username,"participant-group-id":s.participantGroupId,onMfaCallbackFunc:n.mfaLoginCallback,onGenerateRequestIdFailCallbackFunc:n.generateRequestIdFailCallback,onChildEventCancel:n.errorLog},null,8,["name","participant-group-id","onMfaCallbackFunc","onGenerateRequestIdFailCallbackFunc","onChildEventCancel"])):M("",!0),s.currentCode==="ukey_bjca"?(p(),E(ye,{key:15,ref:"pollMfaMethod",name:s.username,"participant-group-id":s.participantGroupId,onMfaCallbackFunc:n.mfaLoginCallback,onGenerateRequestIdFailCallbackFunc:n.generateRequestIdFailCallback,onChildEventCancel:n.errorLog},null,8,["name","participant-group-id","onMfaCallbackFunc","onGenerateRequestIdFailCallbackFunc","onChildEventCancel"])):M("",!0),s.currentCode==="ukey_wq"?(p(),E(xe,{key:16,ref:"pollMfaMethod",name:s.username,"participant-group-id":s.participantGroupId,onMfaCallbackFunc:n.mfaLoginCallback,onGenerateRequestIdFailCallbackFunc:n.generateRequestIdFailCallback,onChildEventCancel:n.errorLog},null,8,["name","participant-group-id","onMfaCallbackFunc","onGenerateRequestIdFailCallbackFunc","onChildEventCancel"])):M("",!0),s.currentCode==="pwd"?(p(),E(B,{key:17,ref:"mfapwd",name:s.username,"participant-group-id":s.participantGroupId,onMfaCallbackFunc:n.mfaLoginCallback,onGenerateRequestIdFailCallbackFunc:n.generateRequestIdFailCallback},null,8,["name","participant-group-id","onMfaCallbackFunc","onGenerateRequestIdFailCallbackFunc"])):M("",!0)])]),_:1},8,["modelValue","title","before-close"])])}const Lc=Oe(vc,[["render",Bc],["__scopeId","data-v-fe96422d"]]),Ac="/assets/images/defaultLogo.png",qc="/assets/QrCodeLogin.png",Dc="/assets/computer.png";const Oc={name:"Login",components:{headerBar:da,SliderCheck:Ra,qrcode:Sn,mfaPwd:us,FINGERPRINT:kn,SMSOTP:vn,EMAILOTP:En,VOICE:In,NATIVEPASS:Mn,AUTHCODE:Fn,MOBILECLICK:Tn,TFC200:Rn,ANSHUA5:Pn,ESCUOTP1:Bn,ETZ203:Ln,VOffline:ds.VOffline,UKEY:An,FACE:xn,FACIAL:Dn,UKEYWQ:qn,UKEYPKI:uc,userAgreement:Ht,resetPwd:yc,forgetPwd:cs,updater:va,mfaMode:Lc,terminalPage:ka},beforeRouteLeave(e,t,r){e.meta.keepalive=!1,r()},setup(){const e=ht();return e.commit("updateIsLogin",!1),{checkUpdate:_n("checkUpdate"),productVersion:window.PRODUCT_VERSION,deviceId:window.DEVICE_ID,connectState:Ae(()=>e.getters.connectState),hasNewVersion:Ae(()=>e.getters.hasNewVersion),showClusterConfig:Ae(()=>e.getters.showClusterConfig),showAboutUs:Ae(()=>e.getters.showAboutUs),isSettings:!1}},inject:["reload"],computed:{carouselMfaMethods(){let e=["qrcode","pwd","sms_otp"];return this.mfaMethods.filter(t=>!e.includes(t.method.code))},carouselMfaGroupMethods(){return this.groupOfThrees(this.carouselMfaMethods)},carouselMethodIndex(){if(!this.mfaContains(this.user.login_method))return this.mfaContains("pwd")?this.user.login_method="pwd":this.mfaContains("sms_otp")?this.user.login_method="sms_otp":this.mfaContains("qrcode")?this.user.login_method="qrcode":this.mfaMethods.length&&(this.user.login_method=this.mfaMethods[0].method.code),0;for(let e=0;e<this.carouselMfaGroupMethods.length;e++){let t=this.carouselMfaGroupMethods[e];for(const r in t)if(t[r].method.code===this.user.login_method)return e}return 0}},data(){return{productName:PRODUCT_NAME,tenant:CONFIG.selected_tenant?CONFIG.selected_tenant:{},secret_key:CONFIG.secret_key?CONFIG.secret_key:{},show_change_tenant_tip:!!CONFIG.show_change_tenant_tip,showForgetPwdDialog:!1,showResetPwdDialog:!1,loading:!1,tenants:[],mfaMethods:[],user:{username:"",password:"",remember_me:!1,auto_connect:!1,secret:"",login_method:"pwd"},node:{host:"",ip:null,port:"",spa_port:""},nodeConfig:{internal:{host:"",uri:"/cluster_inside"},external:{host:"",uri:"/cluster"}},authStrategyTask:null,disableLoginButton:!0,linkedLoginToken:null,linkedLoginSignature:null,authResultId:"",participantGroupId:"",loading_text:"加载中...",throttle:!1,retryTimes:1,currentTimes:1,AllBanners:[],position:"",fistRequestCode:"",showTerminalConfig:!1,userId:"",trustParams:null,limitNumber:0}},watch:{user:{deep:!0,handler:function(e,t){e?this.disableLoginButton=!(e.password!==""&&e.username!==""):t&&(this.disableLoginButton=!1)}},$route(e){e.path!="/login"&&(this.$refs.qrcode&&this.$refs.qrcode.cancel(),this.$refs.pollLoginMethod&&this.$refs.pollLoginMethod.cancel())},"user.username"(e,t){this.user.persistent?this.user.persistent=!1:t&&(this.user.password="",this.user.secret="")},"connectState.state"(e){e===!0&&this.$refs.qrcode&&this.$refs.qrcode.close()},mfaMethods(){this.mfaContains(this.user.login_method)||(this.mfaContains("pwd")?this.user.login_method="pwd":this.mfaContains("sms_otp")?this.user.login_method="sms_otp":this.mfaContains("qrcode")?this.user.login_method="qrcode":this.mfaMethods.length&&(this.user.login_method=this.mfaMethods[0].method.code))}},async created(){let e=this;if(!CONFIG.cluster_config_url&&!CONFIG.cluster_external_config_url){this.$store.commit("setConnectState",{state:!1,title:""});let t=this;t.$alert("服务器连接地址不能为空","提示",{confirmButtonText:"确定",showClose:!1,roundButton:!0,center:!0,callback:()=>{t.$store.commit("setClusterConfigVisible",!0)}});return}e.loading=!0,await e.initData(1,1),e.loading=!1},mounted(){console.log("mounted......"),window.addEventListener("showClusterConfig",this.openClusterConfigDialog),window.addEventListener("keydown",this.handleEnterEvent)},beforeUnmount(){console.log("leave mounted......"),window.removeEventListener("showClusterConfig",this.openClusterConfigDialog,!1),window.removeEventListener("keydown",this.handleEnterEvent,!1),this.$refs.qrcode&&this.$refs.qrcode.cancel(),this.$refs.pollLoginMethod&&this.$refs.pollLoginMethod.cancel(),this.authStrategyTask&&(clearInterval(this.authStrategyTask),this.authStrategyTask=null)},methods:{startLoading(){this.loading=!0},setCurrentTenant(){this.tenant=CONFIG.selected_tenant},setCloseTenantTip(){this.show_change_tenant_tip=CONFIG.show_change_tenant_tip},resetState(){this.loading=!1,this.mfaMethods=[]},mfaContains(e){return!!this.mfaMethods.find(t=>t.method.code===e)},handleEnterEvent(e){if(e.key==="Enter"){if(ge.count&&ge.count()>0){e.preventDefault();return}if(document.querySelector(".el-dialog")&&Array.from(document.querySelectorAll(".el-dialog")).some(t=>t.style.display!=="none")){e.preventDefault();return}if(console.log(e),this.showClusterConfig){this.$refs.clusterConfigBtn.$el.click();return}if(!this.showAboutUs&&!this.throttle){if(this.throttle=!0,setTimeout(()=>{this.throttle=!1},1e3),this.showForgetPwdDialog){this.mfaMethods.length>0&&this.user.login_method==="pwd"&&this.$refs.mfapwd.$refs.forgetPwd.$refs.forgetPwdBtn.$el.click(),this.mfaMethods.length===0&&this.$refs.forgetPwd.$refs.forgetPwdBtn.$el.click();return}if(this.showResetPwdDialog){this.$refs.resetPwd.$refs.resetPwdBtn.$el.click();return}if(this.mfaMethods.length>0&&this.user.login_method==="pwd"){if(this.$refs.mfapwd.$refs.confirmBtn.disabled){console.log("mfa pwd login disabled");return}this.$refs.mfapwd.$refs.confirmBtn.$el.click();return}if(this.user.login_method==="sms_otp"){if(this.$refs.smsOtp.$refs.confirmBtn.disabled){console.log("mfa sms login disabled");return}this.$refs.smsOtp.$refs.confirmBtn.$el.click();return}if(this.$refs.unPollLoginMethod){if(this.$refs.unPollLoginMethod.$refs.nextStepBtn){if(this.$refs.unPollLoginMethod.$refs.nextStepBtn.disabled){console.log(`mfa ${this.user.login_method} login disabled`);return}this.$refs.unPollLoginMethod.$refs.nextStepBtn.$el.click();return}if(this.$refs.unPollLoginMethod.$refs.confirmBtn){if(this.$refs.unPollLoginMethod.$refs.confirmBtn.disabled){console.log(`mfa ${this.user.login_method} login disabled`);return}this.$refs.unPollLoginMethod.$refs.confirmBtn.$el.click();return}return}if(this.$refs.pollLoginMethod){if(this.$refs.pollLoginMethod.$refs.nextStepBtn){if(this.$refs.pollLoginMethod.$refs.nextStepBtn.disabled){console.log(`mfa ${this.user.login_method} login disabled`);return}this.$refs.pollLoginMethod.$refs.nextStepBtn.$el.click();return}return}this.clickLogin(this.user.username,"normal",!0)}}},async initData(e,t){this.retryTimes=typeof e=="number"?e:1,this.currentTimes=typeof t=="number"?t:1,window.TMP_USERS&&CONFIG.selected_tenant&&CONFIG.selected_tenant.code&&window.TMP_USERS[CONFIG.selected_tenant.code]?(this.user=window.TMP_USERS[CONFIG.selected_tenant.code],this.user.persistent=!0):CONFIG.selected_tenant&&CONFIG.selected_tenant.code&&CONFIG.users&&CONFIG.users[CONFIG.selected_tenant.code]?(this.user=CONFIG.users[CONFIG.selected_tenant.code],this.user.persistent=!0):this.user={username:"",password:"",remember_me:!1,auto_connect:!1,secret:"",login_method:"pwd"};let r=async(a,s)=>{if(console.log("network_error_callback "+s),s==="Connection failed"||s==="Internal Server Error"){await a.changeToNextClusterNode("initData")?await a.initData(e,t):(!a.connectState.state&&a.$store.commit("setConnectState",{state:!0,title:"服务器连接异常"}),O("plugin:cluster|reload"));return}a.currentTimes<a.retryTimes?setTimeout(async()=>{await a.initData(a.retryTimes,a.currentTimes+1)},500*Math.pow(2,a.currentTimes)):a.currentTimes>=a.retryTimes&&a.$router.push({path:"/login",query:{date:new Date().getTime()}}),!a.connectState.state&&a.$store.commit("setConnectState",{state:!0,title:"服务器连接异常"})};this.loading=!0,N("log",{level:"Trace",message:"initdata: true"});try{if(this.tenants=await this.loadTenants(),this.$store.commit("setConnectState",{state:!1,title:""}),!CONFIG.selected_tenant||!CONFIG.selected_tenant.name){if(this.tenants.length>0){let a=this.tenants.filter(s=>s.code==="DEFAULT");window.CONFIG=await O("patch",{value:{selected_tenant:{code:a[0].code,name:a[0].name}}})}}else CONFIG.selected_tenant&&CONFIG.selected_tenant.name&&this.tenants.forEach(async a=>{a.code===CONFIG.selected_tenant.code&&(CONFIG.selected_tenant.name=a.name,window.CONFIG=await O("patch",{value:{selected_tenant:{code:a.code,name:a.name}}}))});this.setCurrentTenant(),await this.linkedLogin(r),this.slideshowFun()}catch(a){this.loading=!1,N("log",{level:"Error",message:"tenant list: "+JSON.stringify(a)}),N("log",{level:"Trace",message:"initdata catch: false"}),await r(this,a)}},async linkedLogin(e){let t=await O("has_logged_in");if(N("log",{level:"Trace",message:"login with: "+location.hash+", alreadyExecutedLogin: "+t}),t)await this.loadAuthStrategy(e);else{let r=async a=>{a.linkedLoginToken=null,await a.loadAuthStrategy(e)};ha().then(async a=>{if(a&&a.data&&a.data.type==="TFA")if(this.linkedLoginToken=a.data.ticket,this.linkedLoginSignature=a.data.signature,this.tenants.length>0){let s=this.tenants.find(n=>n.code===a.data.tenant);if(s){if(s.code!==CONFIG.selected_tenant.code||this.user.username!==a.data.username){this.user={secret:null,username:a.data.username,password:null,login_method:"pwd",remember_me:!1,auto_connect:!1};let n={};n.users={},n.users[a.data.tenant]=this.user,n.selected_tenant={code:s.code,name:s.name},window.CONFIG=await O("patch",{value:n}),this.setCurrentTenant()}await this.clickLogin(a.data.username,"link",!1)}else await r(this)}else await r(this);else await r(this)}).catch(async()=>{await r(this)}).finally(async()=>{await O("set_logged_in")})}},async loadTenants(){return new Promise((e,t)=>{CONFIG.tenants&&CONFIG.tenants.length>0?nn().then(async a=>{this.secret_key=await this.PublicKeyInit(),O("patch",{value:{tenants:a.data,secret_key:this.secret_key}}).then(s=>{window.CONFIG=s}),e(a.data)}).catch(a=>{t(a)}):nn().then(async a=>{this.secret_key=await this.PublicKeyInit(),O("patch",{value:{tenants:a.data,secret_key:this.secret_key}}).then(s=>{window.CONFIG=s}),e(a.data)}).catch(a=>{t(a)})})},async PublicKeyInit(){return new Promise((e,t)=>{yn().then(r=>{e(r.data)}).catch(r=>{t(r)})})},errorLog(e){fa(e).then(t=>{console.log("res:认证失败错误日志 ",t)})},async loadAuthStrategy(e){this.loading=!0,N("log",{level:"Trace",message:"loadAuthStrategy: true"});try{this.user.authResultId="";let r=await sn();if(console.log(r,"策略"),!r||!r.data)this.mfaMethods=[],await this.autoLogin();else if(r.code!=="SUCCESS")this.alert.error("系统错误");else if(!r.data.enabled||r.data.auth)await this.autoLogin();else switch(r.data.decision){case"PERMIT":this.alert.error(r.data);break;case"DENY":this.mfaDenyLogin();break;case"MFA":this.authResultId=r.data.id,this.participantGroupId=r.data.participantGroups[0].id;let s=r.data.participantGroups[0].participants[0].mfaMethods;this.mfaMethods=[...s],await this.autoLogin("mfa")}}catch(r){r.messageKey==="MFA.CHECK.DENY"&&await this.autoLogin(),N("log",{level:"Error",message:"login strategy: "+JSON.stringify(r)}),typeof e=="function"?await e(this,r):this.$store.commit("setConnectState",{state:!0,title:"服务器连接异常"})}N("log",{level:"Trace",message:"loadAuthStrategy: false"});let t=this;t.authStrategyTask||(N("log",{level:"Trace",message:"Enable the scheduled strategy loading task"}),t.authStrategyTask=setInterval(async()=>{await t.silenceLoadAuthStrategy(function(r,a){N("log",{level:"Error",message:"Authentication policy loading failed: "+JSON.stringify(a)})})},10*60*1e3)),this.loading=!1},async silenceLoadAuthStrategy(e){N("log",{level:"Trace",message:"silenceLoadAuthStrategy: true"});try{let t=await sn();if(console.log(t,"静默加载策略"),!(!t||!t.data)){if(t.code==="SUCCESS"){if(!(!t.data.enabled||t.data.auth))switch(t.data.decision){case"PERMIT":this.alert.error(t.data);break;case"DENY":this.mfaDenyLogin();break;case"MFA":this.authResultId=t.data.id,this.participantGroupId=t.data.participantGroups[0].id;let a=t.data.participantGroups[0].participants[0].mfaMethods;this.mfaMethods=a}}}}catch(t){N("log",{level:"Error",message:"silence login strategy: "+JSON.stringify(t)}),typeof e=="function"&&await e(this,t)}N("log",{level:"Trace",message:"silenceLoadAuthStrategy: false"})},async changeTenant(e){this.tenant&&this.tenant.code===e.code||(CONFIG.selected_tenant&&CONFIG.selected_tenant.code&&(this.user.remember_me||(this.user.password=null),window.TMP_USERS||(window.TMP_USERS={}),window.TMP_USERS[CONFIG.selected_tenant.code]=this.user),this.$refs.qrcode&&this.$refs.qrcode.cancel(),this.$refs.pollLoginMethod&&this.$refs.pollLoginMethod.cancel(),window.CONFIG=await O("patch",{value:{selected_tenant:{code:e.code,name:e.name}}}),this.setCurrentTenant(),this.resetState(),this.loading=!0,await this.initData(1,1),this.loading=!1)},changeAutoConnect(){this.user.remember_me=!0},changeRemember(){this.user.remember_me&&(this.user.auto_connect=!1)},handleCommand(e){this.changeTenant(e)},async autoLogin(e){let t=await O("has_logged_in");if(this.user.auto_connect&&!t){N("log",{level:"Info",message:"Auto login"});let r=this;setTimeout(function(){e==="mfa"?r.$refs.mfapwd.login():r.clickLogin(r.user.username,"normal",!1).then(()=>{})},500)}},async clickLogin(e,t,r){if(r&&this.loading)return;if(!this.tenant||!this.tenant.code){this.alert.error("请选择单位");return}if(!e&&t!=="link"){this.alert.error("请输入用户名");return}if(!this.user.password&&this.user.login_method==="pwd"&&t==="normal"){this.alert.error("请输入密码");return}this.loading=!0,await N("log",{level:"Trace",message:"clicklogin: true"});let a=this;t==="normal"&&!await this.getBindCode()||xc(e,this.user.secret,this.user.password,this.tenant.code,t==="normal"?this.user.login_method:t,t==="link"?this.linkedLoginToken:null,t==="link"?this.linkedLoginSignature:null,this.user.authResultId||"").then(s=>{a.$store.commit("updateIsLogin",!0);let n=function(){if(a.$store.commit("setUsername",e),t!=="link"){let u={};a.user.remember_me||(a.user.password=null),u[a.tenant.code]=a.user,O("patch",{value:{users:u}}).then(b=>{window.CONFIG=b})}a.$store.commit("setConnectState",{state:!1,title:"服务器连接异常"}),a.$router.push("/"),a.loading=!1};if(s.payload.expire_day!=2147483647&&s.payload.expire_day<=s.payload.pwd_tip_day){let u=s.payload.expire_day===0?"您的密码将在今天过期":"您的密码还有"+s.payload.expire_day+"天过期!，请及时修改密码";a.$confirm(u,"过期提示",{confirmButtonText:"修改密码",cancelButtonText:"跳过",class:"logout",roundButton:!0,closeOnClickModal:!1,closeOnPressEscape:!1,center:!0}).then(()=>{a.$nextTick(()=>{a.$refs.resetPwd.init({secret:a.user.secret,verifyCode:"",username:a.user.username}),a.openResetPwdDialog()}),a.loading=!1}).catch(()=>{n()})}else n()}).catch(async s=>{console.log(s,"登录失败"),this.loading=!1,N("log",{level:"Trace",message:"clicklogin login failed: false"}),t!=="link"&&await this.throwError(s)})},async throwError(e){let t=this;if(e.type==="NEED_FORCE_PASSWORD")return await this.$alert('<div style="padding: 0 50px">'+e.tip+"</div>","重置密码",{confirmButtonText:"确定",roundButton:!0,center:!0,dangerouslyUseHTMLString:!0,callback:r=>{r==="confirm"&&t.$nextTick(()=>{t.$refs.resetPwd.init({secret:t.user.secret,verifyCode:e.verifyCode,username:t.user.username}),t.openResetPwdDialog()})}}),!1;if(e.type==="PASSWORD_WILL_EXPIRED"){let r=e.expireDay===0?"您的密码将在今天过期":"您的密码还有"+e.expireDay+"天过期!，请及时修改密码";try{return await t.$confirm(r,"过期提示",{confirmButtonText:"修改密码",cancelButtonText:"跳过",class:"logout",roundButton:!0,closeOnClickModal:!1,closeOnPressEscape:!1,center:!0}),t.$nextTick(()=>{t.$refs.resetPwd.init({secret:t.user.secret,verifyCode:"",username:t.user.username}),t.openResetPwdDialog()}),t.loading=!1,!1}catch{return!0}}else if(e.code==602)this.alert.error("服务器地址为空"),this.$router.push({name:"setting",query:{date:new Date().getTime()},params:{item:"endpoint"}});else if(e.code==604)this.alert.error("绑定码为空"),this.$router.push({name:"setting",query:{date:new Date().getTime()},params:{item:"client"}});else if(e.code==603)this.alert.error("域名解析失败");else if(e.code==607||e.code==608||e.code==609)this.alert.error("认证失败");else if(e.code==601){let r=JSON.parse(e.msg);r.code==="APP_FAILED"&&(this.user.remember_me=!1,this.user.auto_connect=!1,this.user.password=""),this.alert.error(r.data.errorMsg)}else if(e.code===606||e.code===610)this.alert.error("连接超时");else{this.user.secret=null;let r={};r[this.tenant.code]=this.user,O("patch",{value:{users:r}}).then(a=>{window.CONFIG=a}),this.alert.error("网络连接异常, 请稍后再试"),await this.changeToNextClusterNode("connect_ctl")&&await this.initData(1,1);return}await this.loadAuthStrategy()},async changeToNextClusterNode(e){if(await N("log",{level:"Debug",message:"Call changeToNextClusterNode: "+e+", length: "+CONFIG.cluster_nodes.length+", "+CONFIG.cluster_node_changed_times}),CONFIG.cluster_nodes&&CONFIG.cluster_nodes.length>1){let t=CONFIG.cluster_nodes.length;if(CONFIG.cluster_node_changed_times>t)return!1;let r=0;for(const s in CONFIG.cluster_nodes){r+=1;let n=CONFIG.cluster_nodes[s];if(CONFIG.node.ip===n.ip&&CONFIG.node.port===n.port&&CONFIG.node.spa_port===n.spa_port)break}let a=CONFIG.cluster_node_changed_times+1;return window.CONFIG=await O("patch",{value:{cluster_node_changed_times:a,node:CONFIG.cluster_nodes[r%t]}}),await N("log",{level:"Debug",message:"Change to next node: "+JSON.stringify(CONFIG.node)}),!0}return!1},showClusterConfigDialogCancelBtn(){return CONFIG.cluster_config_url||CONFIG.cluster_external_config_url},openClusterConfigDialog(){var e=/^(?:([A-Za-z]+):)?(\/{0,3})([0-9.\-A-Za-z]+)(?::(\d+))?(?:\/([^?#]*))?(?:\?([^#]*))?(?:#(.*))?$/;if(CONFIG.cluster_config_url){var t=e.exec(CONFIG.cluster_config_url);this.nodeConfig.internal.host=t[1]+"://"+t[3],t[4]&&(this.nodeConfig.internal.host+=":"+t[4])}if(CONFIG.cluster_external_config_url){var t=e.exec(CONFIG.cluster_external_config_url);this.nodeConfig.external.host=t[1]+"://"+t[3],t[4]&&(this.nodeConfig.external.host+=":"+t[4])}this.$store.commit("setClusterConfigVisible",!0)},closeClusterConfigDialog(){this.$store.commit("setClusterConfigVisible",!1)},async updateClusterConfigUrl(){this.loading=!0;var e={};this.nodeConfig.internal.host&&(e.internal=this.nodeConfig.internal.host+this.nodeConfig.internal.uri),this.nodeConfig.external.host&&(e.external=this.nodeConfig.external.host+this.nodeConfig.external.uri),O("plugin:cluster|reload_cluster_nodes",{config:e}).then(async t=>{this.loading=!1,delete t.selected_tenant,window.CONFIG=t,this.$message({message:"保存成功",type:"success"}),this.$store.commit("setClusterConfigVisible",!1),await this.initData(1,1)}).catch(()=>{this.loading=!1,this.$message({message:"服务器连接失败，请稍后重试",type:"error"})})},openForgetPwdDialog(){this.showForgetPwdDialog=!0,this.$refs.forgetPwd.init()},closeForgetPwdDialog(){this.showForgetPwdDialog=!1},openResetPwdDialog(){this.showResetPwdDialog=!0},closeResetPwdDialog(){this.showResetPwdDialog=!1},resetOrForgetPwdCallback(e){this.alert.success(e);let t={};this.user.password=null,this.user.remember_me=!1,this.user.auto_connect=!1,t[this.tenant.code]={...this.user},O("patch",{value:{users:t}}).then(r=>{window.CONFIG=r}).finally(()=>{this.reload()})},changeLoginMethod(e){this.$refs.pollLoginMethod&&this.$refs.pollLoginMethod.cancel(),this.$refs.unPollLoginMethod&&this.$refs.unPollLoginMethod.cancel&&this.$refs.unPollLoginMethod.cancel(),this.$refs.qrcode&&this.$refs.qrcode.cancel(),this.user.login_method=e},cutMethods(e){e=="pwd"&&this.$refs.qrcode&&this.$refs.qrcode.cancel(),this.user.login_method=e},slideshowFun(){ma().then(e=>{console.log("result:获取轮播图 ",e),this.AllBanners=e.data?.loginBanners.split(","),this.AllBanners&&this.AllBanners.length===1?this.position="none":this.position=""})},closeEvent(){this.$refs.pollLoginMethod&&this.$refs.pollLoginMethod.cancel(),this.$refs.unPollLoginMethod&&this.$refs.unPollLoginMethod.cancel&&this.$refs.unPollLoginMethod.cancel(),this.mfaContains("pwd")?this.user.login_method="pwd":this.mfaContains("sms_otp")?this.user.login_method="sms_otp":this.mfaContains("qrcode")?this.user.login_method="qrcode":this.mfaMethods.length&&(this.user.login_method=this.mfaMethods[0].method.code)},async mfaLoginCallback(e,t){await this.getBindCode()&&await this.doSecondAuth(e,t)},async doSecondAuth(e,t){let r,a=this.mfaMethods.filter(s=>s.method.code==="pwd");try{if(t&&a.length&&this.mfaMethods.length==1?r=await pa({username:t.username,password:t.password,verifyCode:"",authResultId:this.authResultId}):r=await ga({authResultId:this.authResultId,fistRequestCode:this.fistRequestCode}),(r?.data?.type==="NEED_FORCE_PASSWORD"||r?.data?.type==="PASSWORD_WILL_EXPIRED")&&(this.loading=!1,!await this.throwError(r?.data)))return;r?.data?.needSecondAuth?(this.fistRequestCode=r.data.fistRequestCode,this.authResultId=r.data.secondMfa.id,this.$nextTick(()=>{this.$refs.mfaMode.init({mfaTypes:r.data.secondMfa.participantGroups[0].participants[0].mfaMethods,authResultId:r.data.secondMfa.id,participantGroupId:r.data.secondMfa.participantGroups[0].id,username:r.data.firstAuthUsername})})):r?.data?.USER_LOGIN_DEVICE_LIMIT?(this.userId=r.data.userId,this.trustParams={request:e,user:t,name:r?.data?.userName},this.limitNumber=r.data.USER_LOGIN_DEVICE_LIMIT_NUMBER,this.exceedingTerminalTips()):this.gatewayLogin(e,t,r?.data?.userName)}catch{this.loading=!1}},closeMfaLogin(){this.loading=!1,this.loadAuthStrategy()},gatewayLogin(e,t,r){this.user.username=r||t?.username,t?.password&&(this.user.password=t.password),this.user.authResultId=this.authResultId,this.clickLogin(this.user.username,"mfa",!1)},async getBindCode(){if(!this.user.secret)if(this.authResultId)try{let e=await Ca({authResultId:this.authResultId});const t="3146d44927b057e26f90adf3ee51e3c2297c1cf632b2744fa18ebf0b351ae129";return this.user.secret=Gt.sm2.doDecrypt(e.data,t,0),!0}catch(e){return this.loading=!1,await N("log",{level:"Trace",message:"clicklogin strategy failed: false"}),this.alert.error(e.data&&e.data.errorMsg||"绑定码获取失败, 请稍后再试"),await this.loadAuthStrategy(),!1}else try{let e=await ba("","03",this.user.username,"pwd");try{let t=await _a({requestId:e.data});if(t.data){const r="3146d44927b057e26f90adf3ee51e3c2297c1cf632b2744fa18ebf0b351ae129";return this.user.secret=Gt.sm2.doDecrypt(t.data,r,0),!0}else return this.loading=!1,N("log",{level:"Trace",message:"clicklogin no requestid: false"}),this.user.password="",type==="link"?await this.loadAuthStrategy():this.alert.error("用户名或密码错误"),!1}catch(t){return this.loading=!1,N("log",{level:"Trace",message:"clicklogin request failed: false"}),this.alert.error(t.data&&t.data.errorMsg||"绑定码获取失败, 请稍后再试"),!1}}catch{return this.alert.error("网络连接异常, 请稍后再试"),this.loading=!1,N("log",{level:"Trace",message:"clicklogin request catch: false"}),type==="link"&&await this.loadAuthStrategy(),!1}return!0},exceedingTerminalTips(){this.$confirm("当前终端已超出登录终端数量限制，<br/>是否需要移除已有终端？","提示",{confirmButtonText:"是",cancelButtonText:"否",roundButton:!0,center:!0,closeOnClickModal:!1,closeOnPressEscape:!1,dangerouslyUseHTMLString:!0}).then(()=>{this.showTerminalConfig=!0}).catch(()=>{this.loading=!1,this.loadAuthStrategy()})},isAuthenticationTips(e){this.$confirm("为保障您的设备安全，现已启动二次认证机制。<br/>完成认证后，才登录成功。","温馨提示",{confirmButtonText:"认证",cancelButtonText:"取消",roundButton:!0,center:!0,closeOnClickModal:!1,closeOnPressEscape:!1,dangerouslyUseHTMLString:!0}).then(()=>{this.fistRequestCode=e.data.fistRequestCode,this.authResultId=e.data.secondMfa.id,this.$nextTick(()=>{this.$refs.mfaMode.init({mfaTypes:e.data.secondMfa.participantGroups[0].participants[0].mfaMethods,authResultId:e.data.secondMfa.id,participantGroupId:e.data.secondMfa.participantGroups[0].id,username:e.data.firstAuthUsername})})}).catch(()=>{this.loading=!1,this.loadAuthStrategy()})},handleClose(){this.loading=!1,this.showTerminalConfig=!1,this.loadAuthStrategy()},handleSave(){let{request:e,user:t,name:r}=this.trustParams;wa({userId:this.userId}).then(a=>{this.gatewayLogin(e,t,r)}).catch(a=>{this.loading=!1,this.showTerminalConfig=!1,this.loadAuthStrategy()})},generateRequestIdFailCallback(e){console.log(e,"generateRequestFailed"),this.loading=!1,this.alert.error(e?.data?.errorMsg||"登录失败，请重试"),e&&e.data&&e.data.messageKey==="MFA.MFA.AUTH.TIMEOUT"?this.reload():e&&e.data&&e.data.messageKey==="MFA.USERNAME.OR.PASSWORD.ERROR"&&(this.user.password="",this.user.remember_me=!1,this.user.auto_connect=!1)},mfaDenyLogin(){this.$alert("禁止操作","错误提示",{confirmButtonText:"确定",roundButton:!0,showClose:!1,center:!0,callback:()=>{this.reload()}})},refresh(){var e={};this.nodeConfig.internal.host&&(e.internal=this.nodeConfig.internal.host+this.nodeConfig.internal.uri),this.nodeConfig.external.host&&(e.external=this.nodeConfig.external.host+this.nodeConfig.external.uri),O("plugin:cluster|reload_cluster_nodes",{config:e}).finally(()=>{this.reload()})},groupOfThrees(e){const t=[];for(let r=0;r<e.length;r+=3)t.push(e.slice(r,r+3));return t},resetSettings(){this.$confirm("是否恢复出厂设置？","恢复出厂设置",{confirmButtonText:"确认",cancelButtonText:"取消",roundButton:!0,center:!0,closeOnClickModal:!1,closeOnPressEscape:!1}).then(()=>{delete window.TMP_USERS,O("reset").then(e=>{window.CONFIG=e,this.resetState(),this.$message({type:"success",message:"恢复出厂设置成功"}),this.reload()}).catch(()=>{this.$message({type:"error",message:"恢复出厂设置失败"})})}).catch(()=>{})},openAboutUsDialog(){this.$store.commit("setAboutUsVisible",!0)},closeAboutUsDialog(){this.$store.commit("setAboutUsVisible",!1)},async check(){this.loading_text="检查中",this.loading=!0;let e=await this.checkUpdate(!0,5e3);e&&e.shouldUpdate&&this.$nextTick(()=>{this.$refs.updater.init(e)}),this.loading=!1,this.loading_text="正在退出..."},copyDeviceId(){if(navigator.clipboard&&navigator.clipboard.writeText)navigator.clipboard.writeText(window.DEVICE_ID).then(()=>{this.alert.success("已复制到剪贴板")}).catch(e=>{this.alert.error("复制失败："+e)});else{const e=document.createElement("textarea");e.value=window.DEVICE_ID,e.style.position="absolute",e.style.left="-9999px",document.body.appendChild(e),e.select();try{document.execCommand("copy"),this.alert.success("已复制到剪贴板")}catch(t){this.alert.error("复制失败："+t)}finally{document.body.removeChild(e)}}},closeChangeTenantTip(){O("patch",{value:{show_change_tenant_tip:!1}}).then(e=>{window.CONFIG=e,this.setCloseTenantTip()})}}},We=e=>(je("data-v-d409866c"),e=e(),Ke(),e),Gc={class:"app-content",style:{display:"flex"}},Nc={class:"login_left"},Vc={class:"login_logo"},Uc=We(()=>y("img",{src:ya,width:"30",height:"30",alt:"",class:"icon"},null,-1)),Hc={class:"carouselClass"},zc={key:1,src:Ac,alt:"",style:{width:"100%",height:"100%"}},Kc={class:"flex w-full h-full justify-center items-center text-6xl login_nav"},jc={class:"select-tenant"},Zc={key:0,class:"tenant-img"},Wc=We(()=>y("span",{style:{"margin-right":"3px"}},"切换单位",-1)),Xc=["title"],Yc={class:"el-dropdown-link"},Qc={class:"dropdownClass"},Jc={class:"nav_right"},$c={class:"nav_right_box"},eu={key:0},tu={key:3,style:{display:"flex","flex-direction":"column","align-items":"center",height:"414px","justify-content":"center"}},nu=We(()=>y("p",{style:{"font-size":"18px","font-weight":"600","text-align":"center","margin-bottom":"20px"}}," 安全令APP扫码登录 ",-1)),su={key:4,class:"login","element-loading-text":"loading_text","element-loading-background":"rgba(0, 0, 0, 0.5)"},ou={key:0},iu=We(()=>y("div",{class:"input_wrapper",style:{margin:"65px 0"}},null,-1)),ru={class:"input_wrapper"},au={class:"inputItem"},lu={class:"inputContent"},cu={class:"inputItem"},uu={class:"inputContent",style:{padding:"6px 14px"}},du={class:"login-form-bottom"},hu={style:{margin:"30px 0 15px"}},fu=["disabled"],mu={key:1},pu={key:0},gu={key:5,class:"login_title"},Cu=We(()=>y("p",{class:"login_title_el"},[y("span",{class:"login_title_el_eles"}," 更多方式 ")],-1)),bu={class:"block",style:{"flex-grow":"1"}},_u=["src","onClick"],wu=["title"],yu={class:"endpointUrl"},xu={class:"endpointUrl"},Fu={class:"dialog-footer"},vu=We(()=>y("h1",{style:{"font-weight":"normal","margin-top":"0"}},"关于我们",-1)),ku={key:0},Iu={key:1},Tu={class:"dialog-footer",style:{"text-align":"center"}};function Mu(e,t,r,a,s,n){const u=g("Setting"),b=g("el-icon"),h=g("el-dropdown-item"),I=g("el-dropdown-menu"),_=g("el-dropdown"),v=g("headerBar"),k=g("el-carousel-item"),T=g("el-carousel"),x=g("Close"),P=g("arrow-down"),A=g("qrcode"),V=g("el-alert"),U=g("el-input"),W=g("el-checkbox"),H=g("el-link"),ie=g("userAgreement"),re=g("mfaPwd"),G=g("el-tab-pane"),we=g("SMSOTP"),ye=g("FACE"),xe=g("FINGERPRINT"),B=g("EMAILOTP"),me=g("VOICE"),J=g("NATIVEPASS"),tt=g("AUTHCODE"),mt=g("MOBILECLICK"),pt=g("TFC200"),gt=g("ANSHUA5"),Ct=g("ESCUOTP1"),bt=g("ETZ203"),_t=g("UKEY"),wt=g("UKEYWQ"),Ce=g("UKEYPKI"),yt=g("FACIAL"),xt=g("el-tabs"),Re=g("el-button"),Xe=g("el-dialog"),Ft=g("DocumentCopy"),vt=g("reset-pwd"),be=g("forget-pwd"),kt=g("updater"),It=g("mfa-mode"),Tt=g("terminal-page"),Mt=qe("banner"),Et=qe("loading");return p(),S("div",Gc,[m(v,{ref:"inswitch"},{default:C(()=>[m(_,null,{dropdown:C(()=>[m(I,null,{default:C(()=>[m(h,{onClick:n.openClusterConfigDialog},{default:C(()=>[q("服务器设置 ")]),_:1},8,["onClick"]),m(h,{onClick:n.resetSettings},{default:C(()=>[q("恢复出厂设置 ")]),_:1},8,["onClick"]),m(h,{onClick:n.openAboutUsDialog},{default:C(()=>[q("关于我们 ")]),_:1},8,["onClick"])]),_:1})]),default:C(()=>[a.isSettings?M("",!0):(p(),E(b,{key:0,class:"title-icon",size:20},{default:C(()=>[m(u)]),_:1}))]),_:1})]),_:1},512),y("div",Nc,[y("div",Vc,[Uc,y("h1",null,"欢迎使用"+Y(s.productName),1)]),y("div",Hc,[s.AllBanners&&s.AllBanners.length?(p(),E(T,{key:0,arrow:"never","indicator-position":s.position},{default:C(()=>[(p(!0),S(Ie,null,Te(s.AllBanners,L=>(p(),E(k,{key:L},{default:C(()=>[de(y("img",{style:{width:"100%",height:"100%"},onError:t[0]||(t[0]=ne=>ne.target.src="/assets/images/defaultLogo.png")},null,544),[[Mt,L]])]),_:2},1024))),128))]),_:1},8,["indicator-position"])):(p(),S("img",zc))])]),y("div",Kc,[y("div",jc,[s.show_change_tenant_tip&&s.tenants.length>1?(p(),S("div",Zc,[Wc,m(b,{style:{cursor:"pointer"},onClick:n.closeChangeTenantTip},{default:C(()=>[m(x)]),_:1},8,["onClick"])])):M("",!0),s.tenants.length>1?(p(),S("div",{key:1,class:"remould_select",title:s.tenant.name},[m(_,{class:"dropdown-link",trigger:"click",onCommand:t[2]||(t[2]=L=>n.handleCommand(L,e.row))},{dropdown:C(()=>[m(I,null,{default:C(()=>[(p(!0),S(Ie,null,Te(s.tenants,L=>(p(),E(h,{class:Se(["dropdownStyle",{selected:s.tenant.name==L.name}]),key:L.id,command:L},{default:C(()=>[y("span",Qc,Y(L.name)+" ",1)]),_:2},1032,["command","class"]))),128))]),_:1})]),default:C(()=>[y("span",Yc,[de(y("input",{class:"tenant_input","onUpdate:modelValue":t[1]||(t[1]=L=>s.tenant.name=L),type:"text",readonly:""},null,512),[[Dt,s.tenant.name]]),m(b,{class:"el-icon--right"},{default:C(()=>[m(P)]),_:1})])]),_:1})],8,Xc)):M("",!0)]),y("div",Jc,[y("div",$c,[n.mfaContains("qrcode")||n.mfaContains("sms_otp")||n.mfaContains("pwd")?(p(),S("div",eu,[s.user.login_method!=="qrcode"&&s.user.login_method!=="sms_otp"&&s.user.login_method!=="pwd"?(p(),E(b,{key:0,class:"nav_right_icon",size:20,onClick:n.closeEvent},{default:C(()=>[m(x)]),_:1},8,["onClick"])):M("",!0)])):M("",!0),n.mfaContains("qrcode")&&(s.user.login_method=="pwd"||s.user.login_method=="sms_otp")?(p(),S("img",{key:1,class:"nav_right_qrcode",onClick:t[3]||(t[3]=L=>n.cutMethods("qrcode")),src:qc,alt:"",width:"50"})):M("",!0),s.user.login_method==="qrcode"&&(n.mfaContains("pwd")||n.mfaContains("sms_otp"))?(p(),S("img",{key:2,class:"nav_right_pwd",onClick:t[4]||(t[4]=L=>n.cutMethods("pwd")),src:Dc,alt:"",width:"50"})):M("",!0),s.user.login_method==="qrcode"?(p(),S("div",tu,[nu,m(A,{ref:"qrcode","participant-group-id":s.participantGroupId,type:"login",onMfaCallbackFunc:n.mfaLoginCallback,onChildEventCancel:n.errorLog,onGenerateRequestIdFailCallbackFunc:n.generateRequestIdFailCallback},null,8,["participant-group-id","onMfaCallbackFunc","onChildEventCancel","onGenerateRequestIdFailCallbackFunc"])])):de((p(),S("div",su,[a.connectState.state?(p(),E(V,{key:0,class:"alert_error",style:pe(a.connectState.title.length>7?"width: 328px;":"width: 230px;"),title:a.connectState.title,type:"error","close-text":"点击刷新",onClose:n.refresh,"show-icon":""},null,8,["style","title","onClose"])):M("",!0),m(xt,{style:{flex:"1",width:"100%",height:"54px"},modelValue:s.user.login_method,"onUpdate:modelValue":t[10]||(t[10]=L=>s.user.login_method=L),onTabChange:n.changeLoginMethod},{default:C(()=>[(s.mfaMethods.length===0||n.mfaContains("pwd"))&&(s.user.login_method=="sms_otp"||s.user.login_method=="pwd")?(p(),E(G,{key:0,label:"账号登录",name:"pwd"},{default:C(()=>[s.mfaMethods.length===0?(p(),S("div",ou,[iu,y("div",ru,[y("div",au,[y("div",lu,[de(y("input",{ref:"username","onUpdate:modelValue":t[5]||(t[5]=L=>s.user.username=L),type:"text",placeholder:"请输入用户名"},null,512),[[Dt,s.user.username]])])]),y("div",cu,[y("div",uu,[m(U,{ref:"password",modelValue:s.user.password,"onUpdate:modelValue":t[6]||(t[6]=L=>s.user.password=L),placeholder:"请输入密码","show-password":"",class:"input-pwd"},null,8,["modelValue"])])])]),y("div",du,[m(W,{modelValue:s.user.remember_me,"onUpdate:modelValue":t[7]||(t[7]=L=>s.user.remember_me=L),class:"pull-left",label:"记住密码",onChange:n.changeRemember},null,8,["modelValue","onChange"]),m(W,{modelValue:s.user.auto_connect,"onUpdate:modelValue":t[8]||(t[8]=L=>s.user.auto_connect=L),class:"pull-right",label:"自动登录",onChange:n.changeAutoConnect},null,8,["modelValue","onChange"]),m(H,{class:"login-bottom-item",underline:!1,onClick:n.openForgetPwdDialog},{default:C(()=>[q(" 忘记密码? ")]),_:1},8,["onClick"])]),y("div",hu,[y("button",{ref:"confirmBtn",class:Se({opt_button:!s.disableLoginButton,forbidden:s.disableLoginButton}),disabled:s.disableLoginButton,onClick:t[9]||(t[9]=L=>n.clickLogin(s.user.username,"normal",!0))}," 登录 ",10,fu)]),m(ie)])):(p(),S("div",mu,[s.user.login_method==="pwd"?(p(),S("div",pu,[m(re,{ref:"mfapwd","participant-group-id":s.participantGroupId,type:"login",userObj:s.user,tenant:s.tenants,"disable-login-button":s.disableLoginButton,"show-forget-pwd-dialog":s.showForgetPwdDialog,onLoading:n.startLoading,onMfaCallbackFunc:n.mfaLoginCallback,onOpenForgetPwdDialog:n.openForgetPwdDialog,onCloseForgetPwdDialog:n.closeForgetPwdDialog,onForgetPwdCallback:n.resetOrForgetPwdCallback,onGenerateRequestIdFailCallbackFunc:n.generateRequestIdFailCallback},null,8,["participant-group-id","userObj","tenant","disable-login-button","show-forget-pwd-dialog","onLoading","onMfaCallbackFunc","onOpenForgetPwdDialog","onCloseForgetPwdDialog","onForgetPwdCallback","onGenerateRequestIdFailCallbackFunc"])])):M("",!0)]))]),_:1})):M("",!0),n.mfaContains("sms_otp")&&(s.user.login_method=="sms_otp"||s.user.login_method=="pwd")?(p(),E(G,{key:1,label:"短信登录",name:"sms_otp"},{default:C(()=>[m(we,{ref:"smsOtp",type:"login","participant-group-id":s.participantGroupId,onMfaCallbackFunc:n.mfaLoginCallback,onGenerateRequestIdFailCallbackFunc:n.generateRequestIdFailCallback,onChildEventCancel:n.errorLog},null,8,["participant-group-id","onMfaCallbackFunc","onGenerateRequestIdFailCallbackFunc","onChildEventCancel"])]),_:1})):M("",!0),n.mfaContains("face")?(p(),E(G,{key:2,name:"face"},{default:C(()=>[m(ye,{ref:"pollLoginMethod",type:"login","participant-group-id":s.participantGroupId,name:s.user.username,onMfaCallbackFunc:n.mfaLoginCallback,onGenerateRequestIdFailCallbackFunc:n.generateRequestIdFailCallback,onChildEventCancel:n.errorLog},null,8,["participant-group-id","name","onMfaCallbackFunc","onGenerateRequestIdFailCallbackFunc","onChildEventCancel"])]),_:1})):M("",!0),n.mfaContains("fingerprint")?(p(),E(G,{key:3,name:"fingerprint"},{default:C(()=>[m(xe,{ref:"pollLoginMethod",type:"login","participant-group-id":s.participantGroupId,name:s.user.username,onMfaCallbackFunc:n.mfaLoginCallback,onGenerateRequestIdFailCallbackFunc:n.generateRequestIdFailCallback,onChildEventCancel:n.errorLog},null,8,["participant-group-id","name","onMfaCallbackFunc","onGenerateRequestIdFailCallbackFunc","onChildEventCancel"])]),_:1})):M("",!0),n.mfaContains("email_otp")?(p(),E(G,{key:4,name:"email_otp"},{default:C(()=>[m(B,{ref:"unPollLoginMethod",type:"login","participant-group-id":s.participantGroupId,onMfaCallbackFunc:n.mfaLoginCallback,onGenerateRequestIdFailCallbackFunc:n.generateRequestIdFailCallback,onChildEventCancel:n.errorLog},null,8,["participant-group-id","onMfaCallbackFunc","onGenerateRequestIdFailCallbackFunc","onChildEventCancel"])]),_:1})):M("",!0),n.mfaContains("voice")?(p(),E(G,{key:5,name:"voice"},{default:C(()=>[m(me,{ref:"pollLoginMethod",type:"login","participant-group-id":s.participantGroupId,name:s.user.username,onMfaCallbackFunc:n.mfaLoginCallback,onGenerateRequestIdFailCallbackFunc:n.generateRequestIdFailCallback,onChildEventCancel:n.errorLog},null,8,["participant-group-id","name","onMfaCallbackFunc","onGenerateRequestIdFailCallbackFunc","onChildEventCancel"])]),_:1})):M("",!0),n.mfaContains("nativepass")?(p(),E(G,{key:6,name:"nativepass"},{default:C(()=>[m(J,{ref:"pollLoginMethod",type:"login","participant-group-id":s.participantGroupId,name:s.user.username,onMfaCallbackFunc:n.mfaLoginCallback,onGenerateRequestIdFailCallbackFunc:n.generateRequestIdFailCallback,onChildEventCancel:n.errorLog},null,8,["participant-group-id","name","onMfaCallbackFunc","onGenerateRequestIdFailCallbackFunc","onChildEventCancel"])]),_:1})):M("",!0),n.mfaContains("auth_code")?(p(),E(G,{key:7,name:"auth_code"},{default:C(()=>[m(tt,{ref:"unPollLoginMethod",type:"login","participant-group-id":s.participantGroupId,name:s.user.username,onMfaCallbackFunc:n.mfaLoginCallback,onGenerateRequestIdFailCallbackFunc:n.generateRequestIdFailCallback,onChildEventCancel:n.errorLog},null,8,["participant-group-id","name","onMfaCallbackFunc","onGenerateRequestIdFailCallbackFunc","onChildEventCancel"])]),_:1})):M("",!0),n.mfaContains("mobileclick")?(p(),E(G,{key:8,name:"mobileclick"},{default:C(()=>[m(mt,{ref:"pollLoginMethod",type:"login","participant-group-id":s.participantGroupId,name:s.user.username,onMfaCallbackFunc:n.mfaLoginCallback,onGenerateRequestIdFailCallbackFunc:n.generateRequestIdFailCallback,onChildEventCancel:n.errorLog},null,8,["participant-group-id","name","onMfaCallbackFunc","onGenerateRequestIdFailCallbackFunc","onChildEventCancel"])]),_:1})):M("",!0),n.mfaContains("ft_c200")?(p(),E(G,{key:9,name:"ft_c200"},{default:C(()=>[m(pt,{ref:"unPollLoginMethod",type:"login","participant-group-id":s.participantGroupId,name:s.user.username,onMfaCallbackFunc:n.mfaLoginCallback,onGenerateRequestIdFailCallbackFunc:n.generateRequestIdFailCallback,onChildEventCancel:n.errorLog},null,8,["participant-group-id","name","onMfaCallbackFunc","onGenerateRequestIdFailCallbackFunc","onChildEventCancel"])]),_:1})):M("",!0),n.mfaContains("anshu_a5")?(p(),E(G,{key:10,name:"anshu_a5"},{default:C(()=>[m(gt,{ref:"unPollLoginMethod",type:"login","participant-group-id":s.participantGroupId,name:s.user.username,onMfaCallbackFunc:n.mfaLoginCallback,onGenerateRequestIdFailCallbackFunc:n.generateRequestIdFailCallback,onChildEventCancel:n.errorLog},null,8,["participant-group-id","name","onMfaCallbackFunc","onGenerateRequestIdFailCallbackFunc","onChildEventCancel"])]),_:1})):M("",!0),n.mfaContains("escuotp1")?(p(),E(G,{key:11,name:"escuotp1"},{default:C(()=>[m(Ct,{ref:"unPollLoginMethod",type:"login","participant-group-id":s.participantGroupId,name:s.user.username,onMfaCallbackFunc:n.mfaLoginCallback,onGenerateRequestIdFailCallbackFunc:n.generateRequestIdFailCallback,onChildEventCancel:n.errorLog},null,8,["participant-group-id","name","onMfaCallbackFunc","onGenerateRequestIdFailCallbackFunc","onChildEventCancel"])]),_:1})):M("",!0),n.mfaContains("et_z203")?(p(),E(G,{key:12,name:"et_z203"},{default:C(()=>[m(bt,{ref:"unPollLoginMethod",type:"login","participant-group-id":s.participantGroupId,name:s.user.username,onMfaCallbackFunc:n.mfaLoginCallback,onGenerateRequestIdFailCallbackFunc:n.generateRequestIdFailCallback,onChildEventCancel:n.errorLog},null,8,["participant-group-id","name","onMfaCallbackFunc","onGenerateRequestIdFailCallbackFunc","onChildEventCancel"])]),_:1})):M("",!0),s.user.login_method==="ukey_bjca"?(p(),E(G,{key:13,name:"ukey_bjca"},{default:C(()=>[m(_t,{ref:"unPollLoginMethod","participant-group-id":s.participantGroupId,type:"login",name:s.user.username,onMfaCallbackFunc:n.mfaLoginCallback,onGenerateRequestIdFailCallbackFunc:n.generateRequestIdFailCallback,onChildEventCancel:n.errorLog},null,8,["participant-group-id","name","onMfaCallbackFunc","onGenerateRequestIdFailCallbackFunc","onChildEventCancel"])]),_:1})):M("",!0),s.user.login_method==="ukey_wq"?(p(),E(G,{key:14,name:"ukey_wq"},{default:C(()=>[m(wt,{ref:"unPollLoginMethod","participant-group-id":s.participantGroupId,type:"login",name:s.user.username,onMfaCallbackFunc:n.mfaLoginCallback,onGenerateRequestIdFailCallbackFunc:n.generateRequestIdFailCallback,onChildEventCancel:n.errorLog},null,8,["participant-group-id","name","onMfaCallbackFunc","onGenerateRequestIdFailCallbackFunc","onChildEventCancel"])]),_:1})):M("",!0),s.user.login_method==="ukey"?(p(),E(G,{key:15,name:"ukey"},{default:C(()=>[m(Ce,{ref:"unPollLoginMethod","participant-group-id":s.participantGroupId,type:"login",name:s.user.username,onMfaCallbackFunc:n.mfaLoginCallback,onGenerateRequestIdFailCallbackFunc:n.generateRequestIdFailCallback,onChildEventCancel:n.errorLog},null,8,["participant-group-id","name","onMfaCallbackFunc","onGenerateRequestIdFailCallbackFunc","onChildEventCancel"])]),_:1})):M("",!0),n.mfaContains("facial_recognition")?(p(),E(G,{key:16,name:"facial_recognition"},{default:C(()=>[m(yt,{ref:"unPollLoginMethod","participant-group-id":s.participantGroupId,type:"login",name:s.user.username,tenant:s.tenant,onMfaCallbackFunc:n.mfaLoginCallback,onGenerateRequestIdFailCallbackFunc:n.generateRequestIdFailCallback,onChildEventCancel:n.errorLog},null,8,["participant-group-id","name","tenant","onMfaCallbackFunc","onGenerateRequestIdFailCallbackFunc","onChildEventCancel"])]),_:1})):M("",!0)]),_:1},8,["modelValue","onTabChange"])])),[[Et,s.loading,void 0,{fullscreen:!0,lock:!0}]]),n.carouselMfaGroupMethods.length?(p(),S("div",gu,[Cu,y("div",bu,[m(T,{trigger:"click",class:"approveTab",height:"80px",autoplay:!1,loop:!1,arrow:n.carouselMfaGroupMethods.length>1?"always":"never","initial-index":n.carouselMethodIndex},{default:C(()=>[(p(!0),S(Ie,null,Te(n.carouselMfaGroupMethods,L=>(p(),E(k,{key:L,class:""},{default:C(()=>[(p(!0),S(Ie,null,Te(L,ne=>(p(),S("div",{key:ne.method.code,class:"carouselCode"},[y("img",{width:"40",height:"40",alt:"",src:s.user.login_method!==ne.method.code?"assets/images/defaultlogin/"+ne.method.code+".png":"/assets/images/mfalogin/"+ne.method.code+".png",onClick:Wt=>n.changeLoginMethod(ne.method.code),style:pe(s.user.login_method===ne.method.code&&"pointer-events: none;")},null,12,_u),y("span",{title:ne.method.name},Y(ne.method.name),9,wu)]))),128))]),_:2},1024))),128))]),_:1},8,["arrow","initial-index"])])])):M("",!0)])])]),m(Xe,{modelValue:a.showClusterConfig,"onUpdate:modelValue":t[13]||(t[13]=L=>a.showClusterConfig=L),title:"服务器设置",width:"60%",center:"","close-on-click-modal":!1,"close-on-press-escape":!1,"show-close":!1},{footer:C(()=>[y("span",Fu,[n.showClusterConfigDialogCancelBtn()?(p(),E(Re,{key:0,round:"",style:{padding:"20px 35px","line-height":"0"},onClick:n.closeClusterConfigDialog},{default:C(()=>[q("取消")]),_:1},8,["onClick"])):M("",!0),m(Re,{ref:"clusterConfigBtn",round:"",style:{padding:"20px 35px","line-height":"0"},type:"primary",disabled:!s.nodeConfig.internal.host&&!s.nodeConfig.external.host,onClick:n.updateClusterConfigUrl},{default:C(()=>[q(" 确认 ")]),_:1},8,["disabled","onClick"])])]),default:C(()=>[y("div",yu,[m(U,{modelValue:s.nodeConfig.internal.host,"onUpdate:modelValue":t[11]||(t[11]=L=>s.nodeConfig.internal.host=L),class:"input-text",placeholder:"服务器内网连接地址",clearable:""},null,8,["modelValue"])]),y("div",xu,[m(U,{modelValue:s.nodeConfig.external.host,"onUpdate:modelValue":t[12]||(t[12]=L=>s.nodeConfig.external.host=L),class:"input-text",placeholder:"服务器外网连接地址",clearable:""},null,8,["modelValue"])])]),_:1},8,["modelValue"]),m(Xe,{modelValue:a.showAboutUs,"onUpdate:modelValue":t[15]||(t[15]=L=>a.showAboutUs=L),width:"50%","before-close":n.closeAboutUsDialog,"close-on-click-modal":!1,"close-on-press-escape":!1,class:"AboutUs"},{default:C(()=>[vu,y("p",null,[a.hasNewVersion?(p(),S("span",ku,"发现新版本 : "+Y(a.productVersion),1)):(p(),S("span",Iu,"当前已是最新版本 : "+Y(a.productVersion),1)),q("  "),m(Re,{plain:"",onClick:n.check,style:{border:"1px solid #f87f1a",color:"#f87f1a"}},{default:C(()=>[q(Y(a.hasNewVersion?"立即更新":"检查更新"),1)]),_:1},8,["onClick"])]),y("p",null,[q(" 设备ID : "+Y(a.deviceId)+"   ",1),m(b,{style:{color:"#f9780c",cursor:"pointer"},onClick:t[14]||(t[14]=L=>n.copyDeviceId())},{default:C(()=>[m(Ft)]),_:1})])]),_:1},8,["modelValue","before-close"]),m(vt,{ref:"resetPwd",onSuccessCallback:n.resetOrForgetPwdCallback,show:s.showResetPwdDialog,onClose:n.closeResetPwdDialog},null,8,["onSuccessCallback","show","onClose"]),m(be,{ref:"forgetPwd",onSuccessCallback:n.resetOrForgetPwdCallback,show:s.showForgetPwdDialog,onClose:n.closeForgetPwdDialog},null,8,["onSuccessCallback","show","onClose"]),m(kt,{ref:"updater"},null,512),m(It,{ref:"mfaMode",onSucceed:n.mfaLoginCallback,onError:n.generateRequestIdFailCallback,onErrMsg:n.errorLog,onClose:n.closeMfaLogin},null,8,["onSucceed","onError","onErrMsg","onClose"]),m(Xe,{modelValue:s.showTerminalConfig,"onUpdate:modelValue":t[16]||(t[16]=L=>s.showTerminalConfig=L),title:"提示",width:"85%","close-on-click-modal":!1,"close-on-press-escape":!1,"before-close":n.handleClose,class:"terminal_class"},{footer:C(()=>[y("span",Tu,[m(Re,{round:"",style:{padding:"20px 35px","line-height":"0"},onClick:n.handleClose},{default:C(()=>[q("取消")]),_:1},8,["onClick"]),m(Re,{round:"",style:{padding:"20px 35px","line-height":"0"},type:"primary",onClick:n.handleSave},{default:C(()=>[q("确定")]),_:1},8,["onClick"])])]),default:C(()=>[s.showTerminalConfig?(p(),E(Tt,{key:0,ref:"terminalRef",type:"login",limit:s.limitNumber,userId:s.userId},null,8,["limit","userId"])):M("",!0)]),_:1},8,["modelValue","before-close"])])}const Lu=Oe(Oc,[["render",Mu],["__scopeId","data-v-d409866c"]]);export{Lu as default};

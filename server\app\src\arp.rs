use std::{
    collections::{HashMap, HashSet},
    net::{IpAddr, Ipv4Addr},
    sync::Arc,
    time::Duration,
};

use libarp::client::ArpClient;
use moka::{
    future::{Cache, FutureExt},
    notification::{ListenerFuture, RemovalCause},
};
use tokio::sync::oneshot;
use tracing::{error, info, warn, Instrument};

use crate::{network::Network, Error};

/// 启动ARP任务
///
/// - `virtual_ip`: 本机虚拟地址
/// - `networks`: 网卡列表
/// - `addresses`: 本机其他网卡IP地址
pub async fn spawn(
    virtual_ip: Option<IpAddr>,
    networks: &[Network],
    addresses: Option<HashSet<IpAddr>>,
) -> Result<ArpHandle, Error> {
    let (tx, rx) = flume::unbounded();

    let mut client_cache = HashMap::new();
    let mut network_gateways = HashMap::new();
    let mut network_map = HashMap::new();
    for network in networks {
        if let Some(gateway) = &network.gateway {
            network_gateways.insert(network.iface.clone(), gateway.mac.octets());
        }
        let arp_client = ArpClient::new_with_iface_name(&network.iface)
            .map_err(|error| Error::ArpError(network.iface.clone(), error))?;

        client_cache.insert(network.iface.clone(), arp_client);
        network_map.insert(network.iface.clone(), network.clone());
    }

    let network_gateways = Arc::new(network_gateways);

    // ARP缓存
    let eviction_tx = tx.clone();
    let arp_cache = Cache::builder()
            // This cache will hold up to 32MiB of values.
            .max_capacity(32 * 1024 * 1024)
            .time_to_idle(Duration::from_secs(8 * 60 * 60))
            .async_eviction_listener(move |k,
                      v: (String, [u8; 6]),
                      cause|
                      -> ListenerFuture {
                    let eviction_tx = eviction_tx.clone();
                    async move {
                        if let RemovalCause::Expired = cause {
                            if eviction_tx
                            .send_async((v.0, *k, Some(v.1), None::<oneshot::Sender<Option<[u8; 6]>>>))
                            .await
                            .is_err()
                            {
                                error!("arp thread panicked.");
                            }
                        }
                    }
                    .boxed()
                },)
            .build();

    // 将本机其他网卡IP地址加入ARP缓存中
    if let Some(addresses) = addresses {
        let interfaces = netdev::get_interfaces();
        for addr in addresses {
            // 其他地址与默认网卡在同一网段
            match addr {
                IpAddr::V4(ipv4_addr) => {
                    if let Some(interface) = interfaces.iter().find(|interface| {
                        interface.mac_addr.is_some()
                            && interface
                                .ipv4
                                .iter()
                                .any(|ipv4net| ipv4net.addr() == ipv4_addr)
                    }) {
                        if let Some(mac) = if networks[0].ipv4net.contains(&ipv4_addr) {
                            interface.mac_addr.map(|mac| mac.octets())
                        } else {
                            interface
                                .gateway
                                .as_ref()
                                .map(|gateway| gateway.mac_addr.octets())
                        } {
                            arp_cache.insert(addr, (interface.name.clone(), mac)).await;
                        }
                    }
                }
                IpAddr::V6(_ipv6_addr) => (),
            }
        }
    }

    tokio::spawn({
        let arp_cache = arp_cache.clone();
        async move {
            loop {
                match rx.recv_async().await {
                    Ok((iface, ip, default_mac, callback)) => {
                        // 本机虚拟IP不需要Mac地址
                        if Some(ip) == virtual_ip {
                            if let Some(tx) = callback {
                                _ = tx.send(None);
                            }
                            continue;
                        }

                        // 缓存中已存在, 直接返回
                        if let Some((_iface, mac)) = arp_cache.get(&ip).await {
                            if let Some(tx) = callback {
                                _ = tx.send(Some(mac));
                            }
                            continue;
                        }

                        // 若IP不是同网段, 则直接返回网关的Mac地址
                        if let IpAddr::V4(ip) = ip {
                            if let Some(network) = network_map.get(&iface) {
                                if !network.ipv4net.contains(&ip) {
                                    if let Some(tx) = callback {
                                        let mac = network
                                            .gateway
                                            .as_ref()
                                            .map(|gateway| gateway.mac.octets());
                                        _ = tx.send(mac);
                                    }
                                    continue;
                                }
                            }
                        }

                        // 通过ARP查看目标IP的Mac地址
                        if let Some(arp_client) = client_cache.get_mut(&iface) {
                            if let Some(mac) = (match ip {
                                IpAddr::V4(ip) => ip_to_mac(arp_client, ip),
                                IpAddr::V6(_) => None,
                            })
                            .or(default_mac)
                            {
                                arp_cache.insert(ip, (iface, mac.clone())).await;
                                if let Some(tx) = callback {
                                    _ = tx.send(Some(mac));
                                }
                            }
                        } else if let Some(tx) = callback {
                            _ = tx.send(None);
                        }
                    }
                    Err(_) => {
                        error!("arp thread exit.");
                        break;
                    }
                }
            }
            info!("ARP thread exited.");
        }
        .in_current_span()
    });

    Ok(ArpHandle {
        tx,
        arp_cache,
        network_gateways,
    })
}

fn ip_to_mac(arp_client: &mut ArpClient, ip: Ipv4Addr) -> Option<[u8; 6]> {
    match arp_client.ip_to_mac(ip, Some(Duration::from_secs(5))) {
        Ok(mac) => Some([mac.0, mac.1, mac.2, mac.3, mac.4, mac.5]),
        Err(error) => {
            warn!(%ip, "IP to Mac conversion failed: {}", error);
            None
        }
    }
}

#[derive(Clone)]
pub struct ArpHandle {
    tx: flume::Sender<(
        String,
        IpAddr,
        Option<[u8; 6]>,
        Option<oneshot::Sender<Option<[u8; 6]>>>,
    )>,
    arp_cache: Cache<IpAddr, (String, [u8; 6])>,
    network_gateways: Arc<HashMap<String, [u8; 6]>>,
}

impl ArpHandle {
    pub async fn send(&self, iface: &str, ip: IpAddr) {
        if self.arp_cache.contains_key(&ip) {
            return;
        }

        if self
            .tx
            .send_async((iface.to_owned(), ip, None, None))
            .await
            .is_err()
        {
            error!("arp thread panicked.");
        }
    }

    pub async fn get_target_mac(&self, iface: &str, ip: IpAddr) -> Option<[u8; 6]> {
        match self.arp_cache.get(&ip).await {
            Some((_iface, mac)) => Some(mac),
            None => {
                if let Some(gateway_mac) = self.network_gateways.get(iface) {
                    _ = self.tx.send_async((iface.to_owned(), ip, None, None)).await;
                    Some(gateway_mac.clone())
                } else {
                    let (tx, rx) = oneshot::channel();
                    _ = self
                        .tx
                        .send_async((iface.to_owned(), ip, None, Some(tx)))
                        .await;

                    rx.await.ok().flatten()
                }
            }
        }
    }
}

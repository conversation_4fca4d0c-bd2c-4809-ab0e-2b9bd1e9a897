use crate::tunnel::ErrorState;

/// Represents the state the client tunnel is in.
#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, PartialEq, Eq)]
pub enum TunnelState {
    Disconnected,
    Connecting,
    Connected,
    Error(ErrorState),
}

impl TunnelState {
    /// Returns true if the tunnel state is in the error state.
    pub fn is_in_error_state(&self) -> bool {
        matches!(self, TunnelState::Error(_))
    }

    /// Returns true if the tunnel state is in the connected state.
    pub fn is_connected(&self) -> bool {
        matches!(self, TunnelState::Connected { .. })
    }

    /// Returns true if the tunnel state is in the disconnected state.
    pub fn is_disconnected(&self) -> bool {
        matches!(self, TunnelState::Disconnected)
    }
}

#[derive(Debug, Clone, PartialEq, Eq)]
pub enum BackendState {
    Disconnected(Option<i8>),
    Connecting,
    ProxyConnecting,
    ProxyConnected,
    Connected,
    Disconnecting,
    Error(crate::backend::ErrorState),
}

use std::{
    collections::HashMap,
    net::{Ip<PERSON>dd<PERSON>, SocketAddr},
    sync::Arc,
};

use fred::types::Expiration;
use gm_sm2::key::{Sm2Model, Sm2PrivateKey};
use moka::future::Cache;
use tracing::{debug, error, info, warn};

use crate::{
    backend::client::BackendCommonClient,
    cache::CacheManager,
    xdp::{XdpCommand, XdpCommandSender},
};
use base::spa::{SPAType, SPA};

const NONCE_PREFIX: &str = "spa_nonce::";
const UNIVERSAL_SECRET: [u8; 20] = [
    0b10110110, 0b01011111, 0b10110000, 0b11111011, 0b00100000, 0b00101110, 0b00010001, 0b01111001,
    0b10110001, 0b11111011, 0b01011010, 0b01000110, 0b10001011, 0b11011001, 0b11011100, 0b00101110,
    0b01001011, 0b00001010, 0b10111010, 0b00011010,
];

pub struct SPAValidator {
    pub sec_key: Sm2PrivateKey,
    pub time_deviation: u32,

    pub xdp_command_sender: XdpCommandSender,
    pub cache_manager: CacheManager,
    pub backend_client: Arc<BackendCommonClient>,

    /// SPA 认证缓存
    pub preauthed: Arc<Cache<IpAddr, HashMap<String, SPAType>>>,
    /// SPA 防重放
    pub anti_replay: Arc<Cache<String, SocketAddr>>,
}

impl SPAValidator {
    #[tracing::instrument(name = "SPAValidator", skip_all)]
    pub async fn validate(&self, src_ip: IpAddr, src_port: u16, packet: &[u8]) -> Option<String> {
        let socket_addr = SocketAddr::new(src_ip, src_port);
        debug!(addr = %socket_addr, "spa packet.");

        let spa = match self.decrypt(packet) {
            Some(spa) => spa,
            None => {
                error!("failed to decrypt spa packet.");
                return None;
            }
        };

        let spa_type = spa.r#type();

        let (username, secret) = match spa_type {
            SPAType::Auth | SPAType::ChangePwd => {
                let payload = match spa.payload() {
                    Some(payload) => payload,
                    None => {
                        error!(addr = %socket_addr, ?spa_type, "spa packet missing payload.");
                        return None;
                    }
                };
                match self.extract_username_and_secret(payload).await {
                    Some(data) => (Some(data.0), data.1),
                    None => return None,
                }
            }
            SPAType::Proxy => (None, UNIVERSAL_SECRET.to_vec()),
        };

        if base::spa::verify_packet(
            &spa,
            &packet[packet.len() - 32..].try_into().unwrap(),
            &secret,
            self.time_deviation,
        ) {
            // 防重放
            let nonce = key(spa.nonce());
            match self.cache_manager.exists(&nonce).await {
                Some(exists) => {
                    if exists {
                        warn!(addr = %socket_addr, ?spa_type, "received duplicate spa packet.");
                        return None;
                    }
                }
                None => return None,
            }
            let device_id = hex::to_string!(spa.device());

            // SPA 认证缓存
            let entry = self
                .preauthed
                .entry(src_ip)
                .or_insert(Default::default())
                .await;
            let mut devices = entry.into_value();
            devices.insert(device_id.clone(), spa.r#type());
            self.preauthed.insert(src_ip, devices).await;

            // 防重复发送SPA
            self.anti_replay
                .entry(device_id.clone())
                .or_insert(SocketAddr::new(src_ip, src_port))
                .await;

            self.cache_manager
                .set(&nonce, "", Some(Expiration::EX(60)))
                .await;

            self.xdp_command_sender
                .send(XdpCommand::OpenPort(src_ip))
                .await;

            info!(addr = %socket_addr, device = &device_id, ?username, ?spa_type, "spa packet validation succeeded.");

            return Some(device_id);
        }
        error!(addr = %socket_addr, ?spa_type, "spa packet validation failed.");
        None
    }

    fn decrypt(&self, packet: &[u8]) -> Option<SPA> {
        // 解密
        let spa = match self
            .sec_key
            .decrypt(&packet[..packet.len() - 32], false, Sm2Model::C1C2C3)
        {
            Ok(spa) => spa,
            Err(err) => {
                error!("failed to decrypt spa packet: {}", err);
                return None;
            }
        };
        SPA::from(spa).map_err(|err| error!("{err}")).ok()
    }

    async fn extract_username_and_secret(&self, payload: &[u8]) -> Option<(String, Vec<u8>)> {
        match std::str::from_utf8(payload) {
            Ok(payload) => {
                if let Some((tenant, username)) = payload.split_once('|') {
                    return self
                        .backend_client
                        .exchange_secret(tenant, username)
                        .await
                        .map(|secret| (username.to_owned(), secret));
                } else {
                    error!("payload format error. {}", payload);
                }
            }
            _ => {
                error!("username is not utf-8 encoded");
            }
        }
        None
    }
}

fn key(nonce: &[u8; 4]) -> String {
    let mut prefix = String::from(NONCE_PREFIX);
    prefix.push_str(&u32::from_be_bytes(*nonce).to_string());
    prefix
}

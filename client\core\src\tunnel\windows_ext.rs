use core::slice;
#[cfg(windows)]
use std::ffi::c_void;
use std::{
    collections::HashSet,
    io,
    net::{IpAddr, Ipv4Addr, Ipv6Addr},
    ptr,
    time::Duration,
};

use futures::StreamExt;
use futures_channel::{mpsc, oneshot};
#[cfg(windows)]
use windows::Win32::NetworkManagement::IpHelper::{
    MIB_NOTIFICATION_TYPE, MIB_UNICASTIPADDRESS_ROW,
};
use windows::Win32::{
    Foundation::{ERROR_SUCCESS, HANDLE},
    NetworkManagement::IpHelper::{
        CancelMibChangeNotify2, FreeMibTable, GetUnicastIpAddressTable,
        NotifyUnicastIpAddressChange, MIB_UNICASTIPADDRESS_TABLE,
    },
    Networking::WinSock::{IpDadStatePreferred, AF_INET, AF_INET6, AF_UNSPEC, SOCKADDR_INET},
};

/// Wait until the specified IP address is set successfully.
pub async fn wait_for_ips(
    if_index: u32,
    mut ips: HashSet<IpAddr>,
    listen_tx: oneshot::Sender<()>,
) -> io::Result<()> {
    let (tx, mut rx) = mpsc::unbounded();

    let _handle = notify_ip_address_change(tx)?;
    _ = listen_tx.send(());

    loop {
        if let Some((index, ip)) = rx.next().await {
            log::debug!("IP address changed: {}", ip);
            if index == if_index {
                ips.remove(&ip);
            }
        }

        if ips.is_empty() {
            break;
        }
    }
    drop(_handle);

    // 等待IP地址就绪
    unsafe {
        log::debug!("Waiting for IP address to be ready");
        loop {
            // 将 if_table 的生命周期限制在循环单次迭代内
            let is_ready = {
                let mut if_table: *mut MIB_UNICASTIPADDRESS_TABLE = ptr::null_mut();
                _ = GetUnicastIpAddressTable(AF_UNSPEC, &mut if_table);

                let table = if_table.as_ref().unwrap();
                let ready = slice::from_raw_parts(table.Table.as_ptr(), table.NumEntries as _)
                    .iter()
                    .filter(|row| row.InterfaceIndex == if_index)
                    .all(|row| row.DadState == IpDadStatePreferred);

                // 立即释放资源
                FreeMibTable(if_table as _);
                ready
            };

            if is_ready {
                break;
            }

            tokio::time::sleep(Duration::from_millis(10)).await;
        }
        log::debug!("IP address ready");
    }

    Ok(())
}

/// Context for [`notify_ip_address_change`]. When it is dropped,
/// the callback is unregistered.
struct IpChangeNotifierHandle {
    sender: mpsc::UnboundedSender<(u32, IpAddr)>,
    handle: HANDLE,
}

unsafe impl Send for IpChangeNotifierHandle {}

impl<'a> Drop for IpChangeNotifierHandle {
    fn drop(&mut self) {
        // SAFETY: There is no clear safety specification on this function. However self.0 should
        // point to a handle that has been allocated by windows and should be non-null. Even
        // if it would be null that would cause a panic rather than UB.
        let ret = unsafe { CancelMibChangeNotify2(self.handle) };
        if ret != ERROR_SUCCESS {
            let err = io::Error::from_raw_os_error(ret.0 as i32);
            // If this callback is called after we free the context that could result in UB, in
            // order to avoid that we panic.
            panic!("Could not cancel change notification callback: {}", err)
        }
    }
}

fn notify_ip_address_change(
    sender: mpsc::UnboundedSender<(u32, IpAddr)>,
) -> io::Result<Box<IpChangeNotifierHandle>> {
    let mut context = Box::new(IpChangeNotifierHandle {
        sender,
        handle: HANDLE::default(),
    });

    let status = unsafe {
        NotifyUnicastIpAddressChange(
            AF_UNSPEC,
            Some(ip_address_change_callback),
            Some(context.as_ref() as *const _ as *const _),
            false,
            &mut context.handle,
        )
    };

    if status == ERROR_SUCCESS {
        Ok(context)
    } else {
        Err(io::Error::from_raw_os_error(status.0 as i32))
    }
}

// SAFETY: `context` is a Box::into_raw() pointer which may only be used as a non-mutable reference.
// It is guaranteed by the DefaultRouteMonitor to not be dropped before this function is guaranteed
// to not be called again.
unsafe extern "system" fn ip_address_change_callback(
    context: *const c_void,
    row: *const MIB_UNICASTIPADDRESS_ROW,
    notification_type: MIB_NOTIFICATION_TYPE,
) {
    use windows::Win32::NetworkManagement::IpHelper::MibAddInstance;

    if notification_type == MibAddInstance {
        // SAFETY: We assume Windows provides this pointer correctly
        let row = &*row;

        let ip = convert_sockaddr(row.Address);

        // SAFETY: context must not be dropped or modified until this callback has been cancelled.
        let context = &mut *(context as *mut IpChangeNotifierHandle);
        _ = context.sender.unbounded_send((row.InterfaceIndex, ip));
    }
}

fn convert_sockaddr(sa: SOCKADDR_INET) -> IpAddr {
    unsafe {
        match sa.si_family {
            AF_INET => Ipv4Addr::from(sa.Ipv4.sin_addr).into(),
            AF_INET6 => Ipv6Addr::from(sa.Ipv6.sin6_addr).into(),
            _ => panic!("Invalid address family"),
        }
    }
}

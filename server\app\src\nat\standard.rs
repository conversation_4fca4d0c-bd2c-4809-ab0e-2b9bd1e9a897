use base::net::Addr;
use dashmap::DashMap;
use moka::future::Cache;

use std::{collections::HashSet, time::Duration};

use crate::xdp::{XdpCommand, XdpCommandSenders};

use super::{NatState, Port};

/// 网络地址转换表
///
/// - `key`: 访问目标地址
/// - `value`: 端口与客户端连接的映射关系
/// - `value.key`: 网关端口号
/// - `value.value.0`: 是否溯源
/// - `value.value.1`: 真实客户端地址
/// - `value.value.2`: 客户端设备ID
///
/// ## Example
///
/// ```txt
/// 客户端地址: 10.10.10.10
/// 客户端设备ID: 4c4c4544004d42108047cac04f505232
/// 客户端访问目标地址: 192.168.1.43:22
/// 客户端本地连接地址: 10.10.10.10:33845
/// 网关映射端口: 56303
///
/// 则存储结构为
/// {"192.168.1.43:22": {56303: (false, false, "10.10.10.10:33845", "4c4c4544004d42108047cac04f505232")}}
/// ```
pub struct Nat {
    xdp_command_senders: XdpCommandSenders,
    used_ports: HashSet<u16>,
    table: DashMap<Addr, Cache<Port, (bool, bool, Addr, String)>>,
}

impl Nat {
    pub fn new(xdp_command_senders: XdpCommandSenders, used_ports: HashSet<u16>) -> Self {
        Self {
            xdp_command_senders,
            used_ports,
            table: DashMap::default(),
        }
    }

    /// 获取转换地址
    ///
    /// # Arguments
    ///
    /// - `device_id`: 客户端设备ID
    /// - `client_local_addr`: 客户端本地地址
    /// - `access_addr`: 访问目标地址
    ///
    /// # Return
    ///
    /// - 如果访问目标地址存在映射, 并且是当前客户端访问的, 则返回已经建立映射的端口, 状态为Already
    /// - 如果访问目标地址存在映射, 但不存在当前客户端访问的端口映射, 则建立端口映射, 并返回端口,
    ///   状态为Created, 建立映射失败, 则返回None
    /// - 如果不存在映射, 则创建访问地址的映射, 并建立当前客户端访问的端口映射, 然后返回端口,
    ///   状态为Created
    #[allow(dead_code)]
    pub fn get_exists(
        &self,
        device_id: &str,
        client_local_addr: Addr,
        access_addr: Addr,
    ) -> Option<NatState> {
        match self.table.get(&access_addr) {
            Some(mapping) => {
                if let Some((port, _)) =
                    mapping
                        .iter()
                        .find(|(_, (_, _, t_client_local_addr, device))| {
                            device_id == device && client_local_addr == *t_client_local_addr
                        })
                {
                    return Some(NatState::Already(*port));
                }
                None
            }
            None => None,
        }
    }

    /// 获取转换地址或构建一个映射地址
    ///
    /// # Arguments
    ///
    /// - `tracing`: 是否溯源
    /// - `is_virtual`: 是否通过伪IP访问
    /// - `device_id`: 客户端设备ID
    /// - `client_local_addr`: 客户端本地地址
    /// - `access_addr`: 访问目标地址
    ///
    /// # Return
    ///
    /// - 如果访问目标地址存在映射, 并且是当前客户端访问的, 则返回已经建立映射的端口, 状态为Already
    /// - 如果访问目标地址存在映射, 但不存在当前客户端访问的端口映射, 则建立端口映射, 并返回端口,
    ///   状态为Already, 建立映射失败, 则返回None
    /// - 如果不存在映射, 则创建访问地址的映射, 并建立当前客户端访问的端口映射, 然后返回端口,
    ///   状态为Created
    pub async fn get_or_map(
        &self,
        tracing: bool,
        is_virtual: bool,
        device_id: String,
        client_local_addr: Addr,
        access_addr: Addr,
    ) -> Option<NatState> {
        match self.table.get_mut(&access_addr) {
            Some(cache) => {
                if let Some((port, _)) =
                    cache
                        .iter()
                        .find(|(_, (_, _, t_client_local_addr, device))| {
                            device_id == *device && client_local_addr == *t_client_local_addr
                        })
                {
                    // 刷新缓存时间
                    let _ = cache.get(&*port);
                    return Some(NatState::Already(*port));
                }

                if let Some(port) = super::rand_port(&self.used_ports) {
                    cache
                        .insert(port, (tracing, is_virtual, client_local_addr, device_id))
                        .await;
                    return Some(NatState::Already(port));
                }

                None
            }
            None => {
                let cache: Cache<Port, (bool, bool, Addr, String)> = Cache::builder()
                    .time_to_idle(Duration::from_secs(122 * 60))
                    .build();
                if let Some(port) = super::rand_port(&self.used_ports) {
                    cache
                        .insert(port, (tracing, is_virtual, client_local_addr, device_id))
                        .await;
                    self.table.insert(access_addr, cache);
                    return Some(NatState::Created(port));
                }
                None
            }
        }
    }

    /// 根据目标地址及映射后的本地端口查询映射信息
    ///
    /// # Arguments
    ///
    /// - `access_addr`: 访问目标地址
    /// - `port`: NAT转换后的本地端口
    ///
    /// # Return
    /// 如果存在映射, 返回客户端连接地址及设备ID
    pub async fn get(&self, access_addr: Addr, port: Port) -> Option<(bool, bool, Addr, String)> {
        if let Some(cache) = self.table.get(&access_addr) {
            return cache.get(&port).await;
        }
        None
    }

    /// 删除映射
    ///
    /// # Arguments
    ///
    /// - `addr`: 访问目标IP地址
    /// - `port`: 映射端口
    ///
    /// # Note
    ///
    /// 仅用于ICMP协议
    pub async fn delete_by_access_addr(
        &self,
        addr: Addr,
        port: Port,
    ) -> Option<(bool, bool, Addr, String)> {
        let mut key = None;
        let mut result = None;
        for entry in self.table.iter_mut() {
            let access_addr = entry.key();
            let cache = entry.value();
            if access_addr.0 == addr.0 && cache.contains_key(&port) {
                result = cache.get(&port).await;
                cache.invalidate(&port).await;
                // cache.sync();
                if cache.entry_count() == 0 {
                    key = Some(*access_addr);
                }
                break;
            }
        }
        if let Some(key) = key {
            self.table.remove(&key);
            // 删除内核中的NAT映射
            self.xdp_command_senders
                .send(XdpCommand::DelNat((key.0, port), None))
                .await;
        }
        result
    }

    /// 删除映射
    ///
    /// - `device_id`: 客户端设备ID
    pub async fn delete_by_device_id(&self, device_id: &str) {
        let entries = self
            .table
            .iter()
            .filter(|entry| {
                let cache = entry.value();
                let mut r#match = 0;
                for (_port, (_, _, _, device)) in cache.iter() {
                    if device == device_id {
                        r#match += 1;
                    }
                }
                cache.entry_count() == r#match
            })
            .map(|entry| *entry.key())
            .collect::<Vec<Addr>>();
        for k in entries {
            self.table.remove(&k);
            // 删除内核中的NAT映射
            self.xdp_command_senders
                .send(XdpCommand::DelNat((k.0, k.1), None))
                .await;
        }
        for entry in self.table.iter_mut() {
            let cache = entry.value();
            for (port, (_, _, _, device)) in cache.iter() {
                if device == device_id {
                    cache.invalidate(&*port).await;
                }
            }
        }
    }
}

import{M as te,a4 as H,aM as ee,aD as de,ax as ke,bE as E,ct as Se,cu as G,cv as P,aR as _,aE as m,u as f,aq as V,I as c,aQ as Ae,bz as W,bp as le,v as h,bx as I,ao as b,aa as Ee,H as y,cb as F,t as C,b3 as k,aI as z,aF as L,cw as we,cx as Pe,s as B,cy as Re,cz as Me,cA as Fe,cB as $,bJ as ce,bM as he,bN as pe,bO as me,bP as fe,aS as _e,bA as ge,bU as j,cC as ue}from"./index-8ff3976b.js";/*!
 * qrcode.vue v3.4.1
 * A Vue.js component to generate QRCode.
 * © 2017-2023 @scopewu(https://github.com/scopewu)
 * MIT License.
 */var X=function(){return X=Object.assign||function(t){for(var a,p=1,n=arguments.length;p<n;p++){a=arguments[p];for(var d in a)Object.prototype.hasOwnProperty.call(a,d)&&(t[d]=a[d])}return t},X.apply(this,arguments)};var O;(function(e){var t=function(){function o(s,i,r,l){if(this.version=s,this.errorCorrectionLevel=i,this.modules=[],this.isFunction=[],s<o.MIN_VERSION||s>o.MAX_VERSION)throw new RangeError("Version value out of range");if(l<-1||l>7)throw new RangeError("Mask value out of range");this.size=s*4+17;for(var u=[],g=0;g<this.size;g++)u.push(!1);for(var g=0;g<this.size;g++)this.modules.push(u.slice()),this.isFunction.push(u.slice());this.drawFunctionPatterns();var v=this.addEccAndInterleave(r);if(this.drawCodewords(v),l==-1)for(var q=1e9,g=0;g<8;g++){this.applyMask(g),this.drawFormatBits(g);var w=this.getPenaltyScore();w<q&&(l=g,q=w),this.applyMask(g)}n(0<=l&&l<=7),this.mask=l,this.applyMask(l),this.drawFormatBits(l),this.isFunction=[]}return o.encodeText=function(s,i){var r=e.QrSegment.makeSegments(s);return o.encodeSegments(r,i)},o.encodeBinary=function(s,i){var r=e.QrSegment.makeBytes(s);return o.encodeSegments([r],i)},o.encodeSegments=function(s,i,r,l,u,g){if(r===void 0&&(r=1),l===void 0&&(l=40),u===void 0&&(u=-1),g===void 0&&(g=!0),!(o.MIN_VERSION<=r&&r<=l&&l<=o.MAX_VERSION)||u<-1||u>7)throw new RangeError("Invalid value");var v,q;for(v=r;;v++){var w=o.getNumDataCodewords(v,i)*8,M=d.getTotalBits(s,v);if(M<=w){q=M;break}if(v>=l)throw new RangeError("Data too long")}for(var A=0,T=[o.Ecc.MEDIUM,o.Ecc.QUARTILE,o.Ecc.HIGH];A<T.length;A++){var N=T[A];g&&q<=o.getNumDataCodewords(v,N)*8&&(i=N)}for(var S=[],U=0,D=s;U<D.length;U++){var x=D[U];a(x.mode.modeBits,4,S),a(x.numChars,x.mode.numCharCountBits(v),S);for(var Q=0,Y=x.getData();Q<Y.length;Q++){var be=Y[Q];S.push(be)}}n(S.length==q);var Z=o.getNumDataCodewords(v,i)*8;n(S.length<=Z),a(0,Math.min(4,Z-S.length),S),a(0,(8-S.length%8)%8,S),n(S.length%8==0);for(var ae=236;S.length<Z;ae^=253)a(ae,8,S);for(var J=[];J.length*8<S.length;)J.push(0);return S.forEach(function(qe,oe){return J[oe>>>3]|=qe<<7-(oe&7)}),new o(v,i,J,u)},o.prototype.getModule=function(s,i){return 0<=s&&s<this.size&&0<=i&&i<this.size&&this.modules[i][s]},o.prototype.getModules=function(){return this.modules},o.prototype.drawFunctionPatterns=function(){for(var s=0;s<this.size;s++)this.setFunctionModule(6,s,s%2==0),this.setFunctionModule(s,6,s%2==0);this.drawFinderPattern(3,3),this.drawFinderPattern(this.size-4,3),this.drawFinderPattern(3,this.size-4);for(var i=this.getAlignmentPatternPositions(),r=i.length,s=0;s<r;s++)for(var l=0;l<r;l++)s==0&&l==0||s==0&&l==r-1||s==r-1&&l==0||this.drawAlignmentPattern(i[s],i[l]);this.drawFormatBits(0),this.drawVersion()},o.prototype.drawFormatBits=function(s){for(var i=this.errorCorrectionLevel.formatBits<<3|s,r=i,l=0;l<10;l++)r=r<<1^(r>>>9)*1335;var u=(i<<10|r)^21522;n(u>>>15==0);for(var l=0;l<=5;l++)this.setFunctionModule(8,l,p(u,l));this.setFunctionModule(8,7,p(u,6)),this.setFunctionModule(8,8,p(u,7)),this.setFunctionModule(7,8,p(u,8));for(var l=9;l<15;l++)this.setFunctionModule(14-l,8,p(u,l));for(var l=0;l<8;l++)this.setFunctionModule(this.size-1-l,8,p(u,l));for(var l=8;l<15;l++)this.setFunctionModule(8,this.size-15+l,p(u,l));this.setFunctionModule(8,this.size-8,!0)},o.prototype.drawVersion=function(){if(!(this.version<7)){for(var s=this.version,i=0;i<12;i++)s=s<<1^(s>>>11)*7973;var r=this.version<<12|s;n(r>>>18==0);for(var i=0;i<18;i++){var l=p(r,i),u=this.size-11+i%3,g=Math.floor(i/3);this.setFunctionModule(u,g,l),this.setFunctionModule(g,u,l)}}},o.prototype.drawFinderPattern=function(s,i){for(var r=-4;r<=4;r++)for(var l=-4;l<=4;l++){var u=Math.max(Math.abs(l),Math.abs(r)),g=s+l,v=i+r;0<=g&&g<this.size&&0<=v&&v<this.size&&this.setFunctionModule(g,v,u!=2&&u!=4)}},o.prototype.drawAlignmentPattern=function(s,i){for(var r=-2;r<=2;r++)for(var l=-2;l<=2;l++)this.setFunctionModule(s+l,i+r,Math.max(Math.abs(l),Math.abs(r))!=1)},o.prototype.setFunctionModule=function(s,i,r){this.modules[i][s]=r,this.isFunction[i][s]=!0},o.prototype.addEccAndInterleave=function(s){var i=this.version,r=this.errorCorrectionLevel;if(s.length!=o.getNumDataCodewords(i,r))throw new RangeError("Invalid argument");for(var l=o.NUM_ERROR_CORRECTION_BLOCKS[r.ordinal][i],u=o.ECC_CODEWORDS_PER_BLOCK[r.ordinal][i],g=Math.floor(o.getNumRawDataModules(i)/8),v=l-g%l,q=Math.floor(g/l),w=[],M=o.reedSolomonComputeDivisor(u),A=0,T=0;A<l;A++){var N=s.slice(T,T+q-u+(A<v?0:1));T+=N.length;var S=o.reedSolomonComputeRemainder(N,M);A<v&&N.push(0),w.push(N.concat(S))}for(var U=[],D=function(x){w.forEach(function(Q,Y){(x!=q-u||Y>=v)&&U.push(Q[x])})},A=0;A<w[0].length;A++)D(A);return n(U.length==g),U},o.prototype.drawCodewords=function(s){if(s.length!=Math.floor(o.getNumRawDataModules(this.version)/8))throw new RangeError("Invalid argument");for(var i=0,r=this.size-1;r>=1;r-=2){r==6&&(r=5);for(var l=0;l<this.size;l++)for(var u=0;u<2;u++){var g=r-u,v=(r+1&2)==0,q=v?this.size-1-l:l;!this.isFunction[q][g]&&i<s.length*8&&(this.modules[q][g]=p(s[i>>>3],7-(i&7)),i++)}}n(i==s.length*8)},o.prototype.applyMask=function(s){if(s<0||s>7)throw new RangeError("Mask value out of range");for(var i=0;i<this.size;i++)for(var r=0;r<this.size;r++){var l=void 0;switch(s){case 0:l=(r+i)%2==0;break;case 1:l=i%2==0;break;case 2:l=r%3==0;break;case 3:l=(r+i)%3==0;break;case 4:l=(Math.floor(r/3)+Math.floor(i/2))%2==0;break;case 5:l=r*i%2+r*i%3==0;break;case 6:l=(r*i%2+r*i%3)%2==0;break;case 7:l=((r+i)%2+r*i%3)%2==0;break;default:throw new Error("Unreachable")}!this.isFunction[i][r]&&l&&(this.modules[i][r]=!this.modules[i][r])}},o.prototype.getPenaltyScore=function(){for(var s=0,i=0;i<this.size;i++){for(var r=!1,l=0,u=[0,0,0,0,0,0,0],g=0;g<this.size;g++)this.modules[i][g]==r?(l++,l==5?s+=o.PENALTY_N1:l>5&&s++):(this.finderPenaltyAddHistory(l,u),r||(s+=this.finderPenaltyCountPatterns(u)*o.PENALTY_N3),r=this.modules[i][g],l=1);s+=this.finderPenaltyTerminateAndCount(r,l,u)*o.PENALTY_N3}for(var g=0;g<this.size;g++){for(var r=!1,v=0,u=[0,0,0,0,0,0,0],i=0;i<this.size;i++)this.modules[i][g]==r?(v++,v==5?s+=o.PENALTY_N1:v>5&&s++):(this.finderPenaltyAddHistory(v,u),r||(s+=this.finderPenaltyCountPatterns(u)*o.PENALTY_N3),r=this.modules[i][g],v=1);s+=this.finderPenaltyTerminateAndCount(r,v,u)*o.PENALTY_N3}for(var i=0;i<this.size-1;i++)for(var g=0;g<this.size-1;g++){var q=this.modules[i][g];q==this.modules[i][g+1]&&q==this.modules[i+1][g]&&q==this.modules[i+1][g+1]&&(s+=o.PENALTY_N2)}for(var w=0,M=0,A=this.modules;M<A.length;M++){var T=A[M];w=T.reduce(function(U,D){return U+(D?1:0)},w)}var N=this.size*this.size,S=Math.ceil(Math.abs(w*20-N*10)/N)-1;return n(0<=S&&S<=9),s+=S*o.PENALTY_N4,n(0<=s&&s<=2568888),s},o.prototype.getAlignmentPatternPositions=function(){if(this.version==1)return[];for(var s=Math.floor(this.version/7)+2,i=this.version==32?26:Math.ceil((this.version*4+4)/(s*2-2))*2,r=[6],l=this.size-7;r.length<s;l-=i)r.splice(1,0,l);return r},o.getNumRawDataModules=function(s){if(s<o.MIN_VERSION||s>o.MAX_VERSION)throw new RangeError("Version number out of range");var i=(16*s+128)*s+64;if(s>=2){var r=Math.floor(s/7)+2;i-=(25*r-10)*r-55,s>=7&&(i-=36)}return n(208<=i&&i<=29648),i},o.getNumDataCodewords=function(s,i){return Math.floor(o.getNumRawDataModules(s)/8)-o.ECC_CODEWORDS_PER_BLOCK[i.ordinal][s]*o.NUM_ERROR_CORRECTION_BLOCKS[i.ordinal][s]},o.reedSolomonComputeDivisor=function(s){if(s<1||s>255)throw new RangeError("Degree out of range");for(var i=[],r=0;r<s-1;r++)i.push(0);i.push(1);for(var l=1,r=0;r<s;r++){for(var u=0;u<i.length;u++)i[u]=o.reedSolomonMultiply(i[u],l),u+1<i.length&&(i[u]^=i[u+1]);l=o.reedSolomonMultiply(l,2)}return i},o.reedSolomonComputeRemainder=function(s,i){for(var r=i.map(function(q){return 0}),l=function(q){var w=q^r.shift();r.push(0),i.forEach(function(M,A){return r[A]^=o.reedSolomonMultiply(M,w)})},u=0,g=s;u<g.length;u++){var v=g[u];l(v)}return r},o.reedSolomonMultiply=function(s,i){if(s>>>8||i>>>8)throw new RangeError("Byte out of range");for(var r=0,l=7;l>=0;l--)r=r<<1^(r>>>7)*285,r^=(i>>>l&1)*s;return n(r>>>8==0),r},o.prototype.finderPenaltyCountPatterns=function(s){var i=s[1];n(i<=this.size*3);var r=i>0&&s[2]==i&&s[3]==i*3&&s[4]==i&&s[5]==i;return(r&&s[0]>=i*4&&s[6]>=i?1:0)+(r&&s[6]>=i*4&&s[0]>=i?1:0)},o.prototype.finderPenaltyTerminateAndCount=function(s,i,r){return s&&(this.finderPenaltyAddHistory(i,r),i=0),i+=this.size,this.finderPenaltyAddHistory(i,r),this.finderPenaltyCountPatterns(r)},o.prototype.finderPenaltyAddHistory=function(s,i){i[0]==0&&(s+=this.size),i.pop(),i.unshift(s)},o.MIN_VERSION=1,o.MAX_VERSION=40,o.PENALTY_N1=3,o.PENALTY_N2=3,o.PENALTY_N3=40,o.PENALTY_N4=10,o.ECC_CODEWORDS_PER_BLOCK=[[-1,7,10,15,20,26,18,20,24,30,18,20,24,26,30,22,24,28,30,28,28,28,28,30,30,26,28,30,30,30,30,30,30,30,30,30,30,30,30,30,30],[-1,10,16,26,18,24,16,18,22,22,26,30,22,22,24,24,28,28,26,26,26,26,28,28,28,28,28,28,28,28,28,28,28,28,28,28,28,28,28,28,28],[-1,13,22,18,26,18,24,18,22,20,24,28,26,24,20,30,24,28,28,26,30,28,30,30,30,30,28,30,30,30,30,30,30,30,30,30,30,30,30,30,30],[-1,17,28,22,16,22,28,26,26,24,28,24,28,22,24,24,30,28,28,26,28,30,24,30,30,30,30,30,30,30,30,30,30,30,30,30,30,30,30,30,30]],o.NUM_ERROR_CORRECTION_BLOCKS=[[-1,1,1,1,1,1,2,2,2,2,4,4,4,4,4,6,6,6,6,7,8,8,9,9,10,12,12,12,13,14,15,16,17,18,19,19,20,21,22,24,25],[-1,1,1,1,2,2,4,4,4,5,5,5,8,9,9,10,10,11,13,14,16,17,17,18,20,21,23,25,26,28,29,31,33,35,37,38,40,43,45,47,49],[-1,1,1,2,2,4,4,6,6,8,8,8,10,12,16,12,17,16,18,21,20,23,23,25,27,29,34,34,35,38,40,43,45,48,51,53,56,59,62,65,68],[-1,1,1,2,4,4,4,5,6,8,8,11,11,16,16,18,16,19,21,25,25,25,34,30,32,35,37,40,42,45,48,51,54,57,60,63,66,70,74,77,81]],o}();e.QrCode=t;function a(o,s,i){if(s<0||s>31||o>>>s)throw new RangeError("Value out of range");for(var r=s-1;r>=0;r--)i.push(o>>>r&1)}function p(o,s){return(o>>>s&1)!=0}function n(o){if(!o)throw new Error("Assertion error")}var d=function(){function o(s,i,r){if(this.mode=s,this.numChars=i,this.bitData=r,i<0)throw new RangeError("Invalid argument");this.bitData=r.slice()}return o.makeBytes=function(s){for(var i=[],r=0,l=s;r<l.length;r++){var u=l[r];a(u,8,i)}return new o(o.Mode.BYTE,s.length,i)},o.makeNumeric=function(s){if(!o.isNumeric(s))throw new RangeError("String contains non-numeric characters");for(var i=[],r=0;r<s.length;){var l=Math.min(s.length-r,3);a(parseInt(s.substring(r,r+l),10),l*3+1,i),r+=l}return new o(o.Mode.NUMERIC,s.length,i)},o.makeAlphanumeric=function(s){if(!o.isAlphanumeric(s))throw new RangeError("String contains unencodable characters in alphanumeric mode");var i=[],r;for(r=0;r+2<=s.length;r+=2){var l=o.ALPHANUMERIC_CHARSET.indexOf(s.charAt(r))*45;l+=o.ALPHANUMERIC_CHARSET.indexOf(s.charAt(r+1)),a(l,11,i)}return r<s.length&&a(o.ALPHANUMERIC_CHARSET.indexOf(s.charAt(r)),6,i),new o(o.Mode.ALPHANUMERIC,s.length,i)},o.makeSegments=function(s){return s==""?[]:o.isNumeric(s)?[o.makeNumeric(s)]:o.isAlphanumeric(s)?[o.makeAlphanumeric(s)]:[o.makeBytes(o.toUtf8ByteArray(s))]},o.makeEci=function(s){var i=[];if(s<0)throw new RangeError("ECI assignment value out of range");if(s<128)a(s,8,i);else if(s<16384)a(2,2,i),a(s,14,i);else if(s<1e6)a(6,3,i),a(s,21,i);else throw new RangeError("ECI assignment value out of range");return new o(o.Mode.ECI,0,i)},o.isNumeric=function(s){return o.NUMERIC_REGEX.test(s)},o.isAlphanumeric=function(s){return o.ALPHANUMERIC_REGEX.test(s)},o.prototype.getData=function(){return this.bitData.slice()},o.getTotalBits=function(s,i){for(var r=0,l=0,u=s;l<u.length;l++){var g=u[l],v=g.mode.numCharCountBits(i);if(g.numChars>=1<<v)return 1/0;r+=4+v+g.bitData.length}return r},o.toUtf8ByteArray=function(s){s=encodeURI(s);for(var i=[],r=0;r<s.length;r++)s.charAt(r)!="%"?i.push(s.charCodeAt(r)):(i.push(parseInt(s.substring(r+1,r+3),16)),r+=2);return i},o.NUMERIC_REGEX=/^[0-9]*$/,o.ALPHANUMERIC_REGEX=/^[A-Z0-9 $%*+.\/:-]*$/,o.ALPHANUMERIC_CHARSET="0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ $%*+-./:",o}();e.QrSegment=d})(O||(O={}));(function(e){(function(t){var a=function(){function p(n,d){this.ordinal=n,this.formatBits=d}return p.LOW=new p(0,1),p.MEDIUM=new p(1,0),p.QUARTILE=new p(2,3),p.HIGH=new p(3,2),p}();t.Ecc=a})(e.QrCode||(e.QrCode={}))})(O||(O={}));(function(e){(function(t){var a=function(){function p(n,d){this.modeBits=n,this.numBitsCharCount=d}return p.prototype.numCharCountBits=function(n){return this.numBitsCharCount[Math.floor((n+7)/17)]},p.NUMERIC=new p(1,[10,12,14]),p.ALPHANUMERIC=new p(2,[9,11,13]),p.BYTE=new p(4,[8,16,16]),p.KANJI=new p(8,[8,10,12]),p.ECI=new p(7,[0,0,0]),p}();t.Mode=a})(e.QrSegment||(e.QrSegment={}))})(O||(O={}));var K=O,ye="H",se={L:K.QrCode.Ecc.LOW,M:K.QrCode.Ecc.MEDIUM,Q:K.QrCode.Ecc.QUARTILE,H:K.QrCode.Ecc.HIGH},Te=function(){try{new Path2D().addPath(new Path2D)}catch{return!1}return!0}();function Ie(e){return e in se}function ve(e,t){t===void 0&&(t=0);var a=[];return e.forEach(function(p,n){var d=null;p.forEach(function(o,s){if(!o&&d!==null){a.push("M".concat(d+t," ").concat(n+t,"h").concat(s-d,"v1H").concat(d+t,"z")),d=null;return}if(s===p.length-1){if(!o)return;d===null?a.push("M".concat(s+t,",").concat(n+t," h1v1H").concat(s+t,"z")):a.push("M".concat(d+t,",").concat(n+t," h").concat(s+1-d,"v1H").concat(d+t,"z"));return}o&&d===null&&(d=s)})}),a.join("")}var ie={value:{type:String,required:!0,default:""},size:{type:Number,default:100},level:{type:String,default:ye,validator:function(e){return Ie(e)}},background:{type:String,default:"#fff"},foreground:{type:String,default:"#000"},margin:{type:Number,required:!1,default:0}},Ne=X(X({},ie),{renderAs:{type:String,required:!1,default:"canvas",validator:function(e){return["canvas","svg"].indexOf(e)>-1}}}),Ve=te({name:"QRCodeSvg",props:ie,setup:function(e){var t=ee(0),a=ee(""),p=function(){var n=e.value,d=e.level,o=e.margin,s=K.QrCode.encodeText(n,se[d]).getModules();t.value=s.length+o*2,a.value=ve(s,o)};return p(),de(p),function(){return H("svg",{width:e.size,height:e.size,"shape-rendering":"crispEdges",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 ".concat(t.value," ").concat(t.value)},[H("path",{fill:e.background,d:"M0,0 h".concat(t.value,"v").concat(t.value,"H0z")}),H("path",{fill:e.foreground,d:a.value})])}}}),Ue=te({name:"QRCodeCanvas",props:ie,setup:function(e){var t=ee(null),a=function(){var p=e.value,n=e.level,d=e.size,o=e.margin,s=e.background,i=e.foreground,r=t.value;if(r){var l=r.getContext("2d");if(l){var u=K.QrCode.encodeText(p,se[n]).getModules(),g=u.length+o*2,v=window.devicePixelRatio||1,q=d/g*v;r.height=r.width=d*v,l.scale(q,q),l.fillStyle=s,l.fillRect(0,0,g,g),l.fillStyle=i,Te?l.fill(new Path2D(ve(u,o))):u.forEach(function(w,M){w.forEach(function(A,T){A&&l.fillRect(T+o,M+o,1,1)})})}}};return ke(a),de(a),function(){return H("canvas",{ref:t,style:{width:"".concat(e.size,"px"),height:"".concat(e.size,"px")}})}}}),Be=te({name:"Qrcode",render:function(){var e=this.$props,t=e.renderAs,a=e.value,p=e.size,n=e.margin,d=e.level,o=e.background,s=e.foreground,i=p>>>0,r=n>>>0,l=Ie(d)?d:ye;return H(t==="svg"?Ve:Ue,{value:a,size:i,margin:r,level:l,background:o,foreground:s})},props:Ne});const xe={name:"QRCODE",components:{QrcodeVue:Be},props:{participantGroupId:{type:String,default:""},userId:{type:String,default:""},type:{type:String,default:""}},data(){return{polling:void 0,requestId:"",qrValue:"",inshow:!1,iswitch:!1,isactive:!0,size:160}},created(){this.qrValue="",this.requestId="",this.timeOut()},watch:{participantGroupId:{handler(e,t){e&&this.getRequestId()},immediate:!0}},methods:{getRequestId(){let e={participantGroupId:this.participantGroupId,participantTypes:this.type==="login"?"":"01",participantKeyword:this.userId};Se(e).then(t=>{this.requestId=t.data.requestId,this.$emit("requestId",this.requestId),this.qrValue=t.data.qrcode,this.startPolling()}).catch(t=>{this.$emit("generateRequestIdFailCallbackFunc",t)})},startPolling(){if(!this.requestId){this.stopPolling();return}G(this.requestId).then(e=>{if(console.log("result: ",e),e.data==="DOING"&&this.isactive){let t=this;window.setTimeout(t.startPolling,1e3);return}switch(e.data){case"FAILED":this.alert.error(e.data.errorMsg||"系统错误");break;case"SUCCESS":this.Complete();break}this.inshow=!0,this.stopPolling(),this.requestId=""}).catch(()=>{this.$emit("childEventCancel",this.requestId)})},stopPolling(){this.isactive=!1},Complete(){this.stopPolling(),this.$emit("mfaCallbackFunc",{requestId:this.requestId,type:"qrcode"})},cancel(){if(this.stopPolling(),this.requestId){let e=this.requestId;this.requestId="",P(e)}},refresh(){this.inshow=!1,this.isactive=!0,this.iswitch=!1,this.getRequestId(),this.timeOut()},timeOut(){setTimeout(()=>{this.inshow=!0,this.iswitch=!0,this.stopPolling()},3e5)},close(){this.stopPolling(),this.inshow=!0,this.iswitch=!0}}},Oe={key:1,class:"qrcode-loading"};function ze(e,t,a,p,n,d){const o=_("qrcode-vue"),s=_("RefreshRight"),i=_("el-icon");return m(),f("div",{class:b(a.type?"mfa-contentLogin":"mfa-content")},[n.qrValue?(m(),f("div",{key:0,class:"qrcode-modal",style:V(!a.type&&"margin: 20px;")},[c(o,{value:n.qrValue,size:n.size},null,8,["value","size"])],4)):(m(),f("div",Oe,"正在加载中...")),Ae(e.$slots,"default",{},void 0,!0),W(h("div",{class:b(a.type?"CambiumLogin":"Cambium"),onClick:t[0]||(t[0]=(...r)=>d.refresh&&d.refresh(...r))},[c(i,null,{default:I(()=>[c(s)]),_:1}),W(h("p",null,"二维码已过期",512),[[le,n.iswitch]])],2),[[le,n.inshow]])],2)}const Yi=E(xe,[["render",ze],["__scopeId","data-v-c7a7db6d"]]),Le={setup(){return{openUserAgreement:Ee("openUserAgreement")}}},De={class:"loginBtn_title"};function Ke(e,t,a,p,n,d){return m(),f("div",null,[h("span",De,[y("登录即默认接受 "),h("strong",{class:"str",onClick:t[0]||(t[0]=(...o)=>p.openUserAgreement&&p.openUserAgreement(...o))},"用户协议")])])}const R=E(Le,[["render",Ke]]),Ge="/assets/images/mfa/fingerprint-auth.png";const Qe={name:"FINGERPRINT",components:{userAgreement:R},props:{participantGroupId:{type:String,default:""},userId:{type:String,default:""},type:{type:String,default:""},name:{type:String,default:""}},data(){return{polling:void 0,requestId:"",src:Ge,code:"fingerprint",user:{username:this.name},firstStep:!0}},created(){this.type!=="login"&&this.generateRequestId()},methods:{generateRequestId(){let e={participantTypes:this.type==="login"?"03":"01,03",participantKeyword:this.userId||this.user.username};F(this.participantGroupId,e.participantTypes,e.participantKeyword,this.code).then(t=>{this.requestId=t.data,this.$emit("requestId",this.requestId),this.startPolling(),this.firstStep=!1}).catch(t=>{this.$emit("generateRequestIdFailCallbackFunc",t),this.$emit("childEventCancel",this.requestId)})},startPolling(){this.polling=setInterval(()=>{if(!this.requestId){this.stopPolling();return}G(this.requestId).then(e=>{if(e.data!=="DOING"){switch(e.data){case"FAILED":this.alert.error(e.data.errorMsg||"系统错误");break;case"SUCCESS":this.Complete();break}this.stopPolling(),this.requestId=""}}).catch(()=>{this.requestId="",this.cancel(),this.$emit("cancelMfaCallbackFunc")})},1e3)},stopPolling(){this.polling&&clearInterval(this.polling)},Complete(){this.stopPolling(),this.$emit("mfaCallbackFunc",{requestId:this.requestId,type:this.code})},cancel(){if(this.stopPolling(),this.requestId){let e=this.requestId;this.requestId="",P(e)}this.firstStep=!0}}},He=e=>(z("data-v-a1451eee"),e=e(),L(),e),$e={class:"mfa-content"},Ye={key:0},Je={key:1},We={class:"inputItem"},Xe={class:"inputContent"},Ze={key:2},je=["src"],et=He(()=>h("div",{class:"auth-tip"},[y(" 请打开"),h("span",{class:"mfa-text"},"【安全令APP】"),y("验证指纹 ")],-1)),tt={key:0,style:{"font-size":"12px"}};function st(e,t,a,p,n,d){const o=_("el-input"),s=_("el-button"),i=_("userAgreement"),r=_("EditPen"),l=_("el-icon");return m(),f("div",$e,[a.type?(m(),f("h3",Ye,"指纹登录")):C("",!0),n.firstStep&&a.type==="login"?(m(),f("div",Je,[h("div",We,[h("div",Xe,[c(o,{ref:"username",modelValue:n.user.username,"onUpdate:modelValue":t[0]||(t[0]=u=>n.user.username=u),placeholder:"请输入用户名"},null,8,["modelValue"])])]),c(s,{ref:"nextStepBtn",class:"Btn",disabled:!n.user.username,onClick:d.generateRequestId},{default:I(()=>[y(" 下一步 ")]),_:1},8,["disabled","onClick"]),c(i)])):(m(),f("div",Ze,[h("img",{src:n.src,class:b(a.type?"mfa-content-img":"mfa-content-imgmode"),alt:""},null,10,je),et,a.type?(m(),f("p",tt,[y(" 用户: "),h("span",{onClick:t[1]||(t[1]=(...u)=>d.cancel&&d.cancel(...u)),style:{color:"#f9780c",cursor:"pointer"}},[y(" "+k(n.user.username)+"  ",1),c(l,{color:"#F9780C",style:{"vertical-align":"middle"}},{default:I(()=>[c(r)]),_:1})])])):C("",!0)]))])}const Ji=E(Qe,[["render",st],["__scopeId","data-v-a1451eee"]]);const it={name:"SMSOTP",components:{userAgreement:R},props:{participantGroupId:{type:String,default:""},userId:{type:String,default:""},type:{type:String,default:""},name:{type:String,default:""}},data(){return{requestId:"",smsCode:"",sendCode:"获取验证码",countdown:void 0,disabled:!1,defaultTime:60,reg:/^\d+$/,username:this.name,btnLoading:!1,subbmitting:!1}},created(){},methods:{getSMSCode(){if(!this.username&&this.type==="login"){this.alert.error("请输入手机号");return}this.btnLoading=!0;let e={participantGroupId:this.participantGroupId,participantTypes:this.type==="login"?"04":"01,03",participantKeyword:this.userId||this.username};we(e).then(t=>{this.requestId=t.data.requestId,this.$emit("requestId",this.requestId),this.alert.success("短信已发送至手机"+t.data.phone+"，请注意查收。"),this.countdown=setInterval(this.timer,1e3)}).catch(t=>{this.btnLoading=!1,this.$emit("childEventCancel",this.requestId),this.$emit("generateRequestIdFailCallbackFunc",t)})},timer(){this.btnLoading=!1,this.disabled=!0,this.sendCode=this.type==="login"?"重新获取("+this.defaultTime+")":this.defaultTime+"s",this.defaultTime--,this.defaultTime<0&&(clearInterval(this.countdown),this.disabled=!1,this.sendCode="重新获取",this.defaultTime=60)},async submitAuth(){try{this.subbmitting=!0,await this.Complete()}finally{this.subbmitting=!1}},Complete(){if(!this.username&&this.type==="login"){this.alert.error("请输入手机号码");return}if(!this.smsCode){this.alert.error("请输入短信验证码");return}if(this.smsCode&&!this.reg.test(this.smsCode)){this.alert.error("验证码格式错误");return}if(!this.requestId){this.alert.error("请发送短信验证码");return}return Pe({requestId:this.requestId,code:this.smsCode}).then(()=>{this.$emit("mfaCallbackFunc",{requestId:this.requestId,type:this.smsCode})}).catch(e=>{let t=JSON.parse(e.message);(t.data.messageKey==="MFA.MFA.AUTH.CANCELED"||t.data.messageKey==="MFA.MFA.AUTH.ALREADY")&&(this.alert.error("认证已失效，请重新发送验证码"),clearInterval(this.countdown),this.disabled=!1,this.sendCode="重新获取",this.defaultTime=60)})},cancel(){if(this.requestId){let e=this.requestId;this.requestId="",P(e)}}},watch:{username(e,t){console.log(e),e&&(this.smsCode="")}}},nt={class:"auth-code-content"},rt={key:0},at={class:"inputItem"},ot={class:"inputContent"},lt={class:"otp-item"};function ut(e,t,a,p,n,d){const o=_("el-input"),s=_("el-button"),i=_("userAgreement");return m(),f("div",{class:b(["mfa-content",a.type?"sms_login":"sms_auth"])},[h("div",nt,[a.type?(m(),f("div",rt,[h("div",at,[h("div",ot,[c(o,{ref:"username",modelValue:n.username,"onUpdate:modelValue":t[0]||(t[0]=r=>n.username=r),placeholder:"请输入手机号"},null,8,["modelValue"])])])])):C("",!0),h("div",lt,[c(o,{modelValue:n.smsCode,"onUpdate:modelValue":t[1]||(t[1]=r=>n.smsCode=r),placeholder:"请输入验证码",class:b(a.type==="login"?"auth-input":"auth-code-input"),maxlength:"6"},{append:I(()=>[c(s,{disabled:n.disabled,class:"send-code-button",size:"small",style:{"font-size":"12px"},onClick:d.getSMSCode,loading:n.btnLoading},{default:I(()=>[y(k(n.sendCode),1)]),_:1},8,["disabled","onClick","loading"])]),_:1},8,["modelValue","class"])]),c(s,{ref:"confirmBtn",type:"primary",class:b({loginBtn:a.type==="login","mfa-confirm-btn":a.type!=="login"}),disabled:a.type==="login"&&!n.username||!n.requestId||!n.smsCode||n.subbmitting,onClick:d.submitAuth},{default:I(()=>[y(k(a.type==="login"?"登录":"确定"),1)]),_:1},8,["class","disabled","onClick"]),a.type?(m(),B(i,{key:1})):C("",!0)])],2)}const Wi=E(it,[["render",ut],["__scopeId","data-v-6e2e9b0e"]]);const dt={name:"EMAILOTP",components:{userAgreement:R},props:{participantGroupId:{type:String,default:""},userId:{type:String,default:""},type:{type:String,default:""},name:{type:String,default:""}},data(){return{requestId:"",emailCode:"",sendCode:"获取验证码",countdown:void 0,disabled:!1,defaultTime:60,reg:/^\d+$/,username:this.name,btnLoading:!1,subbmitting:!1}},created(){},methods:{getEmailCode(){if(!this.username&&this.type==="login"){this.alert.error("请输入邮箱");return}this.btnLoading=!0;let e={participantGroupId:this.participantGroupId,participantTypes:this.type==="login"?"05":"01,03",participantKeyword:this.userId||this.username};Re(e).then(t=>{this.requestId=t.data.requestId,this.$emit("requestId",this.requestId),this.alert.success("邮件已发送至邮箱"+t.data.email+"，请注意查收。"),this.countdown=setInterval(this.timer,1e3)}).catch(t=>{this.btnLoading=!1,this.$emit("generateRequestIdFailCallbackFunc",t)})},timer(){this.btnLoading=!1,this.disabled=!0,this.sendCode=this.type==="login"?"重新获取("+this.defaultTime+")":this.defaultTime+"s",this.defaultTime--,this.defaultTime<0&&(clearInterval(this.countdown),this.disabled=!1,this.sendCode="重新获取",this.defaultTime=60)},async submitAuth(){try{this.subbmitting=!0,await this.Complete()}finally{this.subbmitting=!1}},Complete(){if(!this.username&&this.type==="login"){this.alert.error("请输入邮箱");return}if(!this.emailCode){this.alert.error("请输入邮箱验证码");return}if(this.emailCode&&!this.reg.test(this.emailCode)){this.alert.error("验证码格式错误");return}if(!this.requestId){this.alert.error("请发送邮箱验证码");return}return Me({requestId:this.requestId,code:this.emailCode}).then(()=>{this.$emit("mfaCallbackFunc",{requestId:this.requestId,type:this.emailCode})}).catch(e=>{this.$emit("childEventCancel",this.requestId);let t=JSON.parse(e.message);(t.data.messageKey==="MFA.MFA.AUTH.CANCELED"||t.data.messageKey==="MFA.MFA.AUTH.ALREADY")&&(this.alert.error("认证已失效，请重新发送验证码"),clearInterval(this.countdown),this.disabled=!1,this.sendCode="重新获取",this.defaultTime=60)})},cancel(){if(this.requestId){let e=this.requestId;this.requestId="",P(e)}}},watch:{username(e,t){console.log(e),e&&(this.emailCode="")}}},ct={key:0},ht={class:"auth-code-content"},pt={key:0},mt={class:"inputItem"},ft={class:"inputContent"},_t={class:"otp-item"};function gt(e,t,a,p,n,d){const o=_("el-input"),s=_("el-button"),i=_("userAgreement");return m(),f("div",{class:"mfa-content",style:V(a.type?"":"margin-top: 61px; padding-bottom: 61px;")},[a.type?(m(),f("h3",ct,"邮箱登录")):C("",!0),h("div",ht,[a.type?(m(),f("div",pt,[h("div",mt,[h("div",ft,[c(o,{ref:"username",modelValue:n.username,"onUpdate:modelValue":t[0]||(t[0]=r=>n.username=r),placeholder:"请输入邮箱号"},null,8,["modelValue"])])])])):C("",!0),h("div",_t,[c(o,{modelValue:n.emailCode,"onUpdate:modelValue":t[1]||(t[1]=r=>n.emailCode=r),placeholder:"请输入验证码",class:b(a.type?"auth-code-input":"auth-input"),maxlength:"6"},{append:I(()=>[c(s,{disabled:n.disabled,class:"send-code-button",size:"small",style:{"font-size":"12px"},onClick:d.getEmailCode,loading:n.btnLoading},{default:I(()=>[y(k(n.sendCode),1)]),_:1},8,["disabled","onClick","loading"])]),_:1},8,["modelValue","class"])]),c(s,{ref:"confirmBtn",type:"primary",class:b({loginBtn:a.type==="login","mfa-confirm-btn":a.type!=="login"}),disabled:a.type==="login"&&!n.username||!n.requestId||!n.emailCode||n.subbmitting,onClick:d.submitAuth},{default:I(()=>[y(k(a.type==="login"?"登录":"确定"),1)]),_:1},8,["class","disabled","onClick"]),a.type?(m(),B(i,{key:1})):C("",!0)])],4)}const Xi=E(dt,[["render",gt],["__scopeId","data-v-dad63076"]]),yt="/assets/images/mfa/voice-auth.png";const It={name:"VOICE",components:{userAgreement:R},props:{participantGroupId:{type:String,default:""},userId:{type:String,default:""},type:{type:String,default:""},name:{type:String,default:""}},data(){return{polling:void 0,requestId:"",src:yt,code:"voice",user:{username:this.name},firstStep:!0}},created(){this.type!=="login"&&this.generateRequestId()},methods:{generateRequestId(){let e={participantTypes:this.type==="login"?"03":"01,03",participantKeyword:this.userId||this.user.username};F(this.participantGroupId,e.participantTypes,e.participantKeyword,this.code).then(t=>{this.requestId=t.data,this.$emit("requestId",this.requestId),this.startPolling(),this.firstStep=!1}).catch(t=>{this.$emit("generateRequestIdFailCallbackFunc",t)})},startPolling(){this.polling=setInterval(()=>{if(!this.requestId){this.stopPolling();return}G(this.requestId).then(e=>{if(e.data!=="DOING"){switch(e.data){case"FAILED":this.alert.error(e.data.errorMsg||"系统错误");break;case"SUCCESS":this.Complete();break}this.stopPolling(),this.requestId=""}}).catch(()=>{this.requestId="",this.cancel(),this.$emit("childEventCancel",this.requestId),this.$emit("cancelMfaCallbackFunc")})},1e3)},stopPolling(){this.polling&&clearInterval(this.polling)},Complete(){this.stopPolling(),this.$emit("mfaCallbackFunc",{requestId:this.requestId,type:this.code})},cancel(){if(this.stopPolling(),this.requestId){let e=this.requestId;this.requestId="",P(e)}this.firstStep=!0}}},vt=e=>(z("data-v-a9009dc3"),e=e(),L(),e),Ct={class:"mfa-content"},bt={key:0},qt={key:1},kt={class:"inputItem"},St={class:"inputContent"},At={key:2},Et=["src"],wt=vt(()=>h("div",{class:"auth-tip"},[y(" 请打开"),h("span",{class:"mfa-text"},"【安全令APP】"),y("验证声纹 ")],-1)),Pt={key:0,style:{"font-size":"12px"}};function Rt(e,t,a,p,n,d){const o=_("el-input"),s=_("el-button"),i=_("userAgreement"),r=_("EditPen"),l=_("el-icon");return m(),f("div",Ct,[a.type?(m(),f("h3",bt,"声纹登录")):C("",!0),n.firstStep&&a.type==="login"?(m(),f("div",qt,[h("div",kt,[h("div",St,[c(o,{ref:"username",modelValue:n.user.username,"onUpdate:modelValue":t[0]||(t[0]=u=>n.user.username=u),placeholder:"请输入用户名"},null,8,["modelValue"])])]),c(s,{ref:"nextStepBtn",class:"Btn",disabled:!n.user.username,onClick:d.generateRequestId},{default:I(()=>[y(" 下一步 ")]),_:1},8,["disabled","onClick"]),c(i)])):(m(),f("div",At,[h("img",{src:n.src,class:b(a.type?"mfa-content-img":"mfa-content-imgmode"),alt:""},null,10,Et),wt,a.type?(m(),f("p",Pt,[y(" 用户: "),h("span",{onClick:t[1]||(t[1]=(...u)=>d.cancel&&d.cancel(...u)),style:{color:"#f9780c",cursor:"pointer"}},[y(" "+k(n.user.username)+"  ",1),c(l,{color:"#F9780C",style:{"vertical-align":"middle",cursor:"pointer"}},{default:I(()=>[c(r)]),_:1})])])):C("",!0)]))])}const Zi=E(It,[["render",Rt],["__scopeId","data-v-a9009dc3"]]),Mt="/assets/images/mfa/nativepass-auth.png";const Ft={name:"NATIVEPASS",components:{userAgreement:R},props:{participantGroupId:{type:String,default:""},userId:{type:String,default:""},type:{type:String,default:""},name:{type:String,default:""}},data(){return{polling:void 0,requestId:"",src:Mt,code:"nativepass",user:{username:this.name},firstStep:!0}},created(){this.type!=="login"&&this.generateRequestId()},methods:{generateRequestId(){let e={participantTypes:this.type==="login"?"03":"01,03",participantKeyword:this.userId||this.user.username};F(this.participantGroupId,e.participantTypes,e.participantKeyword,this.code).then(t=>{this.requestId=t.data,this.$emit("requestId",this.requestId),this.startPolling(),this.firstStep=!1}).catch(t=>{this.$emit("generateRequestIdFailCallbackFunc",t)})},startPolling(){this.polling=setInterval(()=>{if(!this.requestId){this.stopPolling();return}G(this.requestId).then(e=>{if(e.data!=="DOING"){switch(e.data){case"FAILED":this.alert.error(e.data.errorMsg||"系统错误");break;case"SUCCESS":this.Complete();break}this.stopPolling(),this.requestId=""}}).catch(()=>{this.requestId="",this.cancel(),this.$emit("childEventCancel",this.requestId),this.$emit("cancelMfaCallbackFunc")})},1e3)},stopPolling(){this.polling&&clearInterval(this.polling)},Complete(){this.stopPolling(),this.$emit("mfaCallbackFunc",{requestId:this.requestId,type:this.code})},cancel(){if(this.stopPolling(),this.requestId){let e=this.requestId;this.requestId="",P(e)}this.firstStep=!0}}},Tt=e=>(z("data-v-dc5bbc80"),e=e(),L(),e),Nt={class:"mfa-content"},Vt={key:0},Ut={key:1},Bt={class:"inputItem"},xt={class:"inputContent"},Ot={key:2},zt=["src"],Lt=Tt(()=>h("div",{class:"auth-tip"},[y(" 请打开"),h("span",{class:"mfa-text"},"【安全令APP】"),y("验证密码 ")],-1)),Dt={key:0,style:{"font-size":"12px"}};function Kt(e,t,a,p,n,d){const o=_("el-input"),s=_("el-button"),i=_("userAgreement"),r=_("EditPen"),l=_("el-icon");return m(),f("div",Nt,[a.type?(m(),f("h3",Vt,"验证密码登录")):C("",!0),n.firstStep&&a.type==="login"?(m(),f("div",Ut,[h("div",Bt,[h("div",xt,[c(o,{ref:"username",modelValue:n.user.username,"onUpdate:modelValue":t[0]||(t[0]=u=>n.user.username=u),placeholder:"请输入用户名"},null,8,["modelValue"])])]),c(s,{ref:"nextStepBtn",class:"Btn",disabled:!n.user.username,onClick:d.generateRequestId},{default:I(()=>[y(" 下一步 ")]),_:1},8,["disabled","onClick"]),c(i)])):(m(),f("div",Ot,[h("img",{src:n.src,class:b(a.type?"mfa-content-img":"mfa-content-imgmode"),alt:""},null,10,zt),Lt,a.type?(m(),f("p",Dt,[y(" 用户: "),h("span",{onClick:t[1]||(t[1]=(...u)=>d.cancel&&d.cancel(...u)),style:{color:"#f9780c",cursor:"pointer"}},[y(" "+k(n.user.username)+"  ",1),c(l,{color:"#F9780C",style:{"vertical-align":"middle"}},{default:I(()=>[c(r)]),_:1})])])):C("",!0)]))])}const ji=E(Ft,[["render",Kt],["__scopeId","data-v-dc5bbc80"]]);const Gt={name:"AUTHCODE",components:{userAgreement:R},props:{participantGroupId:{type:String,default:""},userId:{type:String,default:""},type:{type:String,default:""},name:{type:String,default:""}},data(){return{requestId:"",authCode:"",code:"auth_code",reg:/^\d+$/,user:{username:this.name},firstStep:!0,timer:void 0,submitting:!1}},created(){this.type!=="login"&&this.generateRequestId()},watch:{participantGroupId:{handler(e,t){e&&this.requestId&&this.generateRequestId()},immediate:!0}},mounted(){},methods:{generateRequestId(){let e={participantTypes:this.type==="login"?"03":"01,03",participantKeyword:this.userId||this.user.username};F(this.participantGroupId,e.participantTypes,e.participantKeyword,this.code).then(t=>{this.requestId=t.data,console.log("更新id"),this.$emit("requestId",t.data),this.firstStep=!1}).catch(t=>{this.$emit("generateRequestIdFailCallbackFunc",t)})},async submitAuth(){try{this.subbmitting=!0,await this.Complete()}finally{this.subbmitting=!1}},Complete(){if(!this.authCode){this.alert.error("请输入动态口令");return}if(this.authCode&&!this.reg.test(this.authCode)){this.alert.error("动态口令格式错误");return}return Fe({requestId:this.requestId,authCode:this.authCode}).then(()=>{this.$emit("mfaCallbackFunc",{requestId:this.requestId,type:this.code})}).catch(e=>{this.$emit("childEventCancel",this.requestId);let t=JSON.parse(e.message);(t.data.messageKey=="MFA.MFA.AUTH.CANCELED"||t.data.messageKey=="MFA.MFA.AUTH.ALREADY")&&(this.alert.error("认证已失效，请重试"),this.type&&setTimeout(this.cancel,1e3))})},cancel(){if(this.requestId){let e=this.requestId;this.requestId="",P(e)}this.firstStep=!0}}},Qt={class:"mfa-content"},Ht={key:0},$t={key:1},Yt={class:"inputItem"},Jt={class:"inputContent"},Wt={class:"auth-code-content border"},Xt={key:1,style:{"font-size":"12px"}};function Zt(e,t,a,p,n,d){const o=_("el-input"),s=_("el-button"),i=_("userAgreement"),r=_("EditPen"),l=_("el-icon");return m(),f("div",Qt,[a.type?(m(),f("h3",Ht,"动态口令登录")):C("",!0),n.firstStep&&a.type==="login"?(m(),f("div",$t,[h("div",Yt,[h("div",Jt,[c(o,{ref:"username",modelValue:n.user.username,"onUpdate:modelValue":t[0]||(t[0]=u=>n.user.username=u),placeholder:"请输入用户名"},null,8,["modelValue"])])]),c(s,{ref:"nextStepBtn",class:"Btn",disabled:!n.user.username,onClick:d.generateRequestId},{default:I(()=>[y(" 下一步 ")]),_:1},8,["disabled","onClick"]),c(i)])):(m(),f("div",{key:2,style:V([{"margin-top":"61px"},a.type!=="login"&&"padding-bottom: 61px"])},[h("div",Wt,[c(o,{modelValue:n.authCode,"onUpdate:modelValue":t[1]||(t[1]=u=>n.authCode=u),class:b(a.type?"auth-code-input":"auth-input"),placeholder:"请输入动态口令",maxlength:"6"},null,8,["modelValue","class"])]),c(s,{ref:"confirmBtn",type:"primary",class:b({loginBtn:a.type==="login","mfa-confirm-btn":a.type!=="login"}),disabled:!n.authCode||n.submitting,onClick:d.submitAuth},{default:I(()=>[y(k(a.type==="login"?"登录":"确定"),1)]),_:1},8,["class","disabled","onClick"]),a.type?(m(),B(i,{key:0})):C("",!0),a.type?(m(),f("p",Xt,[y(" 用户: "),h("span",{onClick:t[2]||(t[2]=(...u)=>d.cancel&&d.cancel(...u)),style:{color:"#f9780c",cursor:"pointer"}},[y(" "+k(n.user.username)+"  ",1),c(l,{color:"#F9780C",style:{"vertical-align":"middle"}},{default:I(()=>[c(r)]),_:1})])])):C("",!0)],4))])}const en=E(Gt,[["render",Zt],["__scopeId","data-v-11146795"]]),jt="/assets/images/mfa/mobileclick-auth.png";const es={name:"MOBILECLICK",components:{userAgreement:R},props:{participantGroupId:{type:String,default:""},userId:{type:String,default:""},type:{type:String,default:""},name:{type:String,default:""}},data(){return{polling:void 0,requestId:"",src:jt,code:"mobileclick",user:{username:this.name},firstStep:!0}},created(){this.type!=="login"&&this.generateRequestId()},methods:{generateRequestId(){let e={participantTypes:this.type==="login"?"03":"01,03",participantKeyword:this.userId||this.user.username};F(this.participantGroupId,e.participantTypes,e.participantKeyword,this.code).then(t=>{this.requestId=t.data,this.$emit("requestId",this.requestId),this.startPolling(),this.firstStep=!1}).catch(t=>{this.$emit("generateRequestIdFailCallbackFunc",t)})},startPolling(){this.polling=setInterval(()=>{if(!this.requestId){this.stopPolling();return}G(this.requestId).then(e=>{if(e.data!=="DOING"){switch(e.data){case"FAILED":this.alert.error(e.data.errorMsg||"系统错误");break;case"SUCCESS":this.Complete();break}this.stopPolling(),this.requestId=""}}).catch(()=>{this.requestId="",this.cancel(),this.$emit("childEventCancel",this.requestId),this.$emit("cancelMfaCallbackFunc")})},1e3)},stopPolling(){this.polling&&clearInterval(this.polling)},Complete(){this.stopPolling(),this.$emit("mfaCallbackFunc",{requestId:this.requestId,type:this.code})},cancel(){if(this.stopPolling(),this.requestId){let e=this.requestId;this.requestId="",P(e)}this.firstStep=!0}}},ts=e=>(z("data-v-d1cbe6be"),e=e(),L(),e),ss={class:"mfa-content"},is={key:0},ns={key:1},rs={class:"inputItem"},as={class:"inputContent"},os={key:2},ls=["src"],us=ts(()=>h("div",{class:"auth-tip"},[y(" 请打开"),h("span",{class:"mfa-text"},"【安全令APP】"),y("进行验证 ")],-1)),ds={key:0,style:{"font-size":"12px"}};function cs(e,t,a,p,n,d){const o=_("el-input"),s=_("el-button"),i=_("userAgreement"),r=_("EditPen"),l=_("el-icon");return m(),f("div",ss,[a.type==="login"?(m(),f("h3",is,"推送登录")):C("",!0),n.firstStep&&a.type==="login"?(m(),f("div",ns,[h("div",rs,[h("div",as,[c(o,{ref:"username",modelValue:n.user.username,"onUpdate:modelValue":t[0]||(t[0]=u=>n.user.username=u),placeholder:"手机登录, 请输入用户名"},null,8,["modelValue"])])]),c(s,{ref:"nextStepBtn",class:"Btn",disabled:!n.user.username,onClick:d.generateRequestId},{default:I(()=>[y(" 下一步 ")]),_:1},8,["disabled","onClick"]),c(i)])):(m(),f("div",os,[h("img",{src:n.src,class:b(a.type?"mfa-content-img":"mfa-content-imgmode"),alt:""},null,10,ls),us,a.type?(m(),f("p",ds,[y(" 用户: "),h("span",{onClick:t[1]||(t[1]=(...u)=>d.cancel&&d.cancel(...u)),style:{color:"#f9780c",cursor:"pointer"}},[y(" "+k(n.user.username)+"  ",1),c(l,{color:"#F9780C",style:{"vertical-align":"middle"}},{default:I(()=>[c(r)]),_:1})])])):C("",!0)]))])}const tn=E(es,[["render",cs],["__scopeId","data-v-d1cbe6be"]]);const hs={name:"TFC200",components:{userAgreement:R},props:{participantGroupId:{type:String,default:""},userId:{type:String,default:""},type:{type:String,default:""},name:{type:String,default:""}},data(){return{requestId:"",authCode:"",code:"ft_c200",reg:/^\d+$/,user:{username:this.name},firstStep:!0,submitting:!1}},created(){this.type!=="login"&&this.generateRequestId()},mounted(){},methods:{generateRequestId(){let e={participantTypes:this.type==="login"?"03":"01,03",participantKeyword:this.userId||this.user.username};F(this.participantGroupId,e.participantTypes,e.participantKeyword,this.code).then(t=>{this.requestId=t.data,this.$emit("requestId",this.requestId),this.firstStep=!1}).catch(t=>{this.$emit("generateRequestIdFailCallbackFunc",t)})},async submitAuth(){try{this.subbmitting=!0,await this.Complete()}finally{this.subbmitting=!1}},Complete(){if(!this.authCode){this.alert.error("请输入动态口令");return}if(this.authCode&&!this.reg.test(this.authCode)){this.alert.error("动态口令格式错误");return}return $({requestId:this.requestId,authCode:this.authCode,method:this.code}).then(()=>{this.$emit("mfaCallbackFunc",{requestId:this.requestId,type:this.code})}).catch(e=>{this.$emit("childEventCancel",this.requestId);let t=JSON.parse(err.message);(t.data.messageKey=="MFA.MFA.AUTH.CANCELED"||t.data.messageKey=="MFA.MFA.AUTH.ALREADY")&&(this.alert.error("认证已失效，请重试"),this.type&&setTimeout(this.cancel,1e3))})},cancel(){if(this.requestId){let e=this.requestId;this.requestId="",P(e)}this.firstStep=!0}}},ps={class:"mfa-content"},ms={key:0},fs={key:1},_s={class:"inputItem"},gs={class:"inputContent"},ys={key:2,style:{"margin-top":"61px"}},Is={class:"auth-code-content border"},vs={key:1,style:{"font-size":"12px"}};function Cs(e,t,a,p,n,d){const o=_("el-input"),s=_("el-button"),i=_("userAgreement"),r=_("EditPen"),l=_("el-icon");return m(),f("div",ps,[a.type?(m(),f("h3",ms,"硬件令牌登录")):C("",!0),n.firstStep&&a.type?(m(),f("div",fs,[h("div",_s,[h("div",gs,[c(o,{ref:"username",modelValue:n.user.username,"onUpdate:modelValue":t[0]||(t[0]=u=>n.user.username=u),placeholder:"请输入用户名"},null,8,["modelValue"])])]),c(s,{ref:"nextStepBtn",class:"Btn",disabled:!n.user.username,onClick:d.generateRequestId},{default:I(()=>[y(" 下一步 ")]),_:1},8,["disabled","onClick"]),c(i)])):(m(),f("div",ys,[h("div",Is,[c(o,{modelValue:n.authCode,"onUpdate:modelValue":t[1]||(t[1]=u=>n.authCode=u),class:b(a.type==="login"?"auth-code-input":"auth-input"),placeholder:"请输入动态码",maxlength:"6"},null,8,["modelValue","class"])]),c(s,{ref:"confirmBtn",type:"primary",class:b({loginBtn:a.type==="login","mfa-confirm-btn":a.type!=="login"}),disabled:!n.authCode||n.submitting,onClick:d.submitAuth},{default:I(()=>[y(k(a.type==="login"?"登录":"确定"),1)]),_:1},8,["class","disabled","onClick"]),a.type?(m(),B(i,{key:0})):C("",!0),a.type?(m(),f("p",vs,[y(" 用户: "),h("span",{onClick:t[2]||(t[2]=(...u)=>d.cancel&&d.cancel(...u)),style:{color:"#f9780c",cursor:"pointer"}},[y(" "+k(n.user.username)+"  ",1),c(l,{color:"#F9780C",style:{"vertical-align":"middle"}},{default:I(()=>[c(r)]),_:1})])])):C("",!0)]))])}const sn=E(hs,[["render",Cs],["__scopeId","data-v-6b6c46b3"]]);const bs={name:"ANSHUA5",components:{userAgreement:R},props:{participantGroupId:{type:String,default:""},userId:{type:String,default:""},type:{type:String,default:""},name:{type:String,default:""}},data(){return{requestId:"",authCode:"",code:"anshu_a5",reg:/^\d+$/,user:{username:this.name},firstStep:!0,submitting:!1}},created(){this.type!=="login"&&this.generateRequestId()},mounted(){},methods:{generateRequestId(){let e={participantTypes:this.type==="login"?"03":"01,03",participantKeyword:this.userId||this.user.username};F(this.participantGroupId,e.participantTypes,e.participantKeyword,this.code).then(t=>{this.requestId=t.data,this.$emit("requestId",this.requestId),this.firstStep=!1}).catch(t=>{this.$emit("generateRequestIdFailCallbackFunc",t)})},async submitAuth(){try{this.subbmitting=!0,await this.Complete()}finally{this.subbmitting=!1}},Complete(){if(!this.authCode){this.alert.error("请输入动态口令");return}if(this.authCode&&!this.reg.test(this.authCode)){this.alert.error("动态口令格式错误");return}return $({requestId:this.requestId,authCode:this.authCode,method:this.code}).then(()=>{this.$emit("mfaCallbackFunc",{requestId:this.requestId,type:this.code})}).catch(e=>{let t=JSON.parse(e.message);this.$emit("childEventCancel",this.requestId),(t.data.messageKey=="MFA.MFA.AUTH.CANCELED"||t.data.messageKey=="MFA.MFA.AUTH.ALREADY")&&(this.alert.error("认证已失效，请重试"),this.type&&setTimeout(this.cancel,1e3))})},cancel(){if(this.requestId){let e=this.requestId;this.requestId="",P(e)}this.firstStep=!0}}},qs={class:"mfa-content"},ks={key:0},Ss={key:1},As={class:"inputItem"},Es={class:"inputContent"},ws={class:"auth-code-content border"},Ps={key:1,style:{"font-size":"12px"}};function Rs(e,t,a,p,n,d){const o=_("el-input"),s=_("el-button"),i=_("userAgreement"),r=_("EditPen"),l=_("el-icon");return m(),f("div",qs,[a.type?(m(),f("h3",ks,"硬件令牌登录")):C("",!0),n.firstStep&&a.type?(m(),f("div",Ss,[h("div",As,[h("div",Es,[c(o,{ref:"username",modelValue:n.user.username,"onUpdate:modelValue":t[0]||(t[0]=u=>n.user.username=u),placeholder:"请输入用户名"},null,8,["modelValue"])])]),c(s,{ref:"nextStepBtn",class:"Btn",disabled:!n.user.username,onClick:d.generateRequestId},{default:I(()=>[y(" 下一步 ")]),_:1},8,["disabled","onClick"]),c(i)])):(m(),f("div",{key:2,style:V([{"margin-top":"61px"},a.type!=="login"&&"padding-bottom: 61px"])},[h("div",ws,[c(o,{modelValue:n.authCode,"onUpdate:modelValue":t[1]||(t[1]=u=>n.authCode=u),class:b(a.type==="login"?"auth-code-input":"auth-input"),placeholder:"请输入动态码",maxlength:"6"},null,8,["modelValue","class"])]),c(s,{ref:"confirmBtn",type:"primary",class:b({loginBtn:a.type==="login","mfa-confirm-btn":a.type!=="login"}),disabled:!n.authCode||n.submitting,onClick:d.submitAuth},{default:I(()=>[y(k(a.type==="login"?"登录":"确定"),1)]),_:1},8,["class","disabled","onClick"]),a.type?(m(),B(i,{key:0})):C("",!0),a.type?(m(),f("p",Ps,[y(" 用户: "),h("span",{onClick:t[2]||(t[2]=(...u)=>d.cancel&&d.cancel(...u)),style:{color:"#f9780c",cursor:"pointer"}},[y(" "+k(n.user.username)+"  ",1),c(l,{color:"#F9780C",style:{"vertical-align":"middle"}},{default:I(()=>[c(r)]),_:1})])])):C("",!0)],4))])}const nn=E(bs,[["render",Rs],["__scopeId","data-v-93a6a6e3"]]);const Ms={name:"ESCUOTP1",components:{userAgreement:R},props:{participantGroupId:{type:String,default:""},userId:{type:String,default:""},type:{type:String,default:""},name:{type:String,default:""}},data(){return{requestId:"",authCode:"",code:"escuotp1",reg:/^\d+$/,user:{username:this.name},firstStep:!0,submitting:!1}},created(){this.type!=="login"&&this.generateRequestId()},mounted(){},methods:{generateRequestId(){let e={participantTypes:this.type==="login"?"03":"01,03",participantKeyword:this.userId||this.user.username};F(this.participantGroupId,e.participantTypes,e.participantKeyword,this.code).then(t=>{this.requestId=t.data,this.$emit("requestId",this.requestId),this.firstStep=!1}).catch(t=>{this.$emit("generateRequestIdFailCallbackFunc",t)})},async submitAuth(){try{this.subbmitting=!0,await this.Complete()}finally{this.subbmitting=!1}},Complete(){if(!this.authCode){this.alert.error("请输入动态口令");return}if(this.authCode&&!this.reg.test(this.authCode)){this.alert.error("动态口令格式错误");return}return $({requestId:this.requestId,authCode:this.authCode,method:this.code}).then(()=>{this.$emit("mfaCallbackFunc",{requestId:this.requestId,type:this.code})}).catch(e=>{this.$emit("childEventCancel",this.requestId),this.$emit("generateRequestIdFailCallbackFunc",e)})},cancel(){if(this.requestId){let e=this.requestId;this.requestId="",P(e)}this.firstStep=!0}}},Fs={class:"mfa-content"},Ts={key:0},Ns={key:1},Vs={class:"inputItem"},Us={class:"inputContent"},Bs={class:"auth-code-content border"},xs={key:1,style:{"font-size":"12px"}};function Os(e,t,a,p,n,d){const o=_("el-input"),s=_("el-button"),i=_("userAgreement"),r=_("EditPen"),l=_("el-icon");return m(),f("div",Fs,[a.type?(m(),f("h3",Ts,"硬件令牌登录")):C("",!0),n.firstStep&&a.type==="login"?(m(),f("div",Ns,[h("div",Vs,[h("div",Us,[c(o,{ref:"username",modelValue:n.user.username,"onUpdate:modelValue":t[0]||(t[0]=u=>n.user.username=u),placeholder:"请输入用户名"},null,8,["modelValue"])])]),c(s,{ref:"nextStepBtn",class:"Btn",disabled:!n.user.username,onClick:d.generateRequestId},{default:I(()=>[y(" 下一步 ")]),_:1},8,["disabled","onClick"]),c(i)])):(m(),f("div",{key:2,style:V([{"margin-top":"61px"},a.type!=="login"&&"padding-bottom: 61px"])},[h("div",Bs,[c(o,{modelValue:n.authCode,"onUpdate:modelValue":t[1]||(t[1]=u=>n.authCode=u),class:b(a.type=="login"?"auth-code-input":"auth-input"),placeholder:"请输入六位动态口令",maxlength:"6"},null,8,["modelValue","class"])]),c(s,{type:"primary",ref:"confirmBtn",class:b({loginBtn:a.type==="login","mfa-confirm-btn":a.type!=="login"}),disabled:!n.authCode||n.submitting,onClick:d.submitAuth},{default:I(()=>[y(k(a.type==="login"?"登录":"确定"),1)]),_:1},8,["class","disabled","onClick"]),a.type?(m(),B(i,{key:0})):C("",!0),a.type?(m(),f("p",xs,[y(" 用户: "),h("span",{onClick:t[2]||(t[2]=(...u)=>d.cancel&&d.cancel(...u)),style:{color:"#f9780c",cursor:"pointer"}},[y(" "+k(n.user.username)+"  ",1),c(l,{color:"#F9780C",style:{"vertical-align":"middle"}},{default:I(()=>[c(r)]),_:1})])])):C("",!0)],4))])}const rn=E(Ms,[["render",Os],["__scopeId","data-v-e8386693"]]);const zs={name:"ETZ203",components:{userAgreement:R},props:{participantGroupId:{type:String,default:""},userId:{type:String,default:""},type:{type:String,default:""},name:{type:String,default:""}},data(){return{requestId:"",authCode:"",code:"et_z203",reg:/^\d+$/,user:{username:this.name},firstStep:!0,submitting:!1}},created(){this.type!=="login"&&this.generateRequestId()},mounted(){},methods:{generateRequestId(){let e={participantTypes:this.type==="login"?"03":"01,03",participantKeyword:this.userId||this.user.username};F(this.participantGroupId,e.participantTypes,e.participantKeyword,this.code).then(t=>{this.requestId=t.data,this.$emit("requestId",this.requestId),this.firstStep=!1}).catch(t=>{this.$emit("generateRequestIdFailCallbackFunc",t)})},async submitAuth(){try{this.subbmitting=!0,await this.Complete()}finally{this.subbmitting=!1}},Complete(){if(!this.authCode){this.alert.error("请输入动态口令");return}if(this.authCode&&!this.reg.test(this.authCode)){this.alert.error("动态口令格式错误");return}return $({requestId:this.requestId,authCode:this.authCode,method:this.code}).then(()=>{this.$emit("mfaCallbackFunc",{requestId:this.requestId,type:this.code})}).catch(e=>{this.$emit("childEventCancel",this.requestId);let t=JSON.parse(e.message);(t.data.messageKey=="MFA.MFA.AUTH.CANCELED"||t.data.messageKey=="MFA.MFA.AUTH.ALREADY")&&(this.alert.error("认证已失效，请重试"),this.type&&setTimeout(this.cancel,1e3))})},cancel(){if(this.requestId){let e=this.requestId;this.requestId="",P(e)}this.firstStep=!0}}},Ls={class:"mfa-content"},Ds={key:0},Ks={key:1},Gs={class:"inputItem"},Qs={class:"inputContent"},Hs={class:"auth-code-content border"},$s={key:1,style:{"font-size":"12px"}};function Ys(e,t,a,p,n,d){const o=_("el-input"),s=_("el-button"),i=_("userAgreement"),r=_("EditPen"),l=_("el-icon");return m(),f("div",Ls,[a.type?(m(),f("h3",Ds,"硬件令牌登录")):C("",!0),n.firstStep&&a.type?(m(),f("div",Ks,[h("div",Gs,[h("div",Qs,[c(o,{ref:"username",modelValue:n.user.username,"onUpdate:modelValue":t[0]||(t[0]=u=>n.user.username=u),placeholder:"请输入用户名"},null,8,["modelValue"])])]),c(s,{ref:"nextStepBtn",class:"Btn",disabled:!n.user.username,onClick:d.generateRequestId},{default:I(()=>[y(" 下一步 ")]),_:1},8,["disabled","onClick"]),c(i)])):(m(),f("div",{key:2,style:V([{"margin-top":"61px"},a.type!=="login"&&"padding-bottom: 61px"])},[h("div",Hs,[c(o,{modelValue:n.authCode,"onUpdate:modelValue":t[1]||(t[1]=u=>n.authCode=u),class:b(a.type==="login"?"auth-code-input":"auth-input"),placeholder:"请输入动态码",maxlength:"6"},null,8,["modelValue","class"])]),c(s,{type:"primary",ref:"confirmBtn",class:b({loginBtn:a.type==="login","mfa-confirm-btn":a.type!=="login"}),disabled:!n.authCode||n.submitting,onClick:d.submitAuth},{default:I(()=>[y(k(a.type==="login"?"登录":"确定"),1)]),_:1},8,["class","disabled","onClick"]),a.type?(m(),B(i,{key:0})):C("",!0),a.type?(m(),f("p",$s,[y(" 用户: "),h("span",{onClick:t[2]||(t[2]=(...u)=>d.cancel&&d.cancel(...u)),style:{color:"#f9780c",cursor:"pointer"}},[y(" "+k(n.user.username)+"  ",1),c(l,{color:"#F9780C",style:{"vertical-align":"middle"}},{default:I(()=>[c(r)]),_:1})])])):C("",!0)],4))])}const an=E(zs,[["render",Ys],["__scopeId","data-v-b49045a7"]]),Ce="/assets/usbkey.png";const Js={components:{userAgreement:R},props:{participantGroupId:{type:String,default:""},type:{type:String,default:""},userId:{type:String,default:""}},setup(){return ce().commit("updateIsLogin",!1),{}},data(){return{user:{username:"",password:""},loading:!1,address:"",lessee:"",certID:"",polling:null,code:"ukey",ukeyType:"bjca",ukeyStart:!0,submitting:!1}},watch:{},created(){this.polling=setInterval(()=>{this.ukeycard()},2e3)},methods:{ukeycard(){he(this.ukeyType).then(e=>{if(!e.data.length){this.ukeyStart=!1;return}this.ukeyStart=!0,this.stopPolling(),e&&e.code===0&&(this.user.username=e.data[0].name,this.certID=e.data[0].id)}).catch(()=>{this.alert.error("获取证书失败")})},stopPolling(){this.polling&&clearInterval(this.polling)},async submitAuth(){try{this.subbmitting=!0,await this.clickregister()}finally{this.subbmitting=!1}},clickregister(){if(!this.user.username){this.alert.error("请插入证书");return}if(!this.user.password||this.user.password.length<6){this.alert.error("请输入六位密码");return}this.loading=!0;let e={participantTypes:this.type==="login"?"03":"01,03",participantKeyword:this.userId||this.user.username.split("(")[0]};return pe(this.participantGroupId,e.participantTypes,e.participantKeyword,this.code).then(t=>{const a={id:this.certID,password:this.user.password,random_str:t.data.requestId,der_format:"false",ukey_type:this.ukeyType};return me(a).then(p=>{if(p.data.retry_count==0&&!p.data.result){this.loading=!1,this.alert.error("获取证书失败");return}if(p.data.retry_count==0){this.loading=!1,this.alert.error("您的证书密码已被锁死,请联系管理员进行解锁!");return}if(!p.data.result){this.loading=!1,this.alert.error("校验证书密码失败!您还有"+p.data.retry_count+"次机会重试!");return}fe(t.data.requestId,p.data.signature,p.data.certificate).then(()=>{this.$emit("mfaCallbackFunc",{requestId:t.data.requestId,type:this.code})}).catch(n=>{this.loading=!1,this.$emit("childEventCancel",t.data.requestId);let d=JSON.parse(n.message);this.alert.error(d.data.errorMsg)})}).catch(()=>{this.loading=!1})}).catch(t=>{let a=JSON.parse(t.message);this.alert.error(a.data.errorMsg),this.loading=!1,this.$emit("generateRequestIdFailCallbackFunc",t)})},search(){this.clickregister()},cancel(){this.stopPolling()}}},ne=e=>(z("data-v-387dc00a"),e=e(),L(),e),Ws={class:"content","element-loading-text":"登录中...","element-loading-background":"rgba(0, 0, 0, 0.5)"},Xs={key:0},Zs={key:1,style:{margin:"30px 0"}},js={class:"inputContent"},ei={class:"inputContent"},ti={style:{"text-align":"center"}},si=ne(()=>h("img",{src:Ce,width:"265",height:"200",alt:""},null,-1)),ii=ne(()=>h("p",{style:{"font-size":"14px"}},"未找到可用的ukey证书",-1)),ni=ne(()=>h("p",{style:{"font-size":"14px",color:"#fda43d"}},"请插入ukey证书",-1)),ri=[si,ii,ni];function ai(e,t,a,p,n,d){const o=_("el-input"),s=_("el-button"),i=_("userAgreement"),r=_e("loading");return W((m(),f("div",Ws,[a.type?(m(),f("h3",Xs,"Ukey登录")):C("",!0),n.ukeyStart?(m(),f("div",Zs,[h("div",{class:"inputItem",style:V(!a.type&&"width: 60%; margin: 15px auto;")},[h("div",js,[c(o,{ref:"username",modelValue:n.user.username,"onUpdate:modelValue":t[0]||(t[0]=l=>n.user.username=l),readonly:"",placeholder:"请插入证书"},null,8,["modelValue"])])],4),h("div",{class:"inputItem",style:V(!a.type&&"width: 60%; margin: 15px auto;")},[h("div",ei,[c(o,{ref:"password",modelValue:n.user.password,"onUpdate:modelValue":t[1]||(t[1]=l=>n.user.password=l),placeholder:"请输入密码","show-password":"",onKeyup:ge(d.search,["enter"])},null,8,["modelValue","onKeyup"])])],4),h("div",ti,[c(s,{ref:"confirmBtn",type:"primary",class:b({loginBtn:a.type==="login","mfa-confirm-btn":a.type!=="login"}),disabled:a.type==="login"&&!n.user.username||!n.user.password||n.submitting,onClick:d.submitAuth},{default:I(()=>[y(k(a.type==="login"?"登录":"确定"),1)]),_:1},8,["class","disabled","onClick"])]),a.type?(m(),B(i,{key:0})):C("",!0)])):(m(),f("div",{key:2,class:b(a.type=="login"?"ukeystart":"ukeystyle")},ri,2))])),[[r,n.loading,void 0,{fullscreen:!0,lock:!0}]])}const on=E(Js,[["render",ai],["__scopeId","data-v-387dc00a"]]);const oi={components:{userAgreement:R},props:{participantGroupId:{type:String,default:""},type:{type:String,default:""},userId:{type:String,default:""}},setup(){return ce().commit("updateIsLogin",!1),{}},data(){return{user:{username:"",password:""},loading:!1,address:"",lessee:"",certID:"",polling:null,code:"ukey_wq",ukeyStart:!0,submitting:!1}},watch:{},created(){this.polling=setInterval(()=>{this.ukeycard()},2e3)},methods:{ukeycard(){he(this.code).then(e=>{if(!e.data.length){this.ukeyStart=!1;return}this.ukeyStart=!0,this.stopPolling(),e&&e.code===0&&(this.user.username=e.data[0].name,this.certID=e.data[0].id)}).catch(()=>{this.alert.error("获取证书失败")})},stopPolling(){this.polling&&clearInterval(this.polling)},async submitAuth(){try{this.subbmitting=!0,await this.clickregister()}finally{this.subbmitting=!1}},clickregister(){if(!this.user.username){this.alert.error("请插入证书");return}if(!this.user.password||this.user.password.length<6){this.alert.error("请输入六位密码");return}this.loading=!0;let e={participantTypes:this.type==="login"?"03":"01,03",participantKeyword:this.userId||this.user.username.split("(")[0]};return pe(this.participantGroupId,e.participantTypes,e.participantKeyword,this.code).then(t=>{const a={id:this.certID,password:this.user.password,random_str:t.data.requestId,der_format:"false",ukey_type:this.code};return me(a).then(p=>{if(p.data.retry_count==0){this.loading=!1,this.alert.error("您的证书密码已被锁死,请联系管理员进行解锁!");return}if(!p.data.result){this.loading=!1,this.alert.error("校验证书密码失败!您还有"+p.data.retry_count+"次机会重试!");return}fe(t.data.requestId,p.data.signature,p.data.certificate).then(()=>{this.$emit("mfaCallbackFunc",{requestId:t.data.requestId,type:this.code})}).catch(n=>{this.loading=!1,this.$emit("childEventCancel",t.data.requestId);let d=JSON.parse(n.message);this.alert.error(d.data.errorMsg)})}).catch(p=>{console.log("err: ",p),this.loading=!1})}).catch(t=>{let a=JSON.parse(t.message);this.alert.error(a.data.errorMsg),this.loading=!1,this.$emit("generateRequestIdFailCallbackFunc",error)})},search(){this.clickregister()},cancel(){this.stopPolling()}}},re=e=>(z("data-v-a7bea6e4"),e=e(),L(),e),li={class:"content","element-loading-text":"登录中...","element-loading-background":"rgba(0, 0, 0, 0.5)"},ui={key:0},di={key:1,style:{margin:"30px 0"}},ci={class:"inputContent"},hi={class:"inputContent"},pi={style:{"text-align":"center"}},mi=re(()=>h("img",{src:Ce,width:"265",height:"200",alt:""},null,-1)),fi=re(()=>h("p",{style:{"font-size":"14px"}},"未找到可用的ukey证书",-1)),_i=re(()=>h("p",{style:{"font-size":"14px",color:"#fda43d"}},"请插入ukey证书",-1)),gi=[mi,fi,_i];function yi(e,t,a,p,n,d){const o=_("el-input"),s=_("el-button"),i=_("userAgreement"),r=_e("loading");return W((m(),f("div",li,[a.type?(m(),f("h3",ui,"Ukey登录")):C("",!0),n.ukeyStart?(m(),f("div",di,[h("div",{class:"inputItem",style:V(!a.type&&"width: 60%; margin: 15px auto;")},[h("div",ci,[c(o,{ref:"username",modelValue:n.user.username,"onUpdate:modelValue":t[0]||(t[0]=l=>n.user.username=l),readonly:"",placeholder:"请插入证书"},null,8,["modelValue"])])],4),h("div",{class:"inputItem",style:V(!a.type&&"width: 60%; margin: 15px auto;")},[h("div",hi,[c(o,{ref:"password",modelValue:n.user.password,"onUpdate:modelValue":t[1]||(t[1]=l=>n.user.password=l),placeholder:"请输入密码","show-password":"",onKeyup:ge(d.search,["enter"])},null,8,["modelValue","onKeyup"])])],4),h("div",pi,[c(s,{ref:"confirmBtn",type:"primary",class:b({loginBtn:a.type==="login","mfa-confirm-btn":a.type!=="login"}),disabled:a.type==="login"&&!n.user.username||!n.user.password||n.submitting,onClick:d.submitAuth},{default:I(()=>[y(k(a.type==="login"?"登录":"确定"),1)]),_:1},8,["class","disabled","onClick"])]),a.type?(m(),B(i,{key:0})):C("",!0)])):(m(),f("div",{key:2,class:b(a.type=="login"?"ukeystart":"ukeystyle")},gi,2))])),[[r,n.loading,void 0,{fullscreen:!0,lock:!0}]])}const ln=E(oi,[["render",yi],["__scopeId","data-v-a7bea6e4"]]),Ii="/assets/images/mfa/face-auth.png";const vi={name:"FACE",components:{userAgreement:R},props:{participantGroupId:{type:String,default:""},userId:{type:String,default:""},type:{type:String,default:""},name:{type:String,default:""}},data(){return{polling:void 0,requestId:"",src:Ii,code:"face",user:{username:this.name},firstStep:!0}},created(){this.type!=="login"&&this.generateRequestId()},methods:{generateRequestId(){let e={participantTypes:this.type==="login"?"03":"01,03",participantKeyword:this.userId||this.user.username};F(this.participantGroupId,e.participantTypes,e.participantKeyword,this.code).then(t=>{this.requestId=t.data,this.$emit("requestId",t.data),this.startPolling(),this.firstStep=!1}).catch(t=>{this.$emit("generateRequestIdFailCallbackFunc",t)})},startPolling(){this.polling=setInterval(()=>{if(!this.requestId){this.stopPolling();return}G(this.requestId).then(e=>{if(e.data!=="DOING"){switch(e.data){case"FAILED":this.alert.error(e.data.errorMsg||"系统错误");break;case"SUCCESS":this.Complete();break}this.requestId="",this.stopPolling()}}).catch(()=>{this.requestId="",this.cancel(),this.$emit("cancelMfaCallbackFunc"),this.$emit("childEventCancel",this.requestId)})},1e3)},stopPolling(){this.polling&&clearInterval(this.polling)},Complete(){this.stopPolling(),this.$emit("mfaCallbackFunc",{requestId:this.requestId,type:this.code})},cancel(){if(this.stopPolling(),this.requestId){let e=this.requestId;this.requestId="",P(e)}this.firstStep=!0}}},Ci=e=>(z("data-v-964cd698"),e=e(),L(),e),bi={class:"mfa-content"},qi={key:0},ki={key:1},Si={class:"inputItem"},Ai={class:"inputContent"},Ei={key:2},wi=["src"],Pi=Ci(()=>h("div",{class:"auth-tip"},[y(" 请打开 "),h("span",{class:"mfa-text"},"【安全令APP】"),y("验证面容ID ")],-1)),Ri={key:0,style:{"font-size":"12px"}};function Mi(e,t,a,p,n,d){const o=_("el-input"),s=_("el-button"),i=_("userAgreement"),r=_("EditPen"),l=_("el-icon");return m(),f("div",bi,[a.type?(m(),f("h3",qi,"面容ID登录")):C("",!0),n.firstStep&&a.type==="login"?(m(),f("div",ki,[h("div",Si,[h("div",Ai,[c(o,{ref:"username",modelValue:n.user.username,"onUpdate:modelValue":t[0]||(t[0]=u=>n.user.username=u),placeholder:"请输入用户名"},null,8,["modelValue"])])]),c(s,{ref:"nextStepBtn",class:"Btn",disabled:!n.user.username,onClick:d.generateRequestId},{default:I(()=>[y(" 下一步 ")]),_:1},8,["disabled","onClick"]),c(i)])):(m(),f("div",Ei,[h("img",{src:n.src,class:b(a.type?"mfa-content-img":"mfa-content-imgmode"),alt:""},null,10,wi),Pi,a.type?(m(),f("p",Ri,[y(" 用户: "),h("span",{onClick:t[1]||(t[1]=(...u)=>d.cancel&&d.cancel(...u)),style:{color:"#f9780c",cursor:"pointer"}},[y(" "+k(n.user.username)+"  ",1),c(l,{color:"#F9780C",style:{"vertical-align":"middle"}},{default:I(()=>[c(r)]),_:1})])])):C("",!0)]))])}const un=E(vi,[["render",Mi],["__scopeId","data-v-964cd698"]]),Fi="/assets/images/mfa/facial.png";const Ti={name:"facial_recognition",components:{userAgreement:R},props:{participantGroupId:{type:String,default:""},userId:{type:String,default:""},type:{type:String,default:""},name:{type:String,default:""},tenant:{type:Object,default:()=>{}}},data(){return{loading:!1,polling:void 0,requestId:"",src:Fi,code:"facial_recognition",user:{username:this.name},firstStep:!0}},created(){this.type!=="login"&&this.generateRequestId()},methods:{generateRequestId(){this.loading=!0,j("plugin:bio|is_support_facial_recognition").then(e=>{if(!e){this.loading=!1,this.alert.error("检测到设备不支持人脸识别，请换其他方式认证");return}let t={participantTypes:this.type==="login"?"03":"01,03",participantKeyword:this.userId||this.user.username};F(this.participantGroupId,t.participantTypes,t.participantKeyword,this.code).then(a=>{this.requestId=a.data,this.$emit("requestId",a.data),this.doFacialRecognition(),this.firstStep=!1}).catch(a=>{this.$emit("generateRequestIdFailCallbackFunc",a)})})},doFacialRecognition(){j("plugin:bio|facial_recognition",{username:this.user.username}).then(async e=>{e&&(await ue(this.requestId,null),this.Complete())}).catch(async e=>{await ue(this.requestId,e),this.alert.error(e),this.cancel(),this.$emit("cancelMfaCallbackFunc")})},Complete(){this.$emit("mfaCallbackFunc",{requestId:this.requestId,type:this.code})},cancel(){if(j("plugin:bio|cancel_facial_recognition"),this.requestId){let e=this.requestId;this.requestId="",P(e)}this.firstStep=!0}}},Ni={class:"mfa-content"},Vi={key:0},Ui={key:1},Bi={class:"inputItem"},xi={class:"inputContent"},Oi={key:2},zi=["src"],Li={key:0,style:{"font-size":"12px"}};function Di(e,t,a,p,n,d){const o=_("el-input"),s=_("el-button"),i=_("userAgreement"),r=_("EditPen"),l=_("el-icon");return m(),f("div",Ni,[a.type?(m(),f("h3",Vi,"Windows人脸识别")):C("",!0),n.firstStep&&a.type==="login"?(m(),f("div",Ui,[h("div",Bi,[h("div",xi,[c(o,{ref:"username",modelValue:n.user.username,"onUpdate:modelValue":t[0]||(t[0]=u=>n.user.username=u),placeholder:"请输入用户名"},null,8,["modelValue"])])]),c(s,{ref:"nextStepBtn",class:"Btn",loading:n.loading,disabled:!n.user.username,onClick:d.generateRequestId},{default:I(()=>[y(" 下一步 ")]),_:1},8,["loading","disabled","onClick"]),c(i)])):(m(),f("div",Oi,[h("img",{src:n.src,class:b(a.type?"mfa-content-img":"mfa-content-imgmode"),alt:""},null,10,zi),a.type?(m(),f("p",Li,[y(" 用户: "),h("span",{onClick:t[1]||(t[1]=(...u)=>d.cancel&&d.cancel(...u)),style:{color:"#f9780c",cursor:"pointer"}},[y(" "+k(n.user.username)+"  ",1),c(l,{color:"#F9780C",style:{"vertical-align":"middle"}},{default:I(()=>[c(r)]),_:1})])])):C("",!0)]))])}const dn=E(Ti,[["render",Di],["__scopeId","data-v-e10341d3"]]);const Ki={name:"WECHATOTP",props:{participantGroupId:{type:String,default:""},userId:{type:String,default:""}},data(){return{requestId:"",authCode:"",code:"wechat_otp",reg:/^\d+$/,submitting:!1}},created(){this.getRequestId()},methods:{getRequestId(){F(this.participantGroupId,"01",this.userId,this.code).then(e=>{this.requestId=e.data,this.$emit("requestId",this.requestId)})},async submitAuth(){try{this.subbmitting=!0,await this.Complete()}finally{this.subbmitting=!1}},Complete(){if(!this.authCode){this.alert.error("请输入微信动态口令");return}if(this.authCode&&!this.reg.test(this.authCode)){this.alert.error("微信动态口令格式错误");return}return $({requestId:this.requestId,authCode:this.authCode,method:this.code}).then(()=>{this.$emit("mfaCallbackFunc","wechat_code 认证完成啦")})},cancel(){if(this.requestId){let e=this.requestId;this.requestId="",P(e)}}}},Gi={class:"mfa-content",style:{"margin-top":"61px","padding-bottom":"61px"}},Qi={class:"auth-code-content border"};function Hi(e,t,a,p,n,d){const o=_("el-input"),s=_("el-button");return m(),f("div",Gi,[h("div",Qi,[c(o,{modelValue:n.authCode,"onUpdate:modelValue":t[0]||(t[0]=i=>n.authCode=i),class:"auth-input",placeholder:"请输入微信动态口令"},null,8,["modelValue"])]),c(s,{ref:"confirmBtn",type:"primary",class:"mfa-confirm-btn",disabled:!n.authCode||n.submitting,onClick:d.submitAuth},{default:I(()=>[y("确定")]),_:1},8,["disabled","onClick"])])}const cn=E(Ki,[["render",Hi],["__scopeId","data-v-1a8ddcd5"]]);export{en as A,Xi as E,un as F,tn as M,ji as N,Yi as Q,Wi as S,sn as T,on as U,Zi as V,cn as W,Ce as _,Ji as a,nn as b,rn as c,an as d,ln as e,dn as f,R as u};

use log::error;
use std::{error::Error, path::PathBuf, sync::mpsc};
use tokio::sync::RwLock;

use tauri::{App, Manager, Wry};

use crate::{
    comm, core::CoreManager, log_err, logging, notify, resource, shutdown, state::AppState, web,
};

pub(super) fn resolve_setup(
    app: &mut App<Wry>,
    resource_dir: PathBuf,
) -> Result<(), Box<dyn Error>> {
    #[cfg(target_os = "macos")]
    app.set_activation_policy(tauri::ActivationPolicy::Regular);

    let app_handle = app.handle();

    let handle = app_handle.clone();
    let exit = move || {
        let handle = handle.clone();
        let rx = super::exit(handle);
        log::debug!(target: "app", "Force exit");
        rx.recv().unwrap();
    };
    shutdown::set_shutdown_signal_handler(exit)?;

    // 初始化程序状态
    app_handle.manage(RwLock::const_new(AppState::default()));
    logging::front_log(&app_handle);

    // 检查服务是否正在运行中
    #[cfg(not(debug_assertions))]
    crate::daemon_check::is_running_or_start(&app_handle);

    if let Err(error) = resource::init(&app_handle, resource_dir) {
        error!(target: "app", "{error}");
        notify!(app_handle, "程序配置错误");
        std::process::exit(1);
    }

    // 监听电源事件
    #[cfg(windows)]
    {
        use crate::system;

        system::power::spawn(app_handle.clone());
    }

    let (tx, rx) = mpsc::channel();
    let app_name = app_handle.package_info().name.clone();
    log_err!(CoreManager::global().init(app_handle.clone(), app_name, tx.clone()));
    rx.recv().unwrap();

    tauri::async_runtime::spawn({
        let app_handle = app_handle.clone();
        async move {
            _ = comm::spawn(app_handle).await;
            tx.send(()).unwrap();
        }
    });
    rx.recv().unwrap();

    web::start(app_handle);

    Ok(())
}

pub fn reset() {
    log_err!(CoreManager::global().stop_core());
}

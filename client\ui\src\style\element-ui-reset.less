body {
  padding: 0 !important;
  margin: 0;
  font-family: "微软雅黑", "Helvetica Neue", Helvetica, "PingFang SC",
    "Hiragino Sans GB", "Microsoft YaHei", Arial, sans-serif;
}

@mainColor: #f9780c;

.el-button--primary {
  background-color: @mainColor;
  border-color: @mainColor;
}

.el-button--primary:hover,
.el-button--primary:active,
.el-button--primary:focus {
  background-color: @mainColor;
  border-color: @mainColor;
}

.el-button--primary[disabled],
.el-button--primary[disabled]:hover {
  color: fade(white, 80%) !important;
  background: tint(@mainColor, 70%) !important;
  cursor: not-allowed;
}

.el-button:focus-visible {
  outline: none;
}

.el-dialog *:not(.mfa-confirm-btn).el-button:hover,
//.el-dialog :is(.el-button--primary) .el-button:hover:not(.mfa-confirm-btn),
.el-message-box .el-button:hover {
  color: @mainColor;
  border-color: @mainColor;
  background-color: #fff;
  outline: 0;
}

.el-message-box .el-button {
  line-height: 0;
}

.el-dialog *:not(.Btn).el-button--primary[disabled],
.el-dialog *:not(.Btn).el-button--primary[disabled]:hover {
  border-radius: 30px;
  // height: 42px;
  background: tint(@mainColor, 70%) !important;
  border-color: tint(@mainColor, 70%);
  color: fade(white, 80%) !important;
  cursor: not-allowed;
}

/* .el-message-box {
    width: 80%;
} */
.el-message-box__message p {
  text-align: center;
}

.el-message {
  min-width: 40%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.el-loading-spinner .el-loading-text {
  color: #fff;
}

.el-loading-spinner .path {
  stroke: #fff;
}

.el-checkbox__inner:hover {
  border-color: @mainColor;
}

.el-message-box__headerbtn:focus .el-message-box__close,
.el-message-box__headerbtn:hover .el-message-box__close {
  color: @mainColor;
}

.el-checkbox input:focus-visible + .el-checkbox__inner {
  outline: 2px solid @mainColor;
  outline-offset: 1px;
}

.el-dialog__header {
  margin-right: 10px;
  margin-left: 10px;
  /* border-bottom: 1px solid #ddd; */
}

.el-dialog__title {
  /* color: rgb(153 153 153); */
  font-size: 20px;
  font-weight: 400;
  color: #25282f;
}

.el-dialog__headerbtn .el-dialog__close {
  color: rgb(153 153 153);
}

.el-dialog__headerbtn {
  position: absolute;
  top: 12px;
  right: 6px;
  width: 30px;
  height: 30px;
}

.el-dialog--center .el-dialog__body {
  padding: 0 10px;
}

.logout .el-button.is-round {
  padding: 0;
  width: 112px;
  height: 40px;
}

/* 全局设置 element ui dialog水平垂直居中 */
.el-dialog {
  border-radius: 20px;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  margin: 0px !important;
}

.el-input-group__append {
  border: 1px solid @mainColor;
  box-sizing: border-box;
  border-left: 0;
  background: none;
  box-shadow: none;
}

.el-input-group__append button.el-button {
  font-size: 12px;
  /* border: 1px solid @mainColor;
    border-color: @mainColor; */
  border-width: 0 0 0 1px;
  border-radius: 0;
  color: @mainColor;
}

.el-input-group__append button.el-button:hover {
  color: @mainColor;
}

.app-content {
  position: relative;
}

.app-title {
  position: fixed;
  top: 7px;
  left: 0;
  width: 100%;
  height: 30px;
  line-height: 30px;
  color: #666;
  // #if [platform=macos]
  text-align: right;
  // #endif
}

.app-title .title-icon {
  width: 30px;
  height: 30px;
  cursor: pointer;
  // #if [platform=macos]
  margin-right: 7px;
  // #else
  margin-left: 7px;
  // #endif
}

.app-title .app-title-left {
  position: absolute;
  left: 0;
  top: 0;
}

.app-title .app-title-right {
  position: absolute;
  top: 0;
  right: 5px;
}

.app-title .title-icon {
  color: #000;
}

.mfa-model .mfa-model-item {
  /* border-bottom: 1px solid #eee; */
  box-sizing: border-box;
  vertical-align: middle;
  height: 40px;
  line-height: 40px;
  padding: 7px 15px;
  cursor: pointer;
  text-align: left;
}

.mfa-model-item:hover {
  color: #ee761b;
}

.mfa-model .mfa-model-item:last-child {
  border: 0;
}

.mfa-model .mfa-model-text {
  /* 文字超出宽度则显示ellipsis省略号 */
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  color: #ee761b;
  /* border-bottom: 1px solid #ddd; */
  font-size: 14px;
  padding: 10px 12px;
  letter-spacing: 1px;
  line-height: 20px;
}

.mfa-model-item .mfa-auth-img {
  width: 23px;
  height: auto;
  display: inline-block;
}

.mfa-model-item .mfa-auth-name {
  /* 文字超出宽度则显示ellipsis省略号 */
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  display: inline-block;
  width: calc(100% - 30px);
  line-height: 26px;
  vertical-align: top;
  text-align: left;
  text-indent: 20px;
  font-size: 15px;
}

.mfa-content {
  width: 100%;
  text-align: center;
}

.mfa-content .mfa-content-img {
  width: 70%;
  height: auto;
  // margin: 0 auto 25px;
}

.mfa-content .mfa-content-imgmode {
  width: 60%;
  height: auto;
  margin: 0 auto 25px;
}

.mfa-content .mfa-text {
  line-height: 20px;
  color: @mainColor;
  font-size: 14px;
  text-align: center;
  text-indent: 10px;
  margin-bottom: 15px;
  margin-top: 15px;
}

.mfa-content .mfa-text.text-c {
  text-align: center;
}

.mfa-content .auth-tip {
  padding-bottom: 15px;
  padding-top: 15px;
}

.mfa-content .el-input__wrapper {
  border: 1px solid @mainColor;
  border-right: 0;
  box-shadow: none;
}

.mfa-content .border .el-input__wrapper {
  border: 1px solid @mainColor;
  border-right: 1px solid @mainColor;
  box-shadow: none;
}

.el-input__wrapper.is-focus {
  box-shadow: 0 0 0 1px @mainColor inset;
}

.mfa-content .el-input__wrapper.is-focus {
  box-shadow: none;
}

/* .el-input__wrapper{
    background-color: #F4F5F7;
} */
.mfa-content .mfa-content-footer {
  border-top: 1px solid #ddd;
  font-size: 16px;
  cursor: pointer;
  color: @mainColor;
  text-align: center;
  padding-top: 12px;
  letter-spacing: 2px;
  padding-bottom: 12px;
}

.NoActionMsg {
  color: #53565d;
  font-size: 13px;
  line-height: 26px;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.NoActionMsg img {
  position: fixed;
  top: 13%;
  left: 50%;
  transform: translate(-50%, 0%);
}

.NoActionMsg .titleupdate {
  width: 80%;
  text-align: left;
  height: 90px;
  padding-top: 10px;
  box-sizing: border-box;
}

.loginBtn {
  background-image: linear-gradient(to right, @mainColor, #fbaa66);
  position: relative;
  height: 45px;
  line-height: 45px;
  font-size: 1rem;
  overflow: hidden;
  letter-spacing: 0.3rem;
  text-align: center;
  border-radius: 25px;
  margin: 15px 0 10px;
  width: 100%;
  color: #fff;
  border: 0;
  outline: none;
  cursor: pointer;
}

.mfa-confirm-btn {
  background-image: linear-gradient(to right, @mainColor, #fbaa66);
  position: relative;
  height: 45px;
  line-height: 45px;
  font-size: 1rem;
  overflow: hidden;
  letter-spacing: 0.3rem;
  /* text-align: center; */
  border-radius: 25px;
  /* margin: 15px 0 10px; */
  margin: 0 auto;
  width: 60%;
  color: #fff;
  border: 0;
  outline: none;
  cursor: pointer;
}

.loginBtn_title {
  font-size: 12px;
  padding-bottom: 30px;
}

.str {
  font-weight: normal;
  color: @mainColor;
  cursor: pointer;
}

.el-message-box__content {
  padding: 50px 0 !important;
  /* font-size: 16px !important; */
}

.el-message-box__title {
  font-weight: 500;
  font-size: 20px;
}

.el-message-box {
  width: 334px;
  border-radius: 20px;
  padding-bottom: 25px;
}

.el-message-box__btns button:nth-child(2) {
  margin-left: 20px;
}

.el-tabs__item.is-disabled {
  color: #53565d;
  cursor: pointer;
}

.updateBox {
  background: linear-gradient(181deg, #ffdbbc 0%, #ffffff 50%);
}

.updateBox .el-message-box__content {
  padding: 40px 0 20px !important;
}

.el-button.is-round {
  padding: 18px 30px;
}

.auth-input {
  padding-left: 20px;
  width: 60%;
  margin-bottom: 30px;
  height: 40px;
  border-radius: 20px;
  border: 1px solid @mainColor;
}

.el-tabs__item:hover {
  color: #f87f1a;
}

.el-tabs__item.is-active {
  color: #f87f1a;
}

.el-tabs__active-bar {
  background-color: #f87f1a;
}

/* //鼠标悬停样式 */
.el-dropdown-menu__item:not(.is-disabled):hover {
  background-image: linear-gradient(to right, #fe9e50, #fe9e50);
  color: #fff;
}

.el-dialog__headerbtn:focus .el-dialog__close,
.el-dialog__headerbtn:hover .el-dialog__close {
  color: #ee761b;
}

.el-tabs--left .el-tabs__item.is-left,
.el-tabs--right .el-tabs__item.is-left {
  justify-content: start;
  margin-bottom: 12px !important;
}

.el-menu-item:hover {
  background-color: #f4f5f7;
}

.selected {
  color: #f87f1a;
}

.el-dropdown-menu {
  min-height: 80px;
  max-height: 280px;
  overflow-y: auto;
}

.puresdp {
  margin-bottom: 45px;
  font-style: 16px;
  font-weight: bold;
  color: #25282f;
}

.AboutUs .el-dialog__body {
  padding: 20px 20px 30px 32px;
}

.title-icon:focus-visible {
  outline: none;
}

.userhead {
  border-radius: 50%;
}

img {
  -webkit-user-drag: none;
}
.container {
  width: 100%;
  /* margin-bottom: 18px; */
  height: 40px;
  padding-top: 20px;
  box-sizing: border-box;
}

.container-2 {
  width: 80%;
  height: 10px;
  border-radius: 20px;
  background: linear-gradient(orange 0 0) 0/0% no-repeat lightblue;
  animation: p2 2s infinite steps(10);
  margin: 0 auto !important;
  display: none;
}

@keyframes p2 {
  100% {
    background-size: 110%;
  }
}

.updaterBox {
  width: 334px;
  background: linear-gradient(181deg, #ffdbbc 0%, #ffffff 50%);
}
.dropdownStyle:hover{
  color: #606266 !important;
  background-image:none !important;
}

.dropdownStyle:focus{
  background-color: #fff !important;
  color: #606266 !important;
}

.selected:hover{
  color: #f87f1a !important;
}


.f_w_no {
  font-weight: normal !important;
}
use std::{
    collections::HashMap,
    net::{Ip<PERSON>dd<PERSON>, SocketAddr},
    sync::Arc,
    time::Duration,
};

use std::collections::HashSet;

use base::{new_tls_acceptor, spa::SPAType};
use moka::future::Cache;
use socket2::{SockRef, TcpKeepalive};
use tokio::net::{TcpListener, TcpStream};
use tokio_rustls::TlsAcceptor;
use tokio_util::sync::CancellationToken;
use tracing::{debug, error, info, Instrument};

use crate::{
    backend::client::BackendCommonClient,
    cache::CacheManager,
    config::{Keepalive, ServerConfig},
    message::MqHandle,
    xdp::{XdpCommand, XdpCommandSender},
    EventSender,
};

use super::server::MixServer;

use crate::packet::client::ClientPacketHandle;

pub struct CommArgs {
    pub authorization_interval: u64,
    pub server_config: ServerConfig,
    pub keepalive: Option<Keepalive>,
    pub dns_servers: Option<Vec<IpAddr>>,

    pub event_sender: EventSender,
    pub primary_xdp_sender: XdpCommandSender,
    pub cache_manager: CacheManager,
    pub backend_client: Arc<BackendCommonClient>,
    pub client_packet_handle: ClientPacketHandle,
    pub mq_handle: MqHandle,
    pub resource_whitelist: Option<Arc<HashSet<SocketAddr>>>,

    pub spa_cache: Arc<Cache<IpAddr, HashMap<String, SPAType>>>,
    pub shutdown_token: CancellationToken,
}

pub async fn spawn(args: CommArgs) {
    let tls_acceptor = new_tls_acceptor(
        &args.server_config.ca,
        &args.server_config.cert,
        &args.server_config.key,
        #[cfg(feature = "tlcp")]
        &args.server_config.enc_cert,
        #[cfg(feature = "tlcp")]
        &args.server_config.enc_key,
    );

    let addr = format!("{}:{}", args.server_config.host, args.server_config.port);
    match TcpListener::bind(&addr).await {
        Ok(listener) => {
            info!("listening on tcp://{}", addr);

            tokio::spawn(
                async move {
                    loop {
                        tokio::select! {
                            _ = args.shutdown_token.cancelled() => {
                                break;
                            }
                            result = listener.accept() => match result {
                                Ok((stream, _)) => {
                                    // https://github.com/nervosnetwork/tentacle/pull/333
                                    let peer_addr = match stream.peer_addr() {
                                        Ok(peer_addr) => peer_addr,
                                        Err(err) => {
                                            error!("stream get peer address error: {err}");
                                            continue;
                                        }
                                    };

                                    debug!(%peer_addr, "accept a TCP connection");
                                    let local_addr = match stream.local_addr() {
                                        Ok(local_addr) => local_addr,
                                        Err(err) => {
                                            error!(%peer_addr, "stream get local address error: {err}");
                                            continue;
                                        }
                                    };
                                    tokio::spawn(
                                        process_socket(
                                            args.authorization_interval,
                                            args.dns_servers.clone(),
                                            peer_addr,
                                            local_addr,
                                            stream,
                                            tls_acceptor.clone(),
                                            args.keepalive.clone(),
                                            args.event_sender.clone(),
                                            args.primary_xdp_sender.clone(),
                                            args.cache_manager.clone(),
                                            args.client_packet_handle.clone(),
                                            args.mq_handle.clone(),
                                            args.backend_client.clone(),
                                            args.resource_whitelist.clone(),
                                            args.spa_cache.clone(),
                                            args.shutdown_token.child_token(),
                                        )
                                        .in_current_span(),
                                    );
                                }
                                Err(err) => {
                                    error!("tcp accept error: {err}");
                                    continue;
                                }
                            }
                        }
                    }
                }
                .instrument(tracing::error_span!(parent: None, "Acceptor")),
            );
        }
        Err(err) => {
            error!("error binding channel listener: {err}");

            // Exit
            std::process::exit(1);
        }
    }
}

fn configure_stream(stream: &TcpStream, keepalive: Option<Keepalive>) {
    let socket = SockRef::from(stream);
    if let Some(keepalive) = keepalive {
        let keepalive = TcpKeepalive::new()
            .with_time(Duration::from_secs(keepalive.idle as u64))
            .with_interval(Duration::from_secs(keepalive.interval as u64))
            .with_retries(keepalive.retries);

        socket.set_tcp_keepalive(&keepalive).unwrap();
    }

    if let Err(e) = socket.set_nodelay(true) {
        error!("disabling Nagle fails: {e}");
    }

    // l_onoff=1，l_linger=0 直接关闭socket,丢弃发送和接收缓冲区数据
    if let Err(e) = socket.set_linger(Some(Duration::from_secs(0))) {
        error!("setting SO_LINGER option fails: {e}");
    }
}

async fn process_socket(
    authorization_interval: u64,
    dns_servers: Option<Vec<IpAddr>>,
    peer_addr: SocketAddr,
    local_addr: SocketAddr,
    stream: TcpStream,
    tls_acceptor: TlsAcceptor,
    keepalive: Option<Keepalive>,
    event_sender: EventSender,
    primary_xdp_sender: XdpCommandSender,
    cache_manager: CacheManager,
    client_packet_handle: ClientPacketHandle,
    mq_handle: MqHandle,
    backend_client: Arc<BackendCommonClient>,
    resource_whitelist: Option<Arc<HashSet<SocketAddr>>>,
    spa_cache: Arc<Cache<IpAddr, HashMap<String, SPAType>>>,
    shutdown_token: CancellationToken,
) {
    configure_stream(&stream, keepalive);

    match tls_acceptor.accept(stream).await {
        Ok(stream) => {
            #[cfg(feature = "tlcp")]
            debug!(%peer_addr, "accept a TLCP connection");
            #[cfg(not(feature = "tlcp"))]
            debug!(%peer_addr, "accept a TLS connection");

            let serve_future = async move {
                let mix_server = MixServer {
                    authorization_interval,
                    dns_servers,
                    peer_addr,
                    local_addr,
                    stream: Some(stream),
                    event_sender,
                    cache_manager,
                    backend_client,
                    client_packet_handle,
                    mq_handle,
                    resource_whitelist,
                    spa_cache,
                    shutdown_token,
                };

                if mix_server.start().await.is_err() {
                    // primary_xdp_sender
                    //     .send_async(XdpCommand::AllowConnect(false, peer_addr.ip()))
                    //     .await;
                    primary_xdp_sender
                        .send(XdpCommand::DelSPARecord(peer_addr.ip(), peer_addr.port()))
                        .await;
                }
            };

            tokio::spawn(serve_future);
        }
        Err(err) => {
            error!(%peer_addr, "connection establishment failed. {err}");
        }
    }
}

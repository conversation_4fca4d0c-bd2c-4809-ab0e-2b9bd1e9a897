use tauri::App<PERSON>andle;
#[cfg(windows)]
use windows_service::{
    service::{ServiceAccess, ServiceState},
    service_manager::{ServiceManager, ServiceManagerAccess},
    Error,
};

/// Checks if the daemon is running and starts it if not.
pub fn is_running_or_start(_app_handle: &AppHandle) {
    #[cfg(windows)]
    {
        let manager_access = ServiceManagerAccess::CONNECT;
        let service_manager = match ServiceManager::local_computer(None::<&str>, manager_access) {
            Ok(sm) => sm,
            Err(err) => {
                log::error!(target: "app", "Failed to open scm: {}", err);
                return;
            }
        };
        let service = match service_manager
            .open_service("oneid", ServiceAccess::QUERY_STATUS | ServiceAccess::START)
        {
            Ok(service) => service,
            Err(err) => {
                if let Error::Winapi(err) = err {
                    if err.kind() == std::io::ErrorKind::NotFound {
                        log::error!(target: "app", "Service not installed");
                        crate::notify!(_app_handle, "服务未安装，请重新安装客户端。");
                    }
                    log::error!(target: "app", "Failed to open scm: {}", err);
                } else {
                    log::error!(target: "app", "Failed to open scm: {}", err);
                }
                return;
            }
        };

        let status = service.query_status().unwrap();
        match status.current_state {
            ServiceState::Stopped | ServiceState::Paused => match service.start(&[] as &[&str]) {
                Ok(_) => {
                    log::info!(target: "app", "Service started.");
                }
                Err(err) => {
                    log::error!(target: "app", "Service startup failed: {}", err);
                }
            },
            state @ _ => {
                log::info!(target: "app", "Service current status: {:?}", state);
            }
        }
    }
}

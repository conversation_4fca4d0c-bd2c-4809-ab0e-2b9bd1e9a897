import{bE as v,c2 as C,bJ as S,q as m,bS as V,bT as x,bU as f,aR as n,aS as k,bz as I,aE as w,u as g,I as r,bx as l,v as i,F as D,aP as M,b3 as R,H as h,aI as q,aF as E}from"./index-8ff3976b.js";const L={name:"ChangePwd",components:{headerBar:C},props:["username"],setup(){const e=S();return{loading:m(()=>e.getters.changePwdLoading),ctl:m(()=>e.getters.ctl),tenant:m(()=>e.getters.tenant)}},data(){var e=(s,d,a)=>{d===""?a(new Error("请再次输入新密码")):d!==this.ruleForm.newPassword?a(new Error("两次输入密码不一致!")):a()};return{ruleForm:{oldPassword:"",newPassword:"",newPasswordRepeat:""},verifyCode:"",tip:"",errorMsg:"",newerrorMsg:"",showChangePwdSeccess:!1,rules:{oldPassword:[{required:!0,message:"请输入旧密码",trigger:"blur"}],newPassword:[{required:!0,message:"请输入新密码",trigger:"blur"}],newPasswordRepeat:[{required:!0,validator:e,trigger:"blur"}]},changePwdListener:null}},created(){this.pwdPolicy()},methods:{submitForm(){this.errorMsg="",this.newerrorMsg="",this.$refs.ruleForm.validate(e=>{if(e){this.$store.commit("updateChangePwdLoading",!0);const s={username:this.username,old:this.ruleForm.oldPassword,password:this.ruleForm.newPassword};V(s).then(d=>{this.showChangePwdSeccess=!0}).finally(()=>{this.$store.commit("updateChangePwdLoading",!1)})}else return!1})},pwdPolicy(){x({userKeyword:this.username,userTypes:["03"]}).then(e=>{this.tip=e.data.tipKey.split(`
`)})},closeChangePwdSuccessDialog(){f("plugin:sdp|logout");let e={};e[CONFIG.selected_tenant.code]={password:null,remember_me:!1,auto_connect:!1},f("patch",{value:{users:e}}).then(s=>{window.CONFIG=s}).finally(()=>{this.$router.push({path:"/login",query:{date:new Date().getTime()}})})}}},P=e=>(q("data-v-2730976d"),e=e(),E(),e),N={class:"app-content","element-loading-text":"连接中...","element-loading-background":"rgba(0, 0, 0, 0.5)"},U={class:"tipStarthome"},B=P(()=>i("strong",{style:{"font-size":"15px"},class:"f_w_no"},"密码需要满足以下条件：",-1)),T=P(()=>i("p",{style:{padding:"30px 0","text-align":"center"}},"密码重置成功",-1)),z={class:"dialog-footer"};function G(e,s,d,a,o,c){const p=n("el-input"),u=n("el-form-item"),_=n("el-button"),b=n("el-form"),y=n("el-dialog"),F=k("loading");return I((w(),g("div",N,[r(b,{ref:"ruleForm",model:o.ruleForm,rules:o.rules,"label-width":"110px",class:"changePwd-Form"},{default:l(()=>[r(u,{class:"form-item",label:"旧密码 :",prop:"oldPassword",error:o.errorMsg},{default:l(()=>[r(p,{modelValue:o.ruleForm.oldPassword,"onUpdate:modelValue":s[0]||(s[0]=t=>o.ruleForm.oldPassword=t),class:"form-input",type:"password",placeholder:"请输入旧密码",autocomplete:"off",clearable:"","show-password":""},null,8,["modelValue"])]),_:1},8,["error"]),r(u,{class:"form-item",label:"设置新密码 :",prop:"newPassword",error:o.newerrorMsg},{default:l(()=>[r(p,{modelValue:o.ruleForm.newPassword,"onUpdate:modelValue":s[1]||(s[1]=t=>o.ruleForm.newPassword=t),class:"form-input",placeholder:"请输入新密码",type:"password",autocomplete:"off",clearable:"","show-password":""},null,8,["modelValue"])]),_:1},8,["error"]),r(u,{class:"form-item",label:"新密码确认 :",prop:"newPasswordRepeat"},{default:l(()=>[r(p,{modelValue:o.ruleForm.newPasswordRepeat,"onUpdate:modelValue":s[2]||(s[2]=t=>o.ruleForm.newPasswordRepeat=t),class:"form-input",type:"password",placeholder:"请再次输入新密码",autocomplete:"off",clearable:"","show-password":""},null,8,["modelValue"])]),_:1}),i("div",U,[B,(w(!0),g(D,null,M(o.tip,t=>(w(),g("p",{key:t},R(t),1))),128))]),r(u,null,{default:l(()=>[r(_,{type:"primary",class:"form-btn",onClick:c.submitForm,round:""},{default:l(()=>[h(" 确定 ")]),_:1},8,["onClick"])]),_:1})]),_:1},8,["model","rules"]),r(y,{modelValue:o.showChangePwdSeccess,"onUpdate:modelValue":s[3]||(s[3]=t=>o.showChangePwdSeccess=t),title:"修改密码",width:"30%",center:"","close-on-click-modal":!1,"close-on-press-escape":!1,"before-close":c.closeChangePwdSuccessDialog},{footer:l(()=>[i("span",z,[r(_,{round:"",type:"primary",onClick:c.closeChangePwdSuccessDialog},{default:l(()=>[h(" 确定 ")]),_:1},8,["onClick"])])]),default:l(()=>[T]),_:1},8,["modelValue","before-close"])])),[[F,a.loading,void 0,{fullscreen:!0,lock:!0}]])}const O=v(L,[["render",G],["__scopeId","data-v-2730976d"]]);export{O as default};

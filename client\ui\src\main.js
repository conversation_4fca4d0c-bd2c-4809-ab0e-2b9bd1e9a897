import { createApp } from "vue";
// import ElementPlus from "element-plus";
import "element-plus/dist/index.css";
import * as ElementPlusIconsVue from "@element-plus/icons-vue";
import store from "./store";
import alert from "./tools/alert";
import app1 from "./App";
import router from "./router";
import "./style/element-ui-reset.less";
import Toaster from "@meforma/vue-toaster";
import "font-awesome/css/font-awesome.min.css";
import InfiniteScroll from "element-plus";
import { checkAppUpdate } from "./tools/update";
import { stream } from "./api/service";
import { openUserAgreement } from "@/tools/user_agreement";
// 引入中文包
import zhCn from 'element-plus/es/locale/lang/zh-cn';

const app = createApp(app1);

app.provide("hasNewVersion", true);
app.provide("checkUpdate", checkAppUpdate);
app.provide("openUserAgreement", openUserAgreement);

app.directive("real-img", async (el, binding) => {
  const { value, defaultValue } = binding.value;
  el.onerror = () => {
    el.src = defaultValue;
  };
  if (value && !value.startsWith("/")) {
    let imgData = await stream(value);
    el.src = URL.createObjectURL(imgData);
  }
});

// 加载banner图片
app.directive("banner", async (el, binding) => {
  // 获取组件实例
  const componentInstance = binding.instance;

  // 图片ID
  let bannerId = binding.value;
  // 缓存中有该图片数据
  const cachedData = componentInstance[bannerId];
  if (cachedData) {
    el.src = URL.createObjectURL(cachedData);
    return;
  }

  if (CONFIG.selected_tenant && CONFIG.selected_tenant.code) {
    let tenantCode = CONFIG.selected_tenant.code;
    let url = `https://sso.jingantech.com/access-app-sso/v8/web/resource/preference/client/login-banner?id=${bannerId}&tenant=${tenantCode}`;
    let imgData = await stream(url);
    el.src = URL.createObjectURL(imgData);
    componentInstance[bannerId] = imgData;
  }
});

app.use(Toaster, { position: "top", maxToasts: 1 });
app.use(alert);
app.use(store);
app.use(router);
// app.use(ElementPlus);
app.use(InfiniteScroll, { locale: zhCn });
// app.use(dragVerify)
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component);
}
window.__VUE_APP__ = app;
app.mount("#app");

document.addEventListener("contextmenu", (event) => event.preventDefault());
document.addEventListener("keydown", (e) => {
  if (e.code === "F5") e.preventDefault();
  if (e.ctrlKey && e.code === "KeyP") e.preventDefault();
  if (e.ctrlKey && e.code === "KeyR") e.preventDefault();
});

setInterval(
  function () {
    checkAppUpdate(false, 5000).then(() => { });
  },
  2 * 3600 * 1000
);

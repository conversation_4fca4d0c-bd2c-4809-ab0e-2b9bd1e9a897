<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <title>过渡动画页面</title>
    <style>
      
      html {
        border: 1px solid rgb(171, 171, 171);
        height: 100vh;
        box-sizing: border-box;
      }
body {
        overflow: hidden;
        margin: 0;
        user-select: none;
        -webkit-user-select: none;
      }

      img {
        -webkit-user-drag: none;
      }

      .splash-model {
        position: absolute;
        height: 100vh;
        width: 100vw;
        /* top: 0;
        bottom: 0;
        right: 0;
        left: 0;
        margin: auto;
        text-align: center;
        font-weight: bold;
        font-family: Microsoft YaHei;
        color: rgba(0, 0, 0, 0.75); */
        /* background: url('/assets/images/sdp_start_logo.png') center no-repeat; */
      }
      .splash-model img {
        width: 100%;
        height: 100%;
      }

      .welcome-title {
        font-size: 21px;
      }

      #modal-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.5);
        display: none;
        justify-content: center;
        align-items: center;
      }

      #modal {
        position: relative;
        background: linear-gradient(180deg, #ffdbbc, #ffffff 41%);
        width: 334px;
        padding: 89px 20px 20px;
        border-radius: 12px;
        box-shadow: 0 0 10px rgba(0, 0, 0, 0.5);
        display: flex;
        flex-direction: column;
        z-index: 9999;
        box-sizing: border-box;
      }

      #modal-header {
        display: flex;
        justify-content: center;
        align-items: center;
        margin-bottom: 28px;
      }

      #modal-title {
        margin: 0;
        font-size: 20px;
        color: #000;
      }

      #modal-body {
        min-height: 90px;
        max-height: 156px;
        overflow: auto;
        color: #666;
        font-size: 15px;
        box-sizing: border-box;
        line-height: 24px;
        padding-left: 20px;
      }

      #modal-footer {
        display: flex;
        padding-top: 10px;
        justify-content: space-evenly;
        align-items: center;
      }

      #modal-cancel,
      #modal-cancel-update {
        border: 1px solid #ddd;
        background-color: #fff;
        color: #606266;
        padding: 10px 20px;
        border-radius: 30px;
        cursor: pointer;
        font-size: 14px;
      }

      #modal-ok {
        border: 1px solid #f9780c;
        background-color: #f9780c;
        color: white;
        padding: 10px 20px;
        border-radius: 30px;
        cursor: pointer;
        font-size: 14px;
      }

      #modal-cancel:hover,
      #modal-ok:hover {
        background-color: #fff;
        color: #f9780c;
        border: 1px solid #f9780c;
      }

      .logo {
        position: absolute;
        top: 0;
        left: 50%;
        transform: translate(-50%, -42%);
      }

      /* // 进度条 */
      /* 不重复进度条 */
      #progressBar {
        display: flex;
        width: 100%;
        height: 15px;
        border-radius: 25px;
        background: linear-gradient(
          90deg,
          #f9780c,
          #f9780c var(--progress),
          transparent 0
        );
        border: 1px solid #ddd;
        box-sizing: border-box;
      }

      /*  循环进度条
        #loopProgressBar {
            display: none;
            width: 100%;
            height: 15px;
            border-radius: 25px;
            background: linear-gradient(90deg, #f9780c, #f9780c var(--progress), transparent 0);
            border: 1px solid #ddd;
            box-sizing: border-box;
        } */

      #label {
        text-align: right;
        line-height: 25px;
        display: flex;
      }

      #modal-cancel-update {
        margin-top: 15px;
        display: none;
      }

      .g-container {
        display: none;
        box-sizing: border-box;
      }

      @keyframes p2 {
        100% {
          background-size: 110%;
        }
      }

      
      .cluster-config {
        display: none;
        justify-content: center;
        align-items: center;
        position: fixed;
        z-index: 1;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        overflow: auto;
        background-color: rgba(0, 0, 0, 0.5);
      }

      .cluster-config .content {
        text-align: center;
        background-color: #fefefe;
        padding: 20px 30px;
        border: 1px solid #888;
        width: 585px;
        border-radius: 20px;
      }

      .cluster-config .content .title {
        color: #000;
        font-size: 18px;
        font-weight: 500;
        margin-bottom: 30px;
      }

      .cluster-config .content .input {
        padding: 1px 11px;
        border-bottom: 1px solid #272727;
        display: inline-flex;
        width: 100%;
        box-sizing: border-box;
        margin-bottom: 30px;
      }

      .cluster-config .content input {
        width: 100%;
        box-sizing: border-box;
        border: none;
        height: 30px;
        outline: 0;
        font-size: 14px;
        font-weight: 400;
        font-stretch: 100%;
      }

      .cluster-config .content button {
        width: 100px;
        padding: 20px 35px;
        line-height: 0;
        border: 1px solid #f9780c;
        border-radius: 20px;
        background-color: #f9780c;
        color: #fff;
        cursor: pointer;
        height: 32px;
        box-sizing: border-box;
        display: inline-flex;
        align-items: center;
      }
.error {
        border: 1px solid rgb(245, 74, 69);
        background-color: rgb(254, 240, 240);
      }

      .toast-container {
        opacity: 0;
        position: fixed;
        left: 50%;
        transform: translateX(-50%) translateY(-120%);
        z-index: 1000;
        width: 230px;
        height: 54px;
        padding: 8px 16px;
        background-color: #f8d7da;
        color: #721c24;
        border-radius: 4px;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.3);
        box-sizing: border-box;
      }

      .fade-in {
        animation: fadeIn 500ms ease-in-out forwards;
      }

      .fade-out {
        animation: fadeOut 500ms ease-in-out forwards;
      }

      @keyframes fadeIn {
        0% {
          opacity: 0;
          transform: translateX(-50%) translateY(-120%);
        }
        100% {
          opacity: 1;
          transform: translateX(-50%) translateY(50%);
        }
      }

      @keyframes fadeOut {
        0% {
          opacity: 1;
          transform: translateX(-50%) translateY(50%);
        }
        100% {
          opacity: 0;
          transform: translateX(-50%) translateY(-80%);
        }
      }

      .toast {
        display: flex;
        justify-content: space-between;
        align-items: center;
        border-radius: 4px;
        height: 100%;
        width: 100%;
      }

      .toast-content {
        width: 98%;
        text-align: center;
      }

      .toast-message {
        color: #5c5b60;
        font-size: 14px;
      }

      #loading-overlay {
        display: none;
        justify-content: center;
        align-items: center;
        position: fixed;
        z-index: 1;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        overflow: auto;
        background-color: rgba(0, 0, 0, 0.5);
      }

      #loading-container {
        width: 79px;
        height: 79px;
        display: grid;
        justify-content: center;
        align-items: center;
      }

      .circular {
        width: 50px;
        height: 50px;
        border: 5px solid #fff;
        border-top-color: transparent;
        border-radius: 100%;
        animation: loading-rotate 2s linear infinite;
      }

      .loading-text {
        margin: 3px 0;
        font-size: 14px;
        color: #fff;
      }

      @keyframes loading-rotate {
        100% {
          transform: rotate(360deg);
        }
      }
    </style>
  </head>

  <body>
    <div id="loading-overlay">
      <div id="loading-container">
        <div class="circular"></div>
        <p class="loading-text">加载中...</p>
      </div>
    </div>
    <div class="toast-container error" id="toast">
      <div class="toast">
        <svg
          t="1703066128287"
          class="icon"
          viewBox="0 0 1024 1024"
          version="1.1"
          xmlns="http://www.w3.org/2000/svg"
          p-id="4223"
          width="16"
          height="16"
        >
          <path
            d="M512 0C229.376 0 0 229.376 0 512s229.376 512 512 512 512-229.376 512-512S794.624 0 512 0z m218.624 672.256c15.872 15.872 15.872 41.984 0 57.856-8.192 8.192-18.432 11.776-29.184 11.776s-20.992-4.096-29.184-11.776L512 569.856l-160.256 160.256c-8.192 8.192-18.432 11.776-29.184 11.776s-20.992-4.096-29.184-11.776c-15.872-15.872-15.872-41.984 0-57.856L454.144 512 293.376 351.744c-15.872-15.872-15.872-41.984 0-57.856 15.872-15.872 41.984-15.872 57.856 0L512 454.144l160.256-160.256c15.872-15.872 41.984-15.872 57.856 0 15.872 15.872 15.872 41.984 0 57.856L569.856 512l160.768 160.256z"
            fill="#CF3736"
            p-id="4224"
          ></path>
        </svg>
        <div class="toast-content">
          <span id="toast-message" class="toast-message"></span>
        </div>
      </div>
    </div>
    <div class="splash-model">
      <img
        src="/assets/images/oneid_start_logo.png"
        style="width: 972px; height: 652px"
        alt=""
      />
      <!-- <div class="welcome-title">欢迎使用</div> -->
    </div>
    <div id="modal-overlay">
      <div id="modal">
        <img
          class="logo"
          src="/assets/images/update.png"
          alt=""
          width="167"
          height="167"
        />
        <div id="modal-header">
          <h2 id="modal-title">版本更新</h2>
        </div>
        <div id="modal-body"></div>
        <div id="processBarContainer" class="g-container">
          <div id="label">已下载 0%</div>
          <div id="progressBar"></div>
        </div>
        <div id="modal-footer">
          <button id="modal-cancel">下次提醒</button>
          <button id="modal-ok">立即更新</button>
          <button id="modal-cancel-update">取消更新</button>
        </div>
      </div>
    </div>
    
    <div id="cluster-config" class="cluster-config">
      <div class="content">
        <div class="title">服务器设置</div>
        <div class="input">
          <input
            type="text"
            id="internalUrl"
            placeholder="服务器内网连接地址"
          />
        </div>
        <div class="input">
          <input
            type="text"
            id="externalUrl"
            placeholder="服务器外网连接地址"
          />
        </div>
        <div class="button">
          <button id="cluster_config_btn">确认</button>
        </div>
      </div>
    </div>

    <script>
      document.addEventListener("contextmenu", (event) =>
        event.preventDefault()
      );
      document.addEventListener("keydown", (e) => {
        if (e.code === "F5") e.preventDefault();
        if (e.ctrlKey && e.code === "KeyP") e.preventDefault();
        if (e.ctrlKey && e.code === "KeyR") e.preventDefault();
      });
      const firstLoginMap = JSON.parse(localStorage.getItem("firstLoginMap"));
      localStorage.clear();
      firstLoginMap &&
        localStorage.setItem("firstLoginMap", JSON.stringify(firstLoginMap));
      // ========================= toast ========================
      let TOAST_CANCEL_EVENT = null;

      const toast = document.getElementById("toast");
      const toastMessage = document.getElementById("toast-message");
      function showToast(message) {
        if (TOAST_CANCEL_EVENT) {
          clearTimeout(TOAST_CANCEL_EVENT);
          delete TOAST_CANCEL_EVENT;
        }

        toast.classList.remove("fade-out");
        toast.classList.add("fade-in");
        toastMessage.innerText = message;

        TOAST_CANCEL_EVENT = setTimeout(function () {
          closeToast();
        }, 3000);
      }

      function closeToast() {
        if (TOAST_CANCEL_EVENT) {
          clearTimeout(TOAST_CANCEL_EVENT);
          delete TOAST_CANCEL_EVENT;
        }
        // 当前没有显示
        if (
          toast.style.opacity !== "" &&
          parseInt(toast.style.opacity, 10) !== 0
        ) {
          toast.classList.remove("fade-in");
          toast.classList.add("fade-out");
        }
      }
      // ========================= toast ========================

      // 获取页面元素
      const overlay = document.getElementById("modal-overlay");
      const titleElement = document.getElementById("modal-title");
      const bodyElement = document.getElementById("modal-body");
      const cancelButton = document.getElementById("modal-cancel");
      const okButton = document.getElementById("modal-ok");
      const processBarContainer = document.getElementById(
        "processBarContainer"
      );
      const processBar = document.getElementById("progressBar");
      const cancelUpdateButton = document.getElementById("modal-cancel-update");
      const label = document.getElementById("label");
      const loadingEl = document.getElementById("loading-overlay");

      // 关闭弹窗的函数
      function closeModal() {
        overlay.style.display = "none";
      }

      // 点击取消按钮关闭弹窗
      cancelButton.addEventListener("click", async () => {
        await invoke("create_main_window");
        closeModal();
      });

      cancelUpdateButton.addEventListener("click", async () => {
        await invoke("cancel_update");
      });

      function showModal(title, content, callback) {
        // 设置标题和内容
        titleElement.textContent = title;
        let innerNotes = "";
        let notes = content.split("#");
        for (let i = 0; i < notes.length; i++) {
          if (i === 0) {
            innerNotes += '<p style="margin-top: 0">' + notes[i] + "</p>";
          } else {
            innerNotes += "<p>" + notes[i] + "</p>";
          }
        }
        bodyElement.innerHTML = innerNotes;

        // 显示弹窗
        overlay.style.display = "flex";

        // 点击确认按钮执行回调函数并关闭弹窗
        okButton.addEventListener("click", function () {
          if (typeof callback === "function") {
            callback();
          }
          cancelButton.style.display = "none";
          okButton.style.display = "none";
          closeToast();
        });
      }

      const { invoke } = window.__TAURI__.tauri;
      const { listen, emit, once } = window.__TAURI__.event;
      const { relaunch } = window.__TAURI__.process;

      function showLoading() {
        loadingEl.style.display = "flex";
      }

      function hideLoading() {
        loadingEl.style.display = "none";
      }

      var checkUpdate = function () {
        showLoading();
        invoke("check_update")
          .then((response) => {
            hideLoading();
            emit("log", {
              level: "Trace",
              message: "check update: " + JSON.stringify(response),
            });
            if (!response.shouldUpdate) {
              invoke("create_main_window");
              return;
            }
            force_update = response.payload.force;
            // 强制更新
            if (force_update) {
              cancelButton.remove();
              const footer = document.getElementById("modal-footer");
              footer.style.alignSelf = "center";
            }

            showModal(
              "发现新版本v" + response.payload.version,
              response.payload.notes,
              async () => {
                try {
                  await invoke("download");
                } catch (e) {
                  if (force_update) {
                    emit("log", {
                      level: "Error",
                      message: "auto update: {}" + e,
                    });
                  } else {
                    emit("log", {
                      level: "Warn",
                      message: "auto update: {}" + e,
                    });
                  }
                }
              }
            );
          })
          .catch((e) => {
            hideLoading();
            emit("log", {
              level: "Error",
              message: "check update: " + JSON.stringify(e),
            });
            invoke("create_main_window");
          });
      };

      
      const clusterConfigEl = document.getElementById("cluster-config");
      const clusterConfigBtn = document.getElementById("cluster_config_btn");

      // 保存集群地址
      clusterConfigBtn.addEventListener("click", function () {
        let internalUrl = document.getElementById("internalUrl").value;
        let externalUrl = document.getElementById("externalUrl").value;
        // 为空时
        if (!internalUrl && !externalUrl) {
          showToast("服务器内网地址或服务器外网地址至少填写一个");
          return;
        }

        var config = {};

        var parse_url =
          /^https?:\/\/(?:www\.)?[-a-zA-Z0-9@:%._+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b(?:[-a-zA-Z0-9()@:%_+.~#?&//=]*)$/i;
        if (internalUrl) {
          if (parse_url.test(internalUrl)) {
            config.internal = internalUrl + "/cluster_inside";
          } else {
            showToast("服务器内网地址格式不正确");
            return;
          }
        }

        if (externalUrl) {
          if (parse_url.test(externalUrl)) {
            config.external = externalUrl + "/cluster";
          } else {
            showToast("服务器外网地址格式不正确");
            return;
          }
        }

        showLoading();
        hideClusterConfigUrlDialog();

        invoke("plugin:cluster|reload_cluster_nodes", {
          config: config,
        })
          .then(() => {
            hideLoading();
            checkUpdate();
          })
          .catch((e) => {
            console.log(e);
            showToast("服务器连接失败，请稍后重试");
            hideLoading();
            showClusterConfigUrlDialog();
          });
      });

      function showClusterConfigUrlDialog() {
        clusterConfigEl.style.display = "flex";
      }

      function hideClusterConfigUrlDialog() {
        clusterConfigEl.style.display = "none";
      }
let force_update = false;

      // 下载进度
      !(async () =>
        await listen("download_progress", async (event) => {
          // 已下载大小
          let chunkLength = event.payload.chunkSize;
          // 总大小
          let contentLength = event.payload.contentLength;
          // 原生HTTP分块下载时, 长度未知
          // if (contentLength === 0) {
          //    label.innerHTML = '下载中...';
          //    processBar.style.display = 'none';
          //    loopProcessBar.style.display = 'flex';
          //    return;
          // }

          let num = Math.floor((chunkLength / contentLength) * 100);

          // 渲染进度条
          processBar.style.setProperty("--progress", num + "%");
          label.innerHTML = "已下载 " + num + "%";
        }))();

      // 监听更新事件
      !(async () =>
        await listen("update_status", async (event) => {
          let status = event.payload.status;
          switch (status) {
            // 开始下载, 显示进度条
            case "PENDING": {
              cancelButton.style.display = "none";
              okButton.style.display = "none";
              processBarContainer.style.display = "block";
              if (!force_update) {
                cancelUpdateButton.style.display = "flex";
              }
              break;
            }
            // 安装完成, 重启
            case "DONE": {
              await relaunch();
              break;
            }
            // 下载完成
            case "DOWNLOADED": {
              await invoke("install");
              break;
            }
            // 用户取消
            case "CANCEL": {
              processBarContainer.style.display = "none";
              cancelUpdateButton.style.display = "none";
              cancelButton.style.display = "flex";
              okButton.style.display = "flex";
              break;
            }
            // 更新出错
            case "ERROR": {
              emit("log", {
                level: "Error",
                message: "updator: " + event.payload.error,
              });
              showToast("更新失败");
              processBarContainer.style.display = "none";
              cancelUpdateButton.style.display = "none";
              cancelButton.style.display = "flex";
              okButton.style.display = "flex";
              break;
            }
          }
        }))();

      !(async () => {
        
        // 加载节点信息
        let info = await invoke("plugin:cluster|load");
        // 设置服务器设置
        if (info.type === "ConfigClusterUrl") {
          loadingEl.style.display = "none";

          let internalNode = document.getElementById("internalUrl");
          let externalNode = document.getElementById("externalUrl");

          var parse_url =
            /^(?:([A-Za-z]+):)?(\/{0,3})([0-9.\-A-Za-z]+)(?::(\d+))?(?:\/([^?#]*))?(?:\?([^#]*))?(?:#(.*))?$/;
          if (info.payload.internal) {
            var result = parse_url.exec(info.payload.internal);
            var url = result[1] + "://" + result[3];
            if (result[4]) {
              url += ":" + result[4];
            }
            internalNode.value = url;
          }

          if (info.payload.external) {
            var result = parse_url.exec(info.payload.external);
            var url = result[1] + "://" + result[3];
            if (result[4]) {
              url += ":" + result[4];
            }
            externalNode.value = url;
          }
          showClusterConfigUrlDialog();
        } else {
          checkUpdate();
        }
})();
    </script>
  </body>
</html>

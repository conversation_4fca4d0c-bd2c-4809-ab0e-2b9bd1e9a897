import{cf as v,bE as b,bV as f,bU as u,aR as p,aE as t,u as a,I as _,bx as n,s as y,H as h,t as l,aQ as E,v as r,b3 as m,F as C,aP as R,c1 as x,aI as N,aF as D}from"./index-8ff3976b.js";async function V(){return v({__tauriModule:"Process",message:{cmd:"relaunch"}})}const I="/assets/images/update.png";const U={data(){return{downloading:!1,percentage:0,customColor:"#F87F1A",remoteRelease:{shouldUpdate:!1},listenEvent:void 0,processEvent:void 0}},beforeUnmount(){this.listenEvent&&this.listenEvent(),this.processEvent&&this.processEvent()},async created(){setTimeout(async()=>{this.listenEvent=await f("update_status",async s=>{switch(s.payload.status){case"PENDING":{this.downloading=!0;break}case"DONE":{await V();break}case"DOWNLOADED":{await u("install");break}case"CANCEL":{this.reset();break}case"ERROR":{this.close(),await x("log",{level:"Error",message:"updater: "+s.payload.error}),this.$message({message:"更新失败",type:"error"});break}}})},0),setTimeout(async()=>{this.processEvent=await f("download_progress",async s=>{let o=s.payload.chunkSize,g=s.payload.contentLength;this.percentage=Math.floor(o/g*100)})},0)},methods:{init(s){s.notes=s.payload.notes.split("#"),this.remoteRelease=s},async download(){await u("download")},async cancelDownload(){await u("cancel_update")},close(){this.reset(),this.remoteRelease.shouldUpdate=!1},reset(){this.downloading=!1,this.percentage=0}}},L=s=>(N("data-v-b52ddfde"),s=s(),D(),s),S=L(()=>r("img",{class:"image",src:I,alt:""},null,-1)),B={class:"dialog"},F={class:"find"},O={class:"tips"},T={key:0,style:{"text-align":"center"}},A={style:{"text-align":"right",width:"90%"}},P={key:0,class:"dialog-footer"};function M(s,o,g,z,e,d){const w=p("el-progress"),i=p("el-button"),k=p("el-dialog");return t(),a("div",null,[_(k,{modelValue:e.remoteRelease.shouldUpdate,"onUpdate:modelValue":o[0]||(o[0]=c=>e.remoteRelease.shouldUpdate=c),center:"","show-close":!1,"destroy-on-close":"",class:"updaterBox","close-on-click-modal":!1,"close-on-press-escape":!1},{footer:n(()=>[e.downloading?l("",!0):(t(),a("span",P,[e.remoteRelease.force?l("",!0):(t(),y(i,{key:0,round:"",onClick:d.close},{default:n(()=>[h(" 稍后再说 ")]),_:1},8,["onClick"])),_(i,{round:"",type:"primary",onClick:d.download},{default:n(()=>[h(" 立即更新 ")]),_:1},8,["onClick"])])),e.downloading&&!e.remoteRelease.force&&e.percentage!=100?(t(),y(i,{key:1,round:"",disabled:e.percentage==100,onClick:d.cancelDownload},{default:n(()=>[h(" 取消下载 ")]),_:1},8,["disabled","onClick"])):l("",!0),!e.downloading&&!e.remoteRelease.force?E(s.$slots,"default",{key:2},void 0,!0):l("",!0)]),default:n(()=>[S,r("div",B,[r("div",F,"发现新版本v"+m(e.remoteRelease.payload.version),1),r("div",O,[(t(!0),a(C,null,R(e.remoteRelease.notes,c=>(t(),a("p",{key:c},m(c),1))),128))])]),e.downloading?(t(),a("div",T,[r("div",A,"已更新"+m(e.percentage)+"%",1),_(w,{"stroke-width":15,percentage:e.percentage,color:e.customColor,"show-text":!1},null,8,["percentage","color"])])):l("",!0)]),_:3},8,["modelValue"])])}const H=b(U,[["render",M],["__scopeId","data-v-b52ddfde"]]);export{H as u};

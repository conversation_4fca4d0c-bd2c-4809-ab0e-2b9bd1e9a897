use std::{
    net::{IpAddr, SocketAddr},
    sync::Arc,
};

use http::{header, Version};
use hyper::{body::Incoming, Request, Response, StatusCode};
use hyper_util::rt::TokioIo;
use moka::future::Cache;
use once_cell::sync::Lazy;
use reqwest::{redirect::Policy, Body, Client, ClientBuilder};
use rustls::{
    client::danger::{HandshakeSignatureValid, ServerCertVerified, ServerCertVerifier},
    crypto::{ring as provider, CryptoProvider},
    pki_types::{ServerName, UnixTime},
    ClientConfig, DigitallySignedStruct, SignatureScheme,
};
use tokio::io::copy_bidirectional;
use tokio_tungstenite::{
    connect_async_tls_with_config_and_addr, tungstenite::handshake::client::Request as WsRequest,
    Connector,
};
use tonic::transport::CertificateDer;
use url::Url;

use crate::traceability::{
    constants::RESPONSE_HEADER_SERVER,
    logs::MessageLog,
    utils::headers::{
        add_device_id_header, add_forwarding_header, add_header_entry_overwrite_if_exist,
    },
    ProxyError, TRACEABILITY_RESOURCES_VIRTUAL_IP_MAPPING,
};

use super::response_helper::http_error;

pub static CLIENTS: Lazy<Cache<(IpAddr, String), Client>> = Lazy::new(|| Cache::builder().build());

/// Return with an arbitrary status code of error and log message
fn return_with_error_log(
    status_code: StatusCode,
    log_data: &mut MessageLog,
) -> Result<Response<Body>, ProxyError> {
    log_data.status_code(&status_code).output();
    http_error(status_code)
}

/// Manipulate a response message sent from a backend application to forward downstream to a
/// client.
fn generate_response_forwarded(res: reqwest::Response) -> Response<Body> {
    let mut response = http::Response::from(res);
    let headers = response.headers_mut();

    _ = add_header_entry_overwrite_if_exist(
        headers,
        "server",
        format!("{}/{}", RESPONSE_HEADER_SERVER, version::PRODUCT_VERSION),
    );

    response
}

#[derive(Clone)]
pub(in crate::traceability) struct MessageHandler {
    pub(in crate::traceability) origin_ip: IpAddr,
    pub(in crate::traceability) device_id: String,
    client_config: Arc<ClientConfig>,
}

impl MessageHandler {
    pub fn new(ip: IpAddr, device_id: String) -> Self {
        let client_config = rustls::ClientConfig::builder_with_provider(
            CryptoProvider {
                ..provider::default_provider()
            }
            .into(),
        )
        .with_safe_default_protocol_versions()
        .expect("inconsistent cipher-suite/versions selected")
        .dangerous()
        .with_custom_certificate_verifier(Arc::new(NoVerifier))
        .with_no_client_auth();

        Self {
            origin_ip: ip,
            device_id,
            client_config: Arc::new(client_config),
        }
    }

    /// Handle incoming request message from a client
    pub async fn handle_request(
        &self,
        req: Request<Incoming>,
        client_addr: SocketAddr, // アクセス制御用
        scheme: &'static str,
        origin: SocketAddr,
    ) -> Result<Response<Body>, ProxyError> {
        let start_time = std::time::Instant::now();

        let url = match req.version() {
            Version::HTTP_2 => req.uri().to_string(),
            _ => {
                let host = req
                    .headers()
                    .get(header::HOST)
                    .map(|h| h.to_str().unwrap_or_default())
                    .ok_or(ProxyError::Request("Invalid request"))?;
                format!(
                    "{}://{}{}",
                    if req.uri().scheme_str().unwrap_or("http") == "https" {
                        "https"
                    } else {
                        "http"
                    },
                    host,
                    req.uri()
                )
            }
        }
        .parse::<Url>()
        .map_err(ProxyError::Url)?;

        let mut log_data = MessageLog::from((&req, url.clone()));
        log_data.client_addr(&client_addr);

        let Some(virtual_ip) = TRACEABILITY_RESOURCES_VIRTUAL_IP_MAPPING
            .get(&origin.ip())
            .await
        else {
            log::error!("No virtual IP mapping found for `{}`", origin.ip());
            return return_with_error_log(StatusCode::NOT_ACCEPTABLE, &mut log_data);
        };

        log_data.origin(virtual_ip);

        let response = match self
            .forward_request(url, req, virtual_ip, scheme, origin, &mut log_data)
            .await
        {
            Err(e) => {
                log::error!(
                    "Failed to generate destination uri for forward proxy: {:?}",
                    e
                );
                return return_with_error_log(StatusCode::SERVICE_UNAVAILABLE, &mut log_data);
            }
            Ok(v) => v,
        };

        log_data
            .status_code(&response.status())
            .version(response.version())
            .time(start_time.elapsed())
            .output();
        Ok(response)
    }

    #[allow(clippy::too_many_arguments)]
    async fn forward_request(
        &self,
        mut url: Url,
        mut req: Request<Incoming>,
        virtual_ip: IpAddr,
        scheme: &'static str,
        origin: SocketAddr,
        log_data: &mut MessageLog,
    ) -> Result<Response<Body>, ProxyError> {
        let host = url
            .host_str()
            .ok_or(ProxyError::Request("Invalid request"))?;

        // X-Forwarded-For
        self.add_forwarding_header(&mut req, scheme, host, origin, log_data)?;

        // 如果不是域名, 则直接修改连接地址
        if url.domain().is_none() {
            _ = url.set_ip_host(virtual_ip);
        }

        log_data.final_url(url.clone());

        let headers = req.headers();
        // 检查是否是WebSocket升级请求
        // TODO 兼容 RFC 8441
        if headers.contains_key("upgrade")
            && headers
                .get("upgrade")
                .map(|h| h.to_str().unwrap_or("").to_lowercase() == "websocket")
                .unwrap_or(false)
        {
            self.forward_websocket(url, req, scheme, origin, virtual_ip)
                .await
        } else {
            Self::forward_http(url, req, origin, virtual_ip).await
        }
    }

    fn add_forwarding_header(
        &self,
        req: &mut Request<Incoming>,
        scheme: &'static str,
        host: &str,
        origin: SocketAddr,
        log_data: &mut MessageLog,
    ) -> Result<(), ProxyError> {
        let uri = req.uri().to_string();

        // X-Forwarded-For
        add_forwarding_header(
            req.headers_mut(),
            scheme,
            host,
            self.origin_ip,
            origin.port(),
            &uri,
        )?;
        // DEVICE-ID
        add_device_id_header(req.headers_mut(), self.device_id.clone())?;

        log_data.xff(&req.headers().get("x-forwarded-for"));

        Ok(())
    }

    async fn forward_http(
        url: Url,
        req: Request<Incoming>,
        origin: SocketAddr,
        virtual_ip: IpAddr,
    ) -> Result<Response<Body>, ProxyError> {
        let client = CLIENTS
            .entry((origin.ip(), url.host_str().unwrap().to_owned()))
            .or_insert_with(async {
                let mut builder = ClientBuilder::new();
                // 不是IP地址
                if let Some(domain) = url.domain() {
                    builder = builder.resolve(domain, SocketAddr::new(virtual_ip, origin.port()));
                }

                builder
                    .no_proxy()
                    .redirect(Policy::none())
                    .danger_accept_invalid_certs(true)
                    .danger_accept_invalid_hostnames(true)
                    // .use_rustls_tls()
                    .build()
                    .unwrap()
            })
            .await;
        let client = client.value();

        let (parts, body) = req.into_parts();
        let http_request = client
            .request(parts.method, url)
            .headers(parts.headers)
            .body(Body::wrap(body))
            .build()?;

        client
            .execute(http_request)
            .await
            .map(generate_response_forwarded)
            .map_err(ProxyError::Reqwest)
    }

    async fn forward_websocket(
        &self,
        mut url: Url,
        req: Request<Incoming>,
        scheme: &str,
        origin: SocketAddr,
        virtual_ip: IpAddr,
    ) -> Result<Response<Body>, ProxyError> {
        let ws_scheme = if scheme == "https" || scheme == "wss" {
            "wss"
        } else {
            "ws"
        };
        _ = url.set_scheme(ws_scheme);
        // 创建WebSocket请求
        let mut ws_req = WsRequest::builder().uri(url.as_str());
        // 复制头信息，包括WebSocket必需的头信息
        for (name, value) in req.headers().iter() {
            ws_req = ws_req.header(name, value);
        }

        let connector = if ws_scheme == "wss" {
            Connector::Rustls(self.client_config.clone())
        } else {
            Connector::Plain
        };
        match connect_async_tls_with_config_and_addr(
            ws_req.body(()).unwrap(),
            SocketAddr::new(virtual_ip, origin.port()),
            None,
            false,
            Some(connector),
        )
        .await
        {
            Ok((mut ws_stream, response)) => {
                tokio::spawn(async move {
                    match hyper::upgrade::on(req).await {
                        Ok(upgraded) => {
                            let mut upgraded = TokioIo::new(upgraded);
                            _ = copy_bidirectional(&mut upgraded, ws_stream.get_mut()).await;
                        }
                        Err(e) => {
                            log::error!("WebSocket upgrade failed: {}", e);
                        }
                    }
                });

                let (mut parts, body) = response.into_parts();
                _ = add_header_entry_overwrite_if_exist(
                    &mut parts.headers,
                    "server",
                    format!("{}/{}", RESPONSE_HEADER_SERVER, version::PRODUCT_VERSION),
                );
                Ok(Response::from_parts(
                    parts,
                    body.map(From::from).unwrap_or_default(),
                ))
            }
            Err(e) => {
                // 连接失败
                log::error!("WebSocket connect failed: {}", e);
                Response::builder()
                    .status(StatusCode::BAD_GATEWAY)
                    .header(
                        "server",
                        format!("{}/{}", RESPONSE_HEADER_SERVER, version::PRODUCT_VERSION),
                    )
                    .body(Body::wrap(format!("WebSocket连接失败: {}", e)))
                    .map_err(ProxyError::HyperHttp)
            }
        }
    }
}

#[derive(Debug)]
pub(crate) struct NoVerifier;

impl ServerCertVerifier for NoVerifier {
    fn verify_server_cert(
        &self,
        _end_entity: &CertificateDer,
        _intermediates: &[CertificateDer],
        _server_name: &ServerName,
        _ocsp_response: &[u8],
        _now: UnixTime,
    ) -> Result<ServerCertVerified, rustls::Error> {
        Ok(ServerCertVerified::assertion())
    }

    fn verify_tls12_signature(
        &self,
        _message: &[u8],
        _cert: &CertificateDer,
        _dss: &DigitallySignedStruct,
    ) -> Result<HandshakeSignatureValid, rustls::Error> {
        Ok(HandshakeSignatureValid::assertion())
    }

    fn verify_tlcp11_signature(
        &self,
        _message: &[u8],
        _cert: &CertificateDer<'_>,
        _dss: &DigitallySignedStruct,
    ) -> Result<HandshakeSignatureValid, rustls::Error> {
        Ok(HandshakeSignatureValid::assertion())
    }

    fn verify_tls13_signature(
        &self,
        _message: &[u8],
        _cert: &CertificateDer,
        _dss: &DigitallySignedStruct,
    ) -> Result<HandshakeSignatureValid, rustls::Error> {
        Ok(HandshakeSignatureValid::assertion())
    }

    fn supported_verify_schemes(&self) -> Vec<SignatureScheme> {
        vec![
            SignatureScheme::RSA_PKCS1_SHA1,
            SignatureScheme::ECDSA_SHA1_Legacy,
            SignatureScheme::RSA_PKCS1_SHA256,
            SignatureScheme::ECDSA_NISTP256_SHA256,
            SignatureScheme::RSA_PKCS1_SHA384,
            SignatureScheme::ECDSA_NISTP384_SHA384,
            SignatureScheme::RSA_PKCS1_SHA512,
            SignatureScheme::ECDSA_NISTP521_SHA512,
            SignatureScheme::RSA_PSS_SHA256,
            SignatureScheme::RSA_PSS_SHA384,
            SignatureScheme::RSA_PSS_SHA512,
            SignatureScheme::ED25519,
            SignatureScheme::ED448,
        ]
    }
}

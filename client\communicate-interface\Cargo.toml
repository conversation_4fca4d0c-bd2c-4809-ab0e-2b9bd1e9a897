[package]
description = "SDP UI进程与核心进程通信的API"
edition = "2021"
name = "communicate-interface"
version = "0.0.0"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[dependencies]
futures = "0.3.28"
hyper-util = { version = "0.1.10", features = [
    "client",
    "client-legacy",
    "http2",
    "http1",
] }
log = "0.4.27"
parity-tokio-ipc = "0.9.0"
paths = { workspace = true }
prost = "0.13"
sdptypes = { package = "types", path = "../types" }
semver = { version = "1.0.20", features = ["serde"] }
serde_json = "1.0.96"
thiserror = "2.0.4"
tokio = { workspace = true, features = ["rt"] }
tonic = { version = "0.12", features = ["tls"] }
tower = "0.5"

[target.'cfg(unix)'.dependencies]
nix = "0.23"
once_cell = { workspace = true }

[build-dependencies]
tonic-build = { version = "0.12", default-features = false, features = [
    "transport",
    "prost",
] }

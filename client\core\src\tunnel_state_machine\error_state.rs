#[cfg(target_os = "macos")]
use std::net::Ipv4Addr;

use futures::StreamExt;

use err_ext::ErrorExt;
use types::tunnel::{ErrorStateCause, TunnelStateTransition};

use crate::tunnel_state_machine::{
    connecting_state::ConnectingState, disconnected_state::DisconnectedState, TunnelCommand,
};

use super::{
    EventConsequence, SharedTunnelStateValues, TunnelCommandReceiver, TunnelState,
    TunnelStateWrapper,
};

#[allow(dead_code)]
#[derive(Debug, Clone)]
pub struct ErrorState {
    block_reason: ErrorStateCause,
}

impl ErrorState {
    async fn reset_dns(shared_values: &mut SharedTunnelStateValues) {
        if let Err(error) = shared_values.dns_monitor.reset().await {
            log::error!("{}", error.display_chain_with_msg("Unable to reset DNS"));
        }
    }
}

#[async_trait::async_trait]
impl TunnelState for ErrorState {
    type Bootstrap = ErrorStateCause;

    #[cfg_attr(
        any(target_os = "windows", target_os = "linux"),
        allow(unused_variables)
    )]
    async fn enter(
        shared_values: &mut SharedTunnelStateValues,
        block_reason: Self::Bootstrap,
    ) -> (TunnelStateWrapper, TunnelStateTransition) {
        #[cfg(target_os = "macos")]
        if !block_reason.prevents_filtering_resolver() {
            if let Err(err) = shared_values
                .dns_monitor
                .set("lo", &[Ipv4Addr::LOCALHOST.into()])
                .await
            {
                log::error!(
                    "{}",
                    err.display_chain_with_msg(
                        "Failed to configure system to use filtering resolver"
                    )
                );
            }
        };

        (
            TunnelStateWrapper::from(ErrorState {
                block_reason: block_reason.clone(),
            }),
            TunnelStateTransition::Error(types::tunnel::ErrorState::new(block_reason)),
        )
    }

    #[cfg_attr(not(target_os = "macos"), allow(unused_mut))]
    async fn handle_event(
        self,
        commands: &mut TunnelCommandReceiver,
        shared_values: &mut SharedTunnelStateValues,
    ) -> EventConsequence {
        use self::EventConsequence::*;

        match commands.next().await {
            Some(TunnelCommand::Dns(_servers)) => SameState(self.into()),
            Some(TunnelCommand::ResetDns(tx)) => {
                _ = shared_values.dns_monitor.reset().await;
                if let Some(tx) = tx {
                    _ = tx.send(());
                }
                SameState(self.into())
            }
            Some(TunnelCommand::Routes(_)) => {
                _ = shared_values.route_manager.clear_routes();
                SameState(self.into())
            }
            Some(TunnelCommand::AskIsOffline(tx)) => {
                let connectivity = shared_values.offline_monitor.connectivity().await;
                _ = tx.send(connectivity.is_offline());
                SameState(self.into())
            }
            Some(TunnelCommand::Down) => {
                _ = shared_values.set_dns_servers(None);
                SameState(self.into())
            }
            Some(TunnelCommand::Connect) => {
                Self::reset_dns(shared_values).await;

                NewState(ConnectingState::enter(shared_values, ()).await)
            }
            Some(TunnelCommand::Disconnect(_)) | None => {
                // #[cfg(target_os = "linux")]
                // shared_values.reset_connectivity_check();
                Self::reset_dns(shared_values).await;
                NewState(DisconnectedState::enter(shared_values, ()).await)
            }
            _ => SameState(self.into()),
        }
    }
}

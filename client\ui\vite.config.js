import { defineConfig } from "vite";
import vitePluginConditionalCompile from "vite-plugin-conditional-compile";
import vue from "@vitejs/plugin-vue";
import path from "path"; //node 需要14.18.1以上版本

// https://vitejs.dev/config/
export default defineConfig(() => {
  const includeSDP = process.argv.includes("sdp");
  const includeIAM = process.argv.includes("iam");
  const CLUSTER = process.argv.includes("cluster");

  console.log("includeSDP: " + includeSDP);
  console.log("includeIAM: " + includeIAM);
  console.log("includeCluster: " + CLUSTER);
  console.log("platform: " + process.env.TAURI_PLATFORM);

  return {
    plugins: [
      {
        ...vitePluginConditionalCompile({
          env: {
            includeSDP: includeSDP,
            includeIAM: includeIAM,
            includeCluster: CLUSTER,
            platform: process.env.TAURI_PLATFORM,
          },
        }),
        enforce: "pre",
      },
      vue(),
    ],
    resolve: {
      alias: {
        "@": path.resolve(__dirname, "./src"),
      },
      extensions: [".mjs", ".js", ".ts", ".jsx", ".tsx", ".json", ".vue"],
    },
    envPrefix: ["VITE_", "TAURI_"],
    build: {
      rollupOptions: {
        input: {
          index: path.resolve(__dirname, "index.html"),
          splashscreen: path.resolve(__dirname, "splashscreen.html"),
        },
      },
      // Tauri supports es2021
      target:
        process.env.TAURI_PLATFORM == "windows" ? "chrome105" : "safari13",
      // don't minify for debug builds
      minify: !process.env.TAURI_DEBUG ? "esbuild" : false,
      // produce sourcemaps for debug builds
      sourcemap: !!process.env.TAURI_DEBUG,
    },
    server: {
      host: "127.0.0.1",
      port: 8081,
      hmr: {
        protocol: "ws",
        host: "127.0.0.1",
        port: 8081,
      }
    },
    optimizeDeps: {
      exclude: ["axios-tauri-adapter"],
    },
  };
});

use communicate_interface::ControlServiceClient;
use tauri::{AppHandle, Runtime};
use types::states::TunnelState;

use crate::notify;

pub async fn check_tunnel_state<R: Runtime>(
    app_handle: AppHandle<R>,
    mut rpc: ControlServiceClient,
) {
    loop {
        let state = rpc
            .get_tunnel_state(tonic::Request::new(()))
            .await
            .unwrap()
            .into_inner();

        let state = TunnelState::try_from(state);
        match state {
            Ok(TunnelState::Connected) => {
                break;
            }
            Ok(TunnelState::Error(_)) => {
                tunnel_error(app_handle).await;
                break;
            }
            _ => (),
        }

        tokio::time::sleep(std::time::Duration::from_millis(50)).await;
    }
}

pub async fn tunnel_error<R: Runtime>(app_handle: AppHandle<R>) {
    // 网卡启动错误
    cfg_if::cfg_if! {
        if #[cfg(windows)] {
            use windows::core::w;
            use windows::Win32::UI::WindowsAndMessaging::{MessageBoxW, MB_OK};
            if crate::system::is_windows_seven() {
                unsafe {
                    MessageBoxW(None, w!("虚拟网卡创建失败"), w!("错误"), MB_OK);
                }
            } else {
                notify!(app_handle.clone(), "虚拟网卡创建失败");
            }
        } else {
            notify!(app_handle.clone(), "虚拟网卡创建失败");
        }
    }

    crate::async_exit(&app_handle).await;
}

#[cfg(feature = "sdp")]
use std::sync::atomic::Ordering;
use std::{
    path::PathBuf,
    pin::Pin,
    sync::{Arc, Mutex},
    task::{Context, Poll},
    time::Duration,
};

#[cfg(feature = "sdp")]
use futures::future::BoxFuture;

use futures::{
    channel::{mpsc, oneshot},
    FutureExt, Stream, StreamExt,
};
#[cfg(feature = "sdp")]
use ipnet::IpNet;
use serde_json::Value;
#[cfg(feature = "sdp")]
use serde_json::{json, Number};
use tokio::time::timeout;
use tokio_stream::wrappers::UnboundedReceiverStream;
use tonic::{
    metadata::{AsciiMetadataValue, MetadataMap},
    Code, Request, Response, Status,
};

use communicate_interface::{
    types::{self as comm_types, check_update_param::Config, core_event},
    CommunicateService, ServerJoinHandle,
};
use err_ext::ErrorExt;
use oneidcore::EventListener;
#[cfg(feature = "sdp")]
use tlv_types::ModePayload;
#[cfg(feature = "sdp")]
use types::backend::{
    CancelAuth, ChangePwdCode, ChangePwdOldPwd, CompleteAuth, ConnectionConfig, Login,
};
use types::{
    net::Connectivity,
    states::{BackendState, TunnelState},
    updater::{UpdateResponse, UpdateResponseInner},
};

#[cfg(feature = "sdp")]
use crate::{
    backend_state_machine::BackendCommand,
    proxy_request::{ProxyHandle, ProxyHttp, ProxyHttpResult, REQUESTS, REQUEST_SEQUENCE},
    tunnel_state_machine::{ResourceEvent, TunnelCommand},
    InternalCoreEvent,
};

use crate::{
    updater::{self, Update, UpdateBuilder},
    CoreCommandSender,
};
use types::backend::TicketType;

const RPC_SERVER_SHUTDOWN_TIMEOUT: Duration = Duration::from_secs(1);

#[derive(thiserror::Error, Debug)]
pub enum Error {
    // Unable to start the management interface server
    #[error("Unable to start management interface server")]
    SetupError(#[source] communicate_interface::Error),
}

type EventsListenerSender =
    tokio::sync::mpsc::UnboundedSender<Result<comm_types::CoreEvent, Status>>;

struct EventsListenerReceiver {
    inner: UnboundedReceiverStream<Result<comm_types::CoreEvent, Status>>,
    core_tx: CoreCommandSender,
}

impl Drop for EventsListenerReceiver {
    fn drop(&mut self) {
        _ = self.core_tx.send(InternalCoreEvent::TriggerShutdown);
    }
}

impl Stream for EventsListenerReceiver {
    type Item = Result<comm_types::CoreEvent, Status>;

    fn poll_next(mut self: Pin<&mut Self>, cx: &mut Context<'_>) -> Poll<Option<Self::Item>> {
        self.inner.poll_next_unpin(cx)
    }
}

struct CommunicateServiceImpl {
    /// 设备ID
    device_id: String,
    /// 系统环境数据
    system_info: Value,
    core_tx: CoreCommandSender,
    #[cfg(feature = "sdp")]
    proxy_handle: ProxyHandle,
    subscriptions: Arc<Mutex<Vec<EventsListenerSender>>>,
    broadcast: CommunicateInterfaceEventBroadcaster,
}

#[communicate_interface::async_trait]
impl CommunicateService for CommunicateServiceImpl {
    async fn get_device_id(&self, _: Request<()>) -> Result<Response<String>, Status> {
        log::debug!("get device id");
        Ok(Response::new(self.device_id.clone()))
    }

    async fn get_system_info(&self, _: Request<()>) -> Result<Response<String>, Status> {
        log::debug!("get system info");
        Ok(Response::new(
            serde_json::to_string(&self.system_info).unwrap(),
        ))
    }

    async fn get_tunnel_state(
        &self,
        _: Request<()>,
    ) -> Result<Response<comm_types::TunnelState>, Status> {
        log::trace!("get tunnel state");
        let (tx, rx) = oneshot::channel();
        _ = self.core_tx.send(InternalCoreEvent::GetTunnelState(tx));
        Ok(Response::new(rx.await.unwrap().into()))
    }

    async fn exit(&self, _: Request<()>) -> Result<Response<()>, Status> {
        #[cfg(feature = "sdp")]
        {
            log::debug!("exit");
            _ = self.core_tx.send(InternalCoreEvent::TriggerShutdown);
            Ok(Response::new(()))
        }
    }

    async fn login(&self, request: Request<comm_types::Login>) -> Result<Response<String>, Status> {
        #[cfg(not(feature = "sdp"))]
        return Err(Status::unimplemented(String::new()));
        #[cfg(feature = "sdp")]
        {
            fn do_login(
                payload: Login,
                system_info: Value,
                core_tx: CoreCommandSender,
            ) -> BoxFuture<'static, Result<Response<String>, Status>> {
                log::debug!("login_controller({:?})", payload);

                async move {
                    let (tx, rx) = oneshot::channel();
                    core_tx
                        .send_to_backend(BackendCommand::Login {
                            tx,
                            system_info: system_info.clone(),
                            payload: payload.clone(),
                        })
                        .map_err(|_| {
                            Status::internal("the core channel receiver has been dropped")
                        })?;

                    match wait_for_result(rx).await? {
                        Ok(result) => {
                            let mut back_data = json!({
                                "payload":{
                                    "token": result.token,
                                    "pwd_tip_day": result.expire_tip_day,
                                    "expire_day": result.expire_day,
                                    "dns": result.dns,
                                }
                            });
                            match result.data {
                                #[allow(unused_variables)]
                                ModePayload::Mix {
                                    reconnect_token,
                                    expire,
                                } => {
                                    if core_tx
                                        .send(InternalCoreEvent::ReconnectToken(
                                            reconnect_token.clone(),
                                            expire,
                                        ))
                                        .is_err()
                                    {
                                        log::error!("Core already down or thread panicked");
                                    }
                                    back_data["mode"] = Value::String(String::from("Mix"));
                                    back_data["payload"]["ticket"] = Value::String(reconnect_token);
                                    back_data["payload"]["expire"] =
                                        Value::Number(Number::from(expire));
                                }
                                ModePayload::Standard { gateway } => {
                                    back_data["mode"] = Value::String(String::from("Standard"));
                                    back_data["payload"]["gateway"] = gateway;
                                }
                            }
                            Ok(Response::new(serde_json::to_string(&back_data).unwrap()))
                        }
                        // 在代理认证时, 如果是正常断开, 则表示代理连接超时,
                        // 重新走正常认证流程(发送SPA->建立连接->认证)
                        Err(oneidcore::Error::ProxyConnectionClosed) => {
                            do_login(payload, system_info, core_tx).await
                        }
                        Err(error) => Err(map_crate_error(error)),
                    }
                }
                .boxed()
            }
            let payload = Login::try_from(request.into_inner()).map_err(map_protobuf_type_err)?;
            do_login(payload, self.system_info.clone(), self.core_tx.clone()).await
        }
    }

    async fn change_pwd_by_code(
        &self,
        request: Request<comm_types::ChangePwdCode>,
    ) -> Result<Response<String>, Status> {
        #[cfg(not(feature = "sdp"))]
        return Err(Status::unimplemented(String::new()));

        #[cfg(feature = "sdp")]
        {
            let payload =
                ChangePwdCode::try_from(request.into_inner()).map_err(map_protobuf_type_err)?;

            log::debug!("change_pwd_by_code({:?})", payload);

            let (tx, rx) = oneshot::channel();
            self.send_backend_command(BackendCommand::ChangePwdByCode(
                tx,
                self.system_info.clone(),
                payload,
            ))?;

            let change_result = wait_for_result(rx).await?;
            change_result.map(Response::new).map_err(map_crate_error)
        }
    }

    async fn disconnect(&self, _: Request<()>) -> Result<Response<bool>, Status> {
        #[cfg(not(feature = "sdp"))]
        return Err(Status::unimplemented(String::new()));

        #[cfg(feature = "sdp")]
        {
            log::debug!("disconnect");

            let (tx, rx) = oneshot::channel();
            let result = self
                .send_backend_command(BackendCommand::Disconnect(Some(tx)))
                .map(|_| Response::new(true));

            _ = rx.await;

            result
        }
    }

    type EventsListenStream = EventsListenerReceiver;

    async fn events_listen(
        &self,
        _: Request<()>,
    ) -> Result<Response<Self::EventsListenStream>, Status> {
        let (tx, rx) = tokio::sync::mpsc::unbounded_channel();

        let mut subscriptions = self.subscriptions.lock().unwrap();
        subscriptions.push(tx);

        Ok(Response::new(EventsListenerReceiver {
            inner: UnboundedReceiverStream::new(rx),
            core_tx: self.core_tx.clone(),
        }))
    }

    async fn next_auth_event(
        &self,
        _: Request<()>,
    ) -> Result<Response<comm_types::AuthEvent>, Status> {
        #[cfg(not(feature = "sdp"))]
        return Err(Status::unimplemented(String::new()));

        #[cfg(feature = "sdp")]
        {
            log::debug!("next_auth_event");

            let (tx, rx) = oneshot::channel();
            self.send_tunnel_command(TunnelCommand::Resource(ResourceEvent::GetFirstBlock(tx)))?;

            let auth_resource = wait_for_result(rx).await?;
            let result = auth_resource.map(|resource| comm_types::AuthEvent {
                ip_addr: resource.map(|ip| match ip {
                    IpNet::V4(ip) => ip.addr().octets().to_vec(),
                    IpNet::V6(ip) => ip.addr().octets().to_vec(),
                }),
            });
            result
                .map(|event| Ok(Response::new(event)))
                .unwrap_or(Err(Status::not_found("nothing")))
        }
    }

    async fn complete_auth_event(
        &self,
        request: Request<comm_types::CompleteAuth>,
    ) -> Result<Response<bool>, Status> {
        #[cfg(not(feature = "sdp"))]
        return Err(Status::unimplemented(String::new()));

        #[cfg(feature = "sdp")]
        {
            let message =
                CompleteAuth::try_from(request.into_inner()).map_err(map_protobuf_type_err)?;

            log::debug!("complete_auth_event({:?})", message);

            let (tx, rx) = oneshot::channel();
            self.send_backend_command(BackendCommand::CompleteAuth(message, tx))
                .map(Response::new)?;

            wait_for_result(rx).await.map(Response::new)
        }
    }

    async fn cancel_auth_event(
        &self,
        request: Request<comm_types::CancelAuth>,
    ) -> Result<Response<bool>, Status> {
        #[cfg(not(feature = "sdp"))]
        return Err(Status::unimplemented(String::new()));

        #[cfg(feature = "sdp")]
        {
            let message =
                CancelAuth::try_from(request.into_inner()).map_err(map_protobuf_type_err)?;

            log::debug!("cancel_auth_event({:?})", message);

            let (tx, rx) = oneshot::channel();
            self.send_backend_command(BackendCommand::CancelAuth(message, tx))
                .map(Response::new)?;

            wait_for_result(rx).await.map(Response::new)
        }
    }

    async fn get_ticket(&self, _request: Request<()>) -> Result<Response<()>, Status> {
        #[cfg(not(feature = "sdp"))]
        return Err(Status::unimplemented(String::new()));
        #[cfg(feature = "sdp")]
        {
            log::debug!("get ticket");

            self.send_backend_command(BackendCommand::GetTicket)
                .map(Response::new)
        }
    }

    async fn change_pwd_by_old_pwd(
        &self,
        request: Request<comm_types::ChangePwdOldPwd>,
    ) -> Result<Response<()>, Status> {
        #[cfg(not(feature = "sdp"))]
        return Err(Status::unimplemented(String::new()));

        #[cfg(feature = "sdp")]
        {
            let message =
                ChangePwdOldPwd::try_from(request.into_inner()).map_err(map_protobuf_type_err)?;

            log::debug!("Change password by old password");

            self.send_backend_command(BackendCommand::ChangePwdByOldPwd(message))
                .map(Response::new)
        }
    }

    async fn http_request(
        &self,
        request: Request<comm_types::ProxyRequest>,
    ) -> Result<Response<comm_types::ProxyResponse>, Status> {
        #[cfg(not(feature = "sdp"))]
        return Err(Status::unimplemented(String::new()));

        #[cfg(feature = "sdp")]
        {
            let proxy_request = request.into_inner();

            let (tx, mut rx) = mpsc::channel(1);
            let (chunk_tx, mut chunk_rx) = mpsc::channel(1);
            // 缓存等待结果
            let seq = REQUEST_SEQUENCE.fetch_add(1, Ordering::Acquire);
            REQUESTS.insert(seq, (tx, chunk_tx)).await;

            log::trace!("Received http request: {seq}");

            // 执行请求
            self.proxy_handle.send_request(ProxyHttp {
                connection_config: Some(
                    ConnectionConfig::try_from(proxy_request.connection_config.unwrap()).unwrap(),
                ),
                seq,
                request: proxy_request.request,
            });

            let result = match rx.next().await {
                Some(result) => match result {
                    ProxyHttpResult::Ok(result, is_chunk) => {
                        if is_chunk {
                            let mut chunk_bytes = vec![];
                            while let Some(chunk) = chunk_rx.next().await {
                                chunk_bytes.extend(chunk);
                            }
                            Ok(Response::new(comm_types::ProxyResponse {
                                response: Some(comm_types::proxy_response::Response::Ok({
                                    comm_types::ResponseData {
                                        response: result,
                                        data: Some(chunk_bytes),
                                    }
                                })),
                            }))
                        } else {
                            Ok(Response::new(comm_types::ProxyResponse {
                                response: Some(comm_types::proxy_response::Response::Ok({
                                    comm_types::ResponseData {
                                        response: result,
                                        data: None,
                                    }
                                })),
                            }))
                        }
                    }
                    ProxyHttpResult::Timeout => Ok(Response::new(comm_types::ProxyResponse {
                        response: Some(comm_types::proxy_response::Response::Timeout(())),
                    })),
                    ProxyHttpResult::ConnectionFailed => {
                        Ok(Response::new(comm_types::ProxyResponse {
                            response: Some(comm_types::proxy_response::Response::ConnectionFailed(
                                (),
                            )),
                        }))
                    }
                    ProxyHttpResult::Fail(message) => {
                        Ok(Response::new(comm_types::ProxyResponse {
                            response: Some(comm_types::proxy_response::Response::Err(message)),
                        }))
                    }
                },
                None => Err(Status::internal("sender was dropped")),
            };
            log::trace!("Http response: {seq}");
            result
        }
    }

    async fn check_update(
        &self,
        request: Request<comm_types::CheckUpdateParam>,
    ) -> Result<Response<comm_types::UpdateResponse>, Status> {
        let check_update_param = request.into_inner();
        let updater = match check_update_param.config.unwrap() {
            Config::ConnectionConfig(_connection_config) => {
                #[cfg(not(feature = "sdp"))]
                return Err(Status::unimplemented(String::new()));

                #[cfg(feature = "sdp")]
                {
                    let connection_config = ConnectionConfig::try_from(_connection_config)
                        .map_err(map_protobuf_type_err)?;

                    UpdateBuilder::default()
                        .model(check_update_param.model)
                        .proxy_handle(self.proxy_handle.clone())
                        .connection_config(connection_config)
                        .build()
                        .await
                }
            }
            Config::Endpoint(_endpoint) => {
                #[cfg(feature = "sdp")]
                return Err(Status::unimplemented(String::new()));

                #[cfg(not(feature = "sdp"))]
                {
                    UpdateBuilder::default()
                        .model(check_update_param.model)
                        .endpoint(_endpoint)
                        .build()
                        .await
                }
            }
        };

        match updater {
            None => Err(Status::new(Code::Unavailable, "Update unavailable")),
            Some(update) => {
                let response = UpdateResponse {
                    should_update: update.should_update(),
                    payload: update
                        .remote_release()
                        .map(|remote_release| UpdateResponseInner {
                            force: remote_release.force,
                            version: remote_release.version.to_string(),
                            notes: remote_release.notes.clone().unwrap_or_default(),
                        }),
                }
                .into();

                let mut updater = updater::UPDATE.write().await;
                if update.should_update() {
                    *updater = Some(update);
                }

                Ok(Response::new(response))
            }
        }
    }

    async fn download(&self, _request: Request<()>) -> Result<Response<()>, Status> {
        let mut update = updater::UPDATE.write().await;

        let Some(update) = update.as_mut() else {
            return Err(Status::new(Code::Unavailable, "Update unavailable"));
        };

        update.download(self.broadcast.clone()).await;

        Ok(Response::new(()))
    }

    async fn cancel_update(&self, _request: Request<()>) -> Result<Response<()>, Status> {
        Update::cancel();

        let update = updater::UPDATE.read().await;
        let Some(update) = update.as_ref() else {
            return Err(Status::new(Code::Unavailable, "Update unavailable"));
        };

        update.clean();

        Ok(Response::new(()))
    }

    async fn install(&self, _request: Request<()>) -> Result<Response<()>, Status> {
        let mut update = updater::UPDATE.write().await;
        let Some(update) = update.as_mut() else {
            return Err(Status::new(Code::Unavailable, "Update unavailable"));
        };

        update
            .install()
            .await
            .map_err(|error| {
                log::error!("Install update: {error}");
                Status::new(Code::Internal, error.to_string())
            })
            .map(Response::new)
    }
}

#[cfg(feature = "sdp")]
impl CommunicateServiceImpl {
    /// Sends a command to the oneidcore and maps the error to an RPC error.
    fn send_backend_command(&self, command: BackendCommand) -> Result<(), Status> {
        self.core_tx
            .send_to_backend(command)
            .map_err(|_| Status::internal("the core channel receiver has been dropped"))
    }

    /// Sends a command to the tunnel and maps the error to an RPC error.
    fn send_tunnel_command(&self, command: TunnelCommand) -> Result<(), Status> {
        self.core_tx
            .send_to_tunnel(command)
            .map_err(|_| Status::internal("the core channel receiver has been dropped"))
    }
}

async fn wait_for_result<T>(rx: oneshot::Receiver<T>) -> Result<T, Status> {
    rx.await.map_err(|_| Status::internal("sender was dropped"))
}

pub struct CommunicateInterfaceServer {
    /// The rpc server spawned by [`Self::start`]. When the underlying join handle yields, the rpc
    /// server has shutdown.
    rpc_server_join_handle: ServerJoinHandle,
    /// Channel used to signal the running gRPC server to shutdown. This needs to be done before
    /// awaiting trying to join [`Self::rpc_server_join_handle`].
    server_abort_tx: mpsc::Sender<()>,
}

impl CommunicateInterfaceServer {
    pub async fn start(
        device_id: String,
        system_info: Value,
        core_tx: CoreCommandSender,
        rpc_socket_path: PathBuf,
        #[cfg(feature = "sdp")] proxy_handle: ProxyHandle,
    ) -> Result<(Self, CommunicateInterfaceEventBroadcaster), Error> {
        let subscriptions = Arc::<Mutex<Vec<EventsListenerSender>>>::default();

        let (server_abort_tx, server_abort_rx) = mpsc::channel(0);

        let broadcast = CommunicateInterfaceEventBroadcaster {
            subscriptions: subscriptions.clone(),
        };

        let server = CommunicateServiceImpl {
            device_id,
            system_info,
            core_tx,
            subscriptions,
            #[cfg(feature = "sdp")]
            proxy_handle,
            broadcast: broadcast.clone(),
        };
        let rpc_server_join_handle = communicate_interface::spawn_rpc_server(
            server,
            async move {
                server_abort_rx.into_future().await;
            },
            &rpc_socket_path,
        )
        .await
        .map_err(Error::SetupError)?;

        log::info!(
            "Communicate interface listening on {}",
            rpc_socket_path.display()
        );

        Ok((
            CommunicateInterfaceServer {
                rpc_server_join_handle,
                server_abort_tx,
            },
            broadcast,
        ))
    }

    /// Wait for the server to shut down gracefully. If that does not happend within
    /// [`RPC_SERVER_SHUTDOWN_TIMEOUT`], the gRPC server is aborted and we yield the async
    /// execution.
    pub async fn stop(mut self) {
        use futures::SinkExt;
        // Send a singal to the underlying RPC server to shut down.
        _ = self.server_abort_tx.send(()).await;

        match timeout(RPC_SERVER_SHUTDOWN_TIMEOUT, self.rpc_server_join_handle).await {
            // Joining the rpc server handle timed out
            Err(timeout) => {
                log::error!("Timed out while shutting down communicate server: {timeout}");
            }
            Ok(join_result) => {
                if let Err(_error) = join_result {
                    log::error!("Communicate server task failed to execute until completion");
                }
            }
        }
    }
}

/// A handle that allows broadcasting messages to all subscribers of the management interface.
#[derive(Clone)]
pub struct CommunicateInterfaceEventBroadcaster {
    subscriptions: Arc<Mutex<Vec<EventsListenerSender>>>,
}

impl CommunicateInterfaceEventBroadcaster {
    fn notify(&self, value: comm_types::CoreEvent) {
        let mut subscriptions = self.subscriptions.lock().unwrap();
        subscriptions.retain(|tx| tx.send(Ok(value.clone())).is_ok());
    }
}

impl EventListener for CommunicateInterfaceEventBroadcaster {
    /// Sends a new tunnel state update to all `new_state` subscribers of the management interface.
    fn notify_new_tunnel_state(&self, new_state: TunnelState) {
        self.notify(comm_types::CoreEvent {
            event: Some(core_event::Event::TunnelState(
                comm_types::TunnelState::from(new_state),
            )),
        })
    }

    /// Sends a new backend state update to all `new_state` subscribers of the management interface.
    fn notify_new_backend_state(&self, new_state: BackendState) {
        self.notify(comm_types::CoreEvent {
            event: Some(core_event::Event::BackendState(
                comm_types::BackendState::from(new_state),
            )),
        })
    }

    fn notify_connectivity(&self, connectivity: Connectivity) {
        self.notify(comm_types::CoreEvent {
            event: Some(core_event::Event::Connectivity(
                comm_types::Connectivity::from(connectivity),
            )),
        })
    }

    #[allow(dead_code)]
    fn notify_close_reason(&self, reason: u8) {
        self.notify(comm_types::CoreEvent {
            event: Some(core_event::Event::CloseReason(comm_types::CloseReason {
                reason: reason as u32,
            })),
        })
    }

    fn notify_notification_reason(&self, r#type: u8) {
        self.notify(comm_types::CoreEvent {
            event: Some(core_event::Event::NotificationType(
                comm_types::Notification {
                    r#type: r#type as u32,
                },
            )),
        })
    }

    fn notify_change_pwd_result(&self, bytes: &[u8]) {
        self.notify(comm_types::CoreEvent {
            event: Some(core_event::Event::ChangePwdResult(
                comm_types::ChangePwdResult {
                    result: bytes.to_vec(),
                },
            )),
        })
    }

    fn notify_need_auth(&self) {
        self.notify(comm_types::CoreEvent {
            event: Some(core_event::Event::NeedAuth(comm_types::NeedAuth {})),
        })
    }

    fn notify_ticket_result(&self, bytes: &[u8], ticket_type: TicketType) {
        self.notify(comm_types::CoreEvent {
            event: Some(core_event::Event::Ticket(comm_types::Ticket {
                ticket: bytes.to_vec(),
                ticket_type: ticket_type as i32,
            })),
        })
    }

    #[allow(dead_code)]
    fn notify_denied_access(&self, bytes: &[u8]) {
        self.notify(comm_types::CoreEvent {
            event: Some(core_event::Event::AccessDenied(comm_types::AccessDenied {
                ip_addr: bytes.to_vec(),
            })),
        })
    }

    fn notify_environment(&self, bytes: Vec<u8>) {
        self.notify(comm_types::CoreEvent {
            event: Some(core_event::Event::Environment(comm_types::Environment {
                data: bytes,
            })),
        })
    }

    fn notify_update_status(&self, status: &str, msg: Option<String>) {
        self.notify(comm_types::CoreEvent {
            event: Some(core_event::Event::UpdateStatus(comm_types::UpdateStatus {
                status: status.to_owned(),
                msg,
            })),
        })
    }

    fn notify_download_progress(&self, chunk_size: usize, content_length: u64) {
        self.notify(comm_types::CoreEvent {
            event: Some(core_event::Event::DownloadProgress(
                comm_types::DownloadProgress {
                    chunk_size: chunk_size as u64,
                    content_length,
                },
            )),
        })
    }
}

/// Converts an instance of [`crate::Error`] into a tonic status.
fn map_crate_error(error: oneidcore::Error) -> Status {
    log::debug!("error: {error}");
    match error {
        oneidcore::Error::NeedChangePwd(result) => {
            let mut metadata = MetadataMap::new();
            metadata.append("error-code", AsciiMetadataValue::from(613i32));
            Status::with_details_and_metadata(
                Code::Unauthenticated,
                "see details",
                result.into_bytes().into(),
                metadata,
            )
        }
        oneidcore::Error::AuthFailed(result) => {
            let mut metadata = MetadataMap::new();
            metadata.append("error-code", AsciiMetadataValue::from(601i32));
            Status::with_details_and_metadata(
                Code::Unauthenticated,
                "see details",
                result.into_bytes().into(),
                metadata,
            )
        }
        error => {
            let message = error.display_chain();
            Status::new(Code::Unknown, message)
        }
    }
}

fn map_protobuf_type_err(err: comm_types::FromProtobufTypeError) -> Status {
    match err {
        comm_types::FromProtobufTypeError::InvalidArgument(err) => Status::invalid_argument(err),
    }
}

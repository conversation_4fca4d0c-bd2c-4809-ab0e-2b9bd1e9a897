use std::{
    collections::{HashMap, HashSet},
    time::Duration,
};

use err_ext::ErrorExt;
use reqwest::{
    header::{HeaderMap, HeaderValue, AUTHORIZATION},
    Client, ClientBuilder,
};
use serde_json::Value;
use tracing::{error, info, warn};

use base::generate_ua;
use url::Url;

use super::*;
use crate::{config::BackendConfig, types::BackendResource};

fn default_headers() -> HeaderMap {
    let mut headers = HeaderMap::new();
    headers.append("Content-Type", HeaderValue::from_static("application/json"));
    headers.append("Accept", HeaderValue::from_static("application/json"));
    headers.append(AUTHORIZATION, HeaderValue::from_static(SDP_SERVICE_SECRET));
    headers
}

pub struct BackendCommonClient {
    // 服务端地址
    base_url: String,
    auth_ctx: String,
    resource_ctx: String,
    client: Client,
    config: BackendConfig,
}

impl BackendCommonClient {
    pub fn new(config: BackendConfig) -> Self {
        let headers = default_headers();
        let client = ClientBuilder::new()
            .user_agent("sdp/1.0")
            .default_headers(headers)
            .timeout(Duration::from_secs(config.timeout))
            .danger_accept_invalid_certs(config.ignore_unknown_ca)
            .danger_accept_invalid_hostnames(config.ignore_invalid_cn)
            .use_rustls_tls()
            .build()
            .expect("failed to build client");

        let mut base_url = config.url.as_str().to_owned();
        if base_url.ends_with("/") {
            base_url = base_url.strip_suffix("/").unwrap().to_owned();
        }

        info!(url = base_url, "creating backend client");
        BackendCommonClient {
            base_url,
            auth_ctx: config.auth_ctx.clone(),
            resource_ctx: config.resource_ctx.clone(),
            client,
            config,
        }
    }

    pub fn base_url(&self) -> Url {
        Url::parse(&self.base_url).unwrap()
    }

    pub fn new_client(&self, device_id: &str, tenant: &str, env: &Value) -> BackendClient {
        BackendClient::new(tenant, device_id, env, &self.config)
    }

    /// 登录
    pub async fn auth(
        &self,
        tenant: &str,
        buf: Vec<u8>,
        ip: &str,
        env: &Value,
    ) -> Result<Value, Vec<u8>> {
        let url = format!("{}{}{}", self.base_url, self.auth_ctx, SDP_AUTH);
        match self
            .client
            .post(&url)
            .header("TENANT", tenant)
            .header("x-forwarded-for", ip)
            .header("APP-TYPE", "DESKTOP")
            .header("User-Agent", generate_ua(env))
            .body(buf)
            .send()
            .await
        {
            Ok(response) => {
                if response.status().is_server_error() {
                    return Err(RestResult::failed(SYSTEM_ERROR, SYSTEM_ERROR_MESSAGE).as_bytes());
                }
                let bytes = response.bytes().await.unwrap_or_default();

                match parse_result::<Value>(&bytes) {
                    Ok(result) => {
                        Ok(result.unwrap_or(Value::Null))
                        // if let Ok(token) = hex::decode::<&str>(&token) {
                        //     return Ok(token);
                        // }
                        // return Err(RestResult::failed(SYSTEM_ERROR,
                        // SYSTEM_ERROR_MESSAGE).buffer());
                    }
                    Err(err) => Err(RestResult::failed_with_err(err).as_bytes()),
                }
            }
            Err(e) => {
                warn!("authentication failed, {}", e);
                Err(RestResult::failed(SYSTEM_ERROR, SYSTEM_ERROR_MESSAGE).as_bytes())
            }
        }
    }

    pub async fn auth_sms(
        &self,
        tenant: &str,
        buf: Vec<u8>,
        ip: &str,
        env: &Value,
    ) -> Result<Value, Vec<u8>> {
        let url = format!("{}{}{}", self.base_url, self.auth_ctx, SDP_SMS_AUTH);
        match self
            .client
            .post(&url)
            .header("TENANT", tenant)
            .header("x-forwarded-for", ip)
            .header("APP-TYPE", "DESKTOP")
            .header("User-Agent", generate_ua(env))
            .body(buf)
            .send()
            .await
        {
            Ok(response) => {
                if response.status().is_server_error() {
                    return Err(RestResult::failed(SYSTEM_ERROR, SYSTEM_ERROR_MESSAGE).as_bytes());
                }
                let bytes = response.bytes().await.unwrap_or_default();

                match parse_result::<Value>(&bytes) {
                    Ok(result) => Ok(result.unwrap_or(Value::Null)),
                    Err(err) => Err(RestResult::failed_with_err(err).as_bytes()),
                }
            }
            Err(e) => {
                warn!("SMS authentication failed, {}", e.display_chain());
                Err(RestResult::failed(SYSTEM_ERROR, SYSTEM_ERROR_MESSAGE).as_bytes())
            }
        }
    }

    pub async fn auth_mfa(
        &self,
        tenant: &str,
        buf: Vec<u8>,
        ip: &str,
        env: &Value,
    ) -> Result<Value, Vec<u8>> {
        let url = format!("{}{}{}", self.base_url, self.auth_ctx, SDP_MFA_AUTH);
        match self
            .client
            .post(url)
            .header("TENANT", tenant)
            .header("x-forwarded-for", ip)
            .header("APP-TYPE", "DESKTOP")
            .header("User-Agent", generate_ua(env))
            .body(buf)
            .send()
            .await
        {
            Ok(response) => {
                if response.status().is_server_error() {
                    return Err(RestResult::failed(SYSTEM_ERROR, SYSTEM_ERROR_MESSAGE).as_bytes());
                }
                let bytes = response.bytes().await.unwrap_or_default();

                match parse_result::<Value>(&bytes) {
                    Ok(result) => Ok(result.unwrap_or(Value::Null)),
                    Err(err) => Err(RestResult::failed_with_err(err).as_bytes()),
                }
            }
            Err(e) => {
                warn!("MFA authentication failed, {}", e.display_chain());
                Err(RestResult::failed(SYSTEM_ERROR, SYSTEM_ERROR_MESSAGE).as_bytes())
            }
        }
    }

    /// 异常重连
    pub async fn reauth(
        &self,
        tenant: &str,
        buf: Vec<u8>,
        ip: &str,
        env: &Value,
    ) -> Result<Value, Vec<u8>> {
        let url = format!("{}{}{}", self.base_url, self.auth_ctx, SDP_LINKED_AUTH);
        match self
            .client
            .post(url)
            .header("TENANT", tenant)
            .header("x-forwarded-for", ip)
            .header("APP-TYPE", "DESKTOP")
            .header("User-Agent", generate_ua(env))
            .body(buf)
            .send()
            .await
        {
            Ok(response) => {
                if response.status().is_server_error() {
                    return Err(RestResult::failed(SYSTEM_ERROR, SYSTEM_ERROR_MESSAGE).as_bytes());
                }
                let bytes = response.bytes().await.unwrap_or_default();

                match parse_result::<Value>(&bytes) {
                    Ok(result) => {
                        Ok(result.unwrap_or(Value::Null))
                        // if let Ok(token) = hex::decode::<&str>(&token) {
                        //     return Ok(token);
                        // }
                        // return Err(RestResult::failed(SYSTEM_ERROR,
                        // SYSTEM_ERROR_MESSAGE).buffer());
                    }
                    Err(err) => Err(RestResult::failed_with_err(err).as_bytes()),
                }
            }
            Err(e) => {
                warn!("reauthentication failed, {}", e.display_chain());
                Err(RestResult::failed(SYSTEM_ERROR, SYSTEM_ERROR_MESSAGE).as_bytes())
            }
        }
    }

    /// 通过验证码修改密码
    pub async fn change_pwd_by_verify_code(
        &self,
        tenant: &str,
        buf: &[u8],
        env: &Value,
    ) -> Result<Vec<u8>, (bool, Vec<u8>)> {
        let url = format!(
            "{}{}{}",
            self.base_url, self.auth_ctx, SDP_CHANGE_PWD_BY_VERIFY_CODE
        );

        match self
            .client
            .post(url)
            .header("TENANT", tenant)
            .header("x-forwarded-for", env["ip"].as_str().unwrap())
            .header("User-Agent", generate_ua(env))
            .body(buf.to_vec())
            .send()
            .await
        {
            Ok(response) => {
                if response.status().is_server_error() {
                    return Err((
                        true,
                        RestResult::failed(SYSTEM_ERROR, SYSTEM_ERROR_MESSAGE).as_bytes(),
                    ));
                }
                let bytes = response.bytes().await.unwrap_or_default().to_vec();

                match serde_json::from_slice::<Value>(&bytes) {
                    Ok(result) => {
                        if result["code"].as_str() != Some("SUCCESS") {
                            if let Some(map) = result["data"].as_object() {
                                match map.get("messageKey") {
                                    Some(value) if value.is_string() => {
                                        // 验证码无效
                                        if value.as_str() == Some("PWD.VERIFY.CODE.INVALID") {
                                            return Err((false, bytes));
                                        }
                                    }
                                    _ => {}
                                }
                            }
                            Err((true, bytes))
                        } else {
                            Ok(bytes)
                        }
                    }
                    Err(_) => Err((true, bytes)),
                }
            }
            Err(e) => {
                warn!("password modification failed, {}", e.display_chain());
                Err((
                    true,
                    RestResult::failed(SYSTEM_ERROR, SYSTEM_ERROR_MESSAGE).as_bytes(),
                ))
            }
        }
    }

    /// 获取服务器列表
    pub async fn fetch_resources(&self) -> HashMap<String, HashSet<BackendResource>> {
        let url = format!(
            "{}{}{}",
            self.base_url, self.resource_ctx, SDP_RESOURCE_LIST
        );
        match self.client.get(url).send().await {
            Ok(response) => {
                let bytes = response.bytes().await.unwrap_or_default();
                if bytes.is_empty() {
                    return Default::default();
                }
                match parse_result::<HashMap<String, HashSet<BackendResource>>>(&bytes) {
                    Ok(server_list) => return server_list.unwrap_or_default(),
                    Err(e) => error!("failed parse resources: {}", e),
                }
            }
            Err(e) => warn!("failed to load resources. {}", e.display_chain()),
        };
        Default::default()
    }

    /// 获取服务端状态
    pub async fn get_backend_state(&self) -> Option<Value> {
        let url = format!("{}{}{}", self.base_url, self.auth_ctx, "/health");
        match self.client.get(url).send().await {
            Ok(response) => {
                let bytes = response.bytes().await.unwrap_or_default();
                return match serde_json::from_slice::<Value>(&bytes) {
                    Ok(value) => Some(value),
                    Err(_) => None,
                };
            }
            Err(_) => return None,
        };
    }

    /// 根据用户名获取secret
    pub async fn exchange_secret(&self, tenant: &str, username: &str) -> Option<Vec<u8>> {
        let url = format!(
            "{}{}{}&username={}",
            self.base_url, self.auth_ctx, SDP_EXCHANGE_SECRET, username
        );
        match self.client.get(url).header("TENANT", tenant).send().await {
            Ok(response) => {
                let bytes = response.bytes().await.unwrap_or_default();
                if bytes.is_empty() {
                    info!("secret is empty.");
                    return None;
                }
                match parse_result::<String>(&bytes) {
                    Ok(secret) => {
                        let secret = secret.unwrap();
                        let secret = hex::decode!(secret.as_bytes());
                        return Some(secret);
                    }
                    Err(e) => warn!(username, "err on getting secret: {}", e),
                }
            }
            Err(e) => warn!("Failed to get secret for `{}`, {}", username, e),
        };

        None
    }
}

pub struct BackendClient {
    // 服务端地址
    base_url: String,
    auth_ctx: String,
    mfa_ctx: String,
    client: Client,
}

impl BackendClient {
    pub fn new(tenant: &str, device_id: &str, env: &Value, config: &BackendConfig) -> Self {
        let mut headers = default_headers();
        headers.append("DEVICE-ID", HeaderValue::from_str(device_id).unwrap());
        headers.append("TENANT", HeaderValue::from_str(tenant).unwrap());
        headers.append(
            "x-forwarded-for",
            HeaderValue::from_str(env["ip"].as_str().unwrap()).unwrap(),
        );
        headers.append("APP-TYPE", HeaderValue::from_static("DESKTOP"));
        headers.append(
            "User-Agent",
            HeaderValue::from_str(&generate_ua(env)).unwrap(),
        );

        let client = ClientBuilder::new()
            .user_agent("sdp/1.0")
            .default_headers(headers)
            .timeout(Duration::from_secs(config.timeout))
            .danger_accept_invalid_certs(config.ignore_unknown_ca)
            .danger_accept_invalid_hostnames(config.ignore_invalid_cn)
            .use_rustls_tls()
            .build()
            .expect("failed to build client");

        let mut base_url = config.url.as_str().to_owned();
        if base_url.ends_with("/") {
            base_url = base_url.strip_suffix("/").unwrap().to_owned();
        }

        BackendClient {
            base_url,
            auth_ctx: config.auth_ctx.clone(),
            mfa_ctx: config.mfa_ctx.clone(),
            client,
        }
    }

    pub fn base_url(&self) -> Url {
        Url::parse(&self.base_url).unwrap()
    }

    /// 禁用用户
    pub async fn disable_user(&self, username: &str) {
        let url = format!(
            "{}{}/web/auth/{}/frozen",
            self.base_url, self.auth_ctx, username
        );

        match self.client.get(url).send().await {
            Ok(response) => {
                if !response.status().is_success() {
                    error!(
                        "Failed to disable user `{}`: {}",
                        username,
                        response.status()
                    );
                }
            }
            // 请求失败
            Err(e) => {
                error!("Failed to send disable user message `{}`: {}", username, e);
            }
        };
    }

    /// 退出
    pub async fn logout(&self, username: &str, device_id: &str, socket_addr: &str) {
        let url = format!(
            "{}{}{}&username={}&deviceId={}&socketAddr={}",
            self.base_url, self.auth_ctx, SDP_LOGOUT, username, device_id, socket_addr
        );
        match self.client.get(url).send().await {
            Ok(response) => {
                if !response.status().is_success() {
                    error!("Failed to logout `{}`: {}", username, response.status());
                }
            }
            // 请求失败
            Err(e) => {
                error!("Failed to send logout message `{}`: {}", username, e);
            }
        };
    }

    /// 生成单点登录票据
    pub async fn generate_token(&self, username: &str) -> Vec<u8> {
        let url = format!(
            "{}{}/web/login/{}/ticket",
            self.base_url, self.auth_ctx, username
        );

        match self.client.get(url).send().await {
            Ok(response) => {
                let status = response.status();
                if status.is_server_error() {
                    return RestResult::failed(SYSTEM_ERROR, SYSTEM_ERROR_MESSAGE).as_bytes();
                }

                response.bytes().await.unwrap_or_default().to_vec()
            }
            Err(err) => {
                error!(
                    "Failed to generate web token. username = {}, {}",
                    username,
                    err.display_chain()
                );
                RestResult::failed(SYSTEM_ERROR, SYSTEM_ERROR_MESSAGE).as_bytes()
            }
        }
    }

    /// 刷新登录票据
    pub async fn refresh_ticket(&self, ticket: &str) -> Result<Vec<u8>, Vec<u8>> {
        let url = format!(
            "{}{}{}&ticket={}",
            self.base_url, self.auth_ctx, SDP_LINKED_GET, ticket
        );
        match self.client.get(url).send().await {
            Ok(response) => {
                let status = response.status();

                if status.is_server_error() {
                    return Err(RestResult::failed(SYSTEM_ERROR, SYSTEM_ERROR_MESSAGE).as_bytes());
                };

                let bytes = response.bytes().await.unwrap_or_default();

                let result = bytes.to_vec();
                if status.is_success() {
                    return Ok(result);
                }
                Err(result)
            }
            // 请求失败
            Err(e) => {
                error!("Failed to refresh ticket: {}", e.display_chain());
                Err(RestResult::failed(SYSTEM_ERROR, SYSTEM_ERROR_MESSAGE).as_bytes())
            }
        }
    }

    /// 修改密码
    pub async fn change_pwd(&self, buf: &[u8]) -> Result<Vec<u8>, Vec<u8>> {
        let url = format!(
            "{}{}{}",
            self.base_url, self.auth_ctx, SDP_CHANGE_PWD_BY_OLD
        );

        match self.client.post(&url).body(buf.to_vec()).send().await {
            Ok(response) => {
                let status = response.status();
                if status.is_server_error() {
                    return Err(RestResult::failed(SYSTEM_ERROR, SYSTEM_ERROR_MESSAGE).as_bytes());
                }
                let result = response.bytes().await.unwrap_or_default().to_vec();
                if status.is_success() {
                    return Ok(result);
                }
                Err(result)
            }
            Err(e) => {
                warn!("change pwd failed, {}", e);
                Err(RestResult::failed(SYSTEM_ERROR, SYSTEM_ERROR_MESSAGE).as_bytes())
            }
        }
    }

    /// 询问是否认证成功
    pub async fn ask_auth(&self, auth_result_id: &str) -> Result<Vec<u8>, Vec<u8>> {
        let url = format!(
            "{}{}{}{}",
            self.base_url, self.mfa_ctx, MFA_AUTH_RESULT, auth_result_id
        );
        match self.client.get(url).send().await {
            Ok(response) => {
                let status = response.status();
                if status.is_server_error() {
                    return Err(RestResult::failed(SYSTEM_ERROR, SYSTEM_ERROR_MESSAGE).as_bytes());
                }

                let bytes = response.bytes().await.unwrap_or_default();
                if status.is_success() {
                    Ok(bytes.to_vec())
                } else {
                    Err(bytes.to_vec())
                }
            }
            Err(err) => {
                warn!("Failed to ask auth: {}", err);
                Err(RestResult::failed(SYSTEM_ERROR, SYSTEM_ERROR_MESSAGE).as_bytes())
            }
        }
    }

    /// 查询本地执行策略
    pub async fn client_execution_policy(&self, username: &str) -> Result<Vec<u8>, Vec<u8>> {
        let url = format!(
            "{}{}{}&username={}",
            self.base_url, self.auth_ctx, CLIENT_EXECUTION_POLICY, username
        );
        match self.client.get(url).send().await {
            Ok(response) => {
                let status = response.status();
                if status.is_server_error() {
                    return Err(RestResult::failed(SYSTEM_ERROR, SYSTEM_ERROR_MESSAGE).as_bytes());
                }

                let bytes = response.bytes().await.unwrap_or_default();
                if status.is_success() {
                    Ok(bytes.to_vec())
                } else {
                    Err(bytes.to_vec())
                }
            }
            Err(err) => {
                warn!("Failed to get client policy: {}", err);
                Err(RestResult::failed(SYSTEM_ERROR, SYSTEM_ERROR_MESSAGE).as_bytes())
            }
        }
    }
}

use clap::Parser;
use serde::{Deserialize, Serialize};

use crate::build_info::version_info;

/// SDP Server.
#[derive(Parser, Debug)]
#[clap(name = ABOUT, about = ABOUT, long_about = None, version = version_info())]
pub struct AppArgs {
    #[clap(subcommand)]
    pub command: Command,
}

static ABOUT: &str = "SDP server";

#[derive(Debug, Serialize, Deserialize, Parser, Clone)]
#[serde(tag = "cmd")]
pub enum Command {
    /// 启动服务
    Serve {
        /// Server config file.
        #[clap(long, value_parser)]
        config: String,

        /// Logs dir.
        #[clap(long, value_parser)]
        log_dir: String,

        /// Logs filter.
        #[clap(long, value_parser)]
        filter: String,
    },
    /// 退出
    Quit,
    /// 产看日志级别
    ViewLogFilter,
    /// 设置日志级别
    SetLogFilter {
        /// 日志级别
        #[clap(long)]
        filter: String,
    },
    /// 查看客户端列表
    ViewClients,
    /// 查看溯源映射
    ViewTraceMapping,
}

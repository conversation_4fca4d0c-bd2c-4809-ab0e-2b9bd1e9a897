use once_cell::sync::Lazy;
use std::{
    collections::{HashMap, HashSet},
    net::IpAddr,
    sync::Arc,
};

use rustls::ServerConfig;
use tlv_types::TraceabilityResource;
use tokio::sync::RwLock;

use crate::traceability::utils::cert;

/// https证书缓存
pub static SNI_SERVER_CRYPTO_MAP: Lazy<RwLock<HashMap<String, Arc<ServerConfig>>>> =
    Lazy::new(|| RwLock::new(Default::default()));

/// 溯源资源列表
pub static TRACEABILITY_RESOURCES: Lazy<RwLock<HashSet<TraceabilityResource>>> =
    Lazy::new(|| RwLock::new(Default::default()));

/// 添加一个溯源资源
pub async fn add_resource(resource: TraceabilityResource) {
    log::debug!("Add traceability resource: {:?}", &resource);
    let traceability_resources = TRACEABILITY_RESOURCES.read().await;
    if traceability_resources.contains(&resource) {
        log::warn!("Duplicate application access address");
        return;
    }
    drop(traceability_resources);

    let mut traceability_resources = TRACEABILITY_RESOURCES.write().await;
    traceability_resources.insert(resource.clone());
    drop(traceability_resources);
    if resource.url.scheme() != "https" {
        return;
    }

    let Some(host) = resource.url.host_str() else {
        log::warn!("no host: {}", resource.url);
        return;
    };

    let sni_map = SNI_SERVER_CRYPTO_MAP.read().await;
    if sni_map.contains_key(host) {
        log::warn!("A certificate with host `{host}` already exists");
        return;
    }
    drop(sni_map);

    let Some(server_config) = cert::generate(host) else {
        return;
    };

    log::debug!("Host `{host}` certificate generated successfully");
    let mut sni_map = SNI_SERVER_CRYPTO_MAP.write().await;
    sni_map.insert(host.to_owned(), server_config);
}

/// 删除资源
pub async fn remove_resource(resource: TraceabilityResource) {
    log::debug!("Del traceability resource: {:?}", &resource);
    let mut traceability_resources = TRACEABILITY_RESOURCES.write().await;
    traceability_resources.remove(&resource);
}

/// 获取访问地址的scheme
pub async fn get_http_scheme(ip: IpAddr, port: u16) -> Option<&'static str> {
    let traceability_resources = TRACEABILITY_RESOURCES.read().await;
    let Some(Some(scheme)) = traceability_resources
        .iter()
        .find(|resource: &&TraceabilityResource| {
            let Some(p) = resource.url.port_or_known_default() else {
                return false;
            };

            if p != port {
                return false;
            }

            resource.ips.contains(&ip)
        })
        .map(|resource| {
            let scheme = resource.url.scheme();
            let scheme = match scheme {
                "http" => "http",
                "https" => "https",
                _ => return None,
            };
            Some(scheme)
        })
    else {
        return None;
    };
    Some(scheme)
}

pub(super) async fn find_tls_config(
    server_name: Option<&str>,
    ip: IpAddr,
    port: u16,
) -> Option<Arc<ServerConfig>> {
    let sni_map = SNI_SERVER_CRYPTO_MAP.read().await;
    if let Some(server_name) = server_name {
        return sni_map.get(server_name).cloned();
    }

    let traceability_resources = TRACEABILITY_RESOURCES.read().await;
    let Some(Some(host)) = traceability_resources
        .iter()
        .find(|resource| {
            let Some(p) = resource.url.port_or_known_default() else {
                return false;
            };

            if p != port {
                return false;
            }

            resource.ips.contains(&ip)
        })
        .map(|resource| {
            let Some(host) = resource.url.host_str() else {
                return None;
            };
            Some(host.to_owned())
        })
    else {
        return None;
    };

    sni_map.get(&host).cloned()
}

# Changelog

All changes to the software that can be noticed from the users' perspective should have an entry in
this file. Except very minor things that will not affect functionality, such as log message changes
and minor GUI adjustments.

### Format

The format is based on [Keep a Changelog](http://keepachangelog.com/en/1.0.0/).

Entries should have the imperative form, just like commit messages. Start each entry with words like
add, fix, increase, force etc.. Not added, fixed, increased, forced etc.

Line wrap the file at 100 chars. That is over here -> |

### Categories each change fall into

- **Added**: for new features.
- **Changed**: for changes in existing functionality.
- **Deprecated**: for soon-to-be removed features.
- **Removed**: for now removed features.
- **Fixed**: for any bug fixes.
- **Security**: in case of vulnerabilities.

## [8.4.4] - 2025-07-03

### Fixed

#### Client

- 启动时等待隧道创建成功

## [8.4.3] - 2025-06-24

### Added

#### Server

- 运行时查看或修改程序状态
- 支持伪 IP 访问
- 优雅退出
- 自主下线其他在线终端通知

#### Client

- 可执行文件输出详细版本信息
- 新增支持 Linux
- 环境信息增加设备厂商
- 支持访问伪 IP
- 内置 DNS 服务(默认未开启)
- 溯源支持 websocket 协议
- Windows 客户端安装时, 杀毒软件拦截操作提示
- 新增信任设备
- SDP 支持显示资源图标
- 优雅关闭 oneidcore, 同时删除 `EventListener`
- 父进程退出后, CORE 进程也退出
- 自主下线其他在线终端通知

### Changed

#### Server

- 使用 tun-rs 替换 tunio

#### Client

- 使用 actix-web 替换 poem
- 使用 rcgen 替换 GO 的证书生成工具
- 默认不开启溯源功能
- 禁用 HTTP 系统代理
- 使用 tun-rs 替换 tunio
- 隧道仅发送资源访问相关流量
- 首页不在重复询问是否有更新, 优化首页加载速度

### Fixed

#### Server

- 单机时, 访问管理平台很慢
- SPA 端口无法动态设置
- 无效网络数据包导致的崩溃
- SPA 验证慢, 导致客户端连接异常

#### Client

- IP 段转 CIDR 失败导致的崩溃
- 启动页没有地址时报错
- 正确识别 Windows 软件运行状态
- Windows Defender 策略设置错误, 导致溯源不可用
- 新策略若不包含之前使用的方式时, 登录页面空白
- 溯源应用 Host 请求头设置错误, 导致无法访问
- 节点不可用时, 页面一直处于加载状态
- 多因子取消失败, 导致后续认证请求未弹出
- 空指针导致白屏
- Windows 服务正确响应停止请求
- win7 虚拟网卡安装失败时, 自动将 wintun.sys 放到系统中

### Removed

- 不再支持混合模式

### Security

#### Client

- 日志会暴露用户敏感信息

## [8.4.0] - 2024-10-31

**不兼容 8.2.1**

### Fixed

#### Server

- 内核参数错误

## [8.3.0] - 2024-09-07

**不兼容 8.2.1**

### Added

#### Client

- 溯源支持虚拟网卡
- 使用服务端配置的公钥进行数据保护
- 集群模式下, 网络变更后, 重新加载节点列表

#### Server

- 支持 VIP 访问

### Changed

#### Client

- 更新版权信息
- 集群模式支持内外网配置不同的地址
- 内置多套证书根据域名进行选择
- 重连时, 不弹出主页
- 定时加载认证策略

#### Server

- 认证包必须在认证 SPA 下才有效

### Fixed

#### Client

- 退出时关闭 core
- MacOS 按钮文字居中
- 集群模式编译错误
- 卸载时, 删除桌面快捷方式

## [8.2.1] - 2024-08-02

**不兼容之前版本**

### Fixed

- 联动登录

## [8.2.0] - 2024-06-07

### Added

#### Client

- 客户端可单点管理平台

### Fixed

#### Client

- 客户端型号设置错误
- 只有一个单位时, 不显示单位选择
- 更新包下载完成后, 停滞在当前也

#### Server

- 索引越界
- 默认网关地址设置错误, 导致数据包转发失败

### Changed

#### Client

- 重构登录 UI

## [8.1.3] - 2024-05-20

### Fixed

#### Client

- 客户端 LOGO 更新
- 优化我的应用分组样式
- 获取 ticket 失败后自动重试
- 密码过期跳转修改密码

### Changed

- 安装时本地配置与内置不匹配时, 覆盖本地配置

#### Tools

- MacOS 新的公证流程

## [8.1.2] - 2024-04-24

### Fixed

#### Client

- 增加遗失的面容 ID 登录方式
- MacOS 动态分配网卡名称
- IAM 会话过期自动重新登录
- 单点登录样式更新

## [8.1.1] - 2024-04-24

### Fixed

#### Client

- 资源分组名称显示完整

#### Tools

- Windows 更新包 zip 压缩算法与 tauri 保持一致

## [8.1.0] - 2024-04-22

### Added

#### Client

- Windows 面部识别
- Core dump

#### Server

- 支持多网卡

### Fixed

#### Client

- 溯源 302 处理
- SDP 资源接口配置错误

#### Tools

- MacOS DMG 镜像

## [8.0.0-beta.3] - 2024-03-01

### Changed

#### Updater

- 支持双因素等其他产品

#### 性能优化

- 使用 coarsetime 替换 chrono
- 使用 once-cell 替换 lazy_static
- 使用 gm-rs 替换 libsm

### Fixed

#### Client

- MFA 登录方式鼠标移入样式
- 恢复出厂设置后用户信息还存在
- 双因素联动登录时, 用户信息没有更新
- 应用 MFA 策略不通过时, 提示禁止操作
- 双因素联动登录时同步更新单位信息

## [8.0.0-beta.2] - 2024-02-08

### Changed

#### Client

- 重构登录页布局

## [8.0.0-beta.1] - 2024-02-06

### Fixed

#### Client

- 访问没有配置单点登录和访问地址的应用给出提示
- 确认按钮鼠标移入会撑大弹窗

## [8.0.0-beta.0] - 2024-02-06

### Add

#### Client

- 客户端支持集群
- 溯源请求头中增加设备 ID(DEVICE-ID)
- 增加客户端打包工具以适配输出多个产品的客户端
- 自动更新支持进度条
- 溯源支持 HTTP/2

### Changed

- Windows 客户端签名证书
- MacOS 菜单图标修改
- 客户端退出登录时关闭定时任务(环境扫描, 刷新重连 Token)
- 重构客户端前端
- 适配 8.0

### Fixed

#### Client

- Windows 任务栏右键无法关闭窗口
- 虚拟网卡异常退出时, 自动重新创建
- 同一 IP 加端口, 域名不同时只能访问其中一个
- 环境信息变化时, 只上传变化的数据
- 用户不存在时, 同一提示用户名或密码错误
- 租户或策略加载失败显示重试按钮
- 左侧菜单兼容性增强
- 溯源对象为 HTTPS 时, 访问资源程序崩溃
- MacOS 联动登录
- UI 兼容 Windows
- 登录后提示会话过期
- Windows 快捷方式图标不会跟随放大

#### Server

- 代理 form 表单上传
- rabbitmq 自动重连
- 核心数与网卡队列数不匹配时导致程序无法启动
- 网关卡死

## [7.4.6] - 2023-10-17

### Fixed

#### Client

- 有默认地址时, 不再提示控制器地址为空
- 虚拟网卡异常退出重建时, 提示缺少参数
- 同一 IP 端口不同域名时, 只能访问其中一个
- 溯源请求不使用系统代理
- 溯源请求头使用覆盖替换追加操作
- Windows 任务栏关闭窗口异常

#### Server

- 认证成功后, 丢弃代理中的接口, 不再等待接口响应

## [7.4.5] - 2023-10-10

### Fixed

#### Client

- 联动登录

## [7.4.4] - 2023-10-09

### Fixed

#### Server

- 代理增加客户端信息

#### Client

- 升级 7.4.3 后一直提示控制器地址为空
- moka 升级到 0.12 后不会定期检查缓存过期
- 首页一直在加载中
- 首次修改密码后提示网络错误

## [7.4.3] - 2023-10-08

### Add

- 溯源

#### Server

- 监控信息

### Changed

- 将 TLCP 设为默认协议
- 通信消息格式变更为 TLV, 增强兼容性
- 客户端不直接与管理服务交互
- SPA 根据请求类型分为三种: 认证(0), 修改密码(1), 代理(2)

### Removed

#### Controller

- 客户端环境校验策略

#### Gateway

- 网关准入不再依赖控制器认证完成

### Fixed

- 独立网关认证成功后没有响应消息

## [7.4.2] - 2023-09-07

### Add

- 资源支持 IP 段

### Fixed

#### Server

- NAT 时, 排除本机开放的端口(若源端口为本机开放端口, 流量无法回到客户端)

## [7.4.0] - 2023-08-23

### Added

#### Server

- 资源白名单

### Changed

#### Client

- UI 调整为横板

### Deprecated

#### Server

- 不再支持 redbpf 版本

### Fixed

- 大量 SPA 阻塞导致服务不可用

## [7.3.1] - 2023-07-21

### Added

#### Client

- 通过环境变量设置生产包日志等级

### Changed

#### Client

- 调试日志隐藏密码

#### Server

- 通过接口获取客户端本地执行策略

### Fixed

#### Client

- 窗口不在主页时多因子认证无法弹出
- 配置中不存在用户信息时无法登录
- 覆盖安装时无法覆盖 oneidservice
- Windows Server 没有边框
- 控制器网关分离时, 没有使用配置的 IP 网关

## [7.3.0] - 2023-07-12

### Added

- Support TLCP.
- Force update.

#### Server

- Whitelist added port, proxy backend service.
- Mixed mode supports opening native ports.
- Enable `TCP_NODELAY` for the socket. Improves latency and performance.

### Changed

#### Server

- Merge controller and gateway.
- Modify the return type of the `server_list` interface to a string.

#### Client

- Update the binding code acquisition process.
- Update tauri version to v1.4.
- Use NSIS to replace Wix.
- Modify the MacOS installation process.
- Windows routing metric set to 1.

## [7.2.3] - 2023-06-21

### Added

- Add ukey login type.

## [7.2.2] - 2023-05-23

### Changed

- Refactor reconnect mechanism.

## [7.2.1] - 2023-05-11

### Added

- Attempt to start IPv6 compatibility.
- DNS is optional.

### Changed

- Refactor client structure, merge Windows and MacOS code.

## [1.4.0] - 2023-04-20

### Added

#### macOS

- Supported macOS.

### Changed

- Split the program into two processes to better manage permissions.

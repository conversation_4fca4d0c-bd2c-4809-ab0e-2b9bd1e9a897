<template>
  <div
    v-loading.fullscreen.lock="loading"
    class="pwdArea"
    :class="connectState.state && 'pswbox'"
    element-loading-text="登录中..."
    element-loading-background="rgba(0, 0, 0, 0.5)"
  >
    <!-- 账号 -->
    <div id="pwdBox" v-if="type">
      <div class="inputItem" :style="connectState.state && 'margin-top: 0;'">
        <div class="inputContent">
          <el-input ref="username" v-model="user.username" placeholder="请输入用户名" />
        </div>
      </div>
      <!-- 密码 -->
      <div class="inputItem" :style="connectState.state && 'margin-top: 0;'">
        <div class="inputContent">
          <el-input
            ref="password"
            v-model="user.password"
            placeholder="请输入密码"
            class="input-pwd"
            show-password
          />
        </div>
      </div>
      <div class="login-form-bottom">
        <el-checkbox v-model="user.remember_me" label="记住密码" @change="changeRemember" />
        <el-checkbox v-model="user.auto_connect" label="自动登录" @change="changeAutoConnect" />
        <el-link class="login-bottom-item" :underline="false" @click="openForgetPwdDialog">
          忘记密码?
        </el-link>
      </div>

      <!-- 登录 -->
      <div>
        <el-button
          ref="confirmBtn"
          type="primary"
          class="loginBtn"
          :style="connectState.state ? 'margin: 7px 0 0 0;' : 'margin: 20px 0 5px;'"
          :disabled="disableLoginButton"
          @click="login()"
        >
          登录
        </el-button>
        <userAgreement />
      </div>
    </div>

    <div class="mfa-content" v-else>
      <div style="margin-top: 61px; padding-bottom: 61px">
        <div class="auth-code-content border">
          <el-input
            ref="password"
            v-model="user.password"
            placeholder="请输入密码"
            class="auth-input"
            style="padding-right: 10px;"
            show-password
          />
        </div>
        <el-button ref="confirmBtn" type="primary" class="mfa-confirm-btn" @click="login()">登录</el-button>
      </div>
    </div>
    <forget-pwd
      ref="forgetPwd"
      @successCallback="forgetPwdCallback"
      :show="showForgetPwdDialog"
      @close="closeForgetPwdDialog"
    />
  </div>
</template>

<script>
import {passwordVerify, getPublicKey} from '../../api/service.js'
import {sm2} from 'sm-crypto'
import {Base64} from 'js-base64'
import {useStore} from 'vuex'
import {computed} from 'vue'
import userAgreement from '../../views/userAgreement.vue'
import forgetPwd from '../../views/forgetPwd.vue'

export default {
  components: {
    userAgreement,
    forgetPwd,
  },
  props: {
    participantGroupId: {
      type: String,
      default: '',
    },
    userObj: {
      type: Object,
      default: () => {},
    },
    tenant: {
      type: Array,
      default: () => {},
    },
    disableLoginButton: {
      type: Boolean,
      default: false,
    },
    // 显示忘记密码, 透传到forgetPwd组件中
    showForgetPwdDialog: {
      type: Boolean,
      default: false,
    },
    type: {
      type: String,
      default: '',
    },
    name: {
      type: String,
      default: '',
    },
  },
  setup() {
    const store = useStore()
    store.commit('updateIsLogin', false)
    return {
      connectState: computed(() => store.getters.connectState),
    }
  },
  inject: ['reload'],
  data() {
    return {
      loading: false,
      //节流
      throttle: false,
      publicKey: CONFIG.secretKey ? CONFIG.secretKey : null,
      user: this.name ? {username: this.name} : this.userObj
    }
  },
  created() {
    this.PublicKeyInit()
  },
  methods: {
    login() {
      if (this.throttle) return
      this.throttle = true
      this.doLogin()
      setTimeout(() => {
        this.throttle = false
      }, 1000)
    },
    // 登录
    doLogin() {
      if (!this.user.username) {
        this.alert.error('请输入用户名')
        return
      }
      if (!this.user.password) {
        this.alert.error('请输入密码')
        return
      }
      this.loading = true
      this.$emit('loading')
      let public_key =
        '04645629ffb19130f9a8a5338c64148ee819fd273d9b69b5aba97ae25a9f917d10549c62e81c1c87a91067174fb1603dfa9113e526c85cded7a6fdd9c6bab9c5c5'
      // 加密密钥处理
      if (this.publicKey && this.publicKey.dataTransmitType == 1) {
        public_key = this.publicKey.publicKeyStr
      }
      let passwordd = '04' + sm2.doEncrypt(Base64.encode(this.user.password), public_key, 0)
      let param = {
        participantKeyword: this.user.username,
        participantTypes: '03',
        participantGroupId: this.participantGroupId,
        password: passwordd,
      }

      passwordVerify(param)
        .then(() => {
          this.$emit('mfaCallbackFunc', {type: 'qrcode'}, this.user)
        })
        .catch(error => {
          this.$emit('generateRequestIdFailCallbackFunc', error)
          this.loading = false
        })
    },
    // 记住密码
    changeRemember() {
      this.user.remember_me ? '' : (this.user.auto_connect = false)
    },
    // 自动登录
    changeAutoConnect() {
      this.user.remember_me = true
    },
    openForgetPwdDialog() {
      this.$emit('openForgetPwdDialog')
      this.$refs.forgetPwd.init()
    },
    closeForgetPwdDialog() {
      this.$emit('closeForgetPwdDialog')
    },
    forgetPwdCallback(successMsg) {
      this.$emit('forgetPwdCallback', successMsg)
    },
    // // 动态加密
    PublicKeyInit() {
      getPublicKey().then(res => {
        let data = res.data
        if (data.dataTransmitType == 1) {
          this.publicKeyStr = data.publicKeyStr
        } else {
          this.publicKeyStr = ''
        }
      })
    },
  },
}
</script>

<style lang="less" scoped>
@mainColor: #f9780c;

.pswbox {
  margin-top: 35px;
}

#pwdBox {
  :deep(.el-input__wrapper) {
    box-shadow: none;
    padding: 1px;
  }
}

.inputItem {
  text-indent: 0.6em;
  position: relative;
  margin-top: 2rem;
  // border-radius: 30px;
  border-bottom: 1px solid #d1d3db;
  margin-bottom: 10px;
  &:hover {
    border-bottom: 1px solid #f9780c;
  }

  .clud {
    display: flex;
    align-items: center;

    span {
      // width: 40px;
      cursor: pointer;
      color: @mainColor;
      text-align: right;
      font-size: 14px;
    }
  }
}

.login_icon {
  color: #bfbfbf;
  font-size: 22px;
}

.inputContent {
  padding: 0.4rem;
  width: 240px;
}

.input-pwd {
  width: 300px;
}

.login-form-bottom {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 10px 0 0 3px;

  :deep(.el-checkbox) {
    margin-right: 0;
  }
}

.login-bottom-item {
  // vertical-align: middle;
  margin-bottom: 2px;
  font-size: 14px;
  color: @mainColor;
}

.mfa-content {
  .auth-code-content {
    width: 100%;
    margin: auto;
    text-align: center;
    position: relative;
  }

  :deep(.el-input__wrapper) {
    border: 0 !important;
    padding: 1px 1px;
    border-radius: 20px;
  }
}
</style>

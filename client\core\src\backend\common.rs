use std::collections::HashSet;

use base::packet::MessageType;
use futures::{channel::oneshot, SinkExt};
use ipnet::IpNet;
use serde_json::Value;
use tlv_types::{
    AccessResource, IncrResourceList, MfaCompleteAuth, ProxyFail, ProxyResponse, ResourceList,
    ResponseChunk, TraceabilityResources, TraceabilityResourcesVirtualIps, Whitelist,
};

use crate::{
    mpsc::Sender,
    proxy_request::{ProxyHttpResult, REQUESTS},
    tunnel_state_machine::{ResourceEvent, TunnelCommand},
    CoreEventSender, EventListener, InternalCoreEvent,
};

#[cfg(feature = "traceability")]
use crate::traceability::TRACEABILITY_RESOURCES_VIRTUAL_IP_MAPPING;

pub(crate) async fn handle_whitelist_resources(
    core_tx: CoreEventSender,
    input: untrusted::Input<'_>,
) {
    let result: Result<Whitelist, &'static str> = tlv::read_to_struct!(input);
    let Ok(resources) = result else {
        log::trace!("{:?}", input.as_slice_less_safe());
        log::error!("bad message. type = {:?}", MessageType::ResourceWhitelist);
        return;
    };

    log::debug!("Received whitelist resources: {:?}", &resources);

    let Some(resources) = resources.ips else {
        return;
    };

    let resources: HashSet<IpNet> = resources.into_iter().map(IpNet::from).collect();
    _ = core_tx.send(InternalCoreEvent::TunnelCommand(TunnelCommand::Resource(
        ResourceEvent::AddResources(resources),
    )));
}

pub(crate) async fn handle_server_list(core_tx: CoreEventSender, input: untrusted::Input<'_>) {
    let result: Result<ResourceList, &'static str> = tlv::read_to_struct!(input);
    let Ok(resources) = result else {
        log::trace!("{:?}", input.as_slice_less_safe());
        log::error!("bad message. type = {:?}", MessageType::ServerList);
        return;
    };

    log::debug!("Received resources: {:?}", &resources);

    let Some(resources) = resources.resources else {
        return;
    };

    let mut ipnets = HashSet::new();
    for resource in resources {
        let ipnet_list: Vec<IpNet> = resource.into();
        ipnets.extend(ipnet_list.into_iter());
    }

    _ = core_tx.send(InternalCoreEvent::TunnelCommand(TunnelCommand::Resource(
        ResourceEvent::AddResources(ipnets),
    )));
}

pub(crate) async fn handle_increment_server_list(
    core_tx: CoreEventSender,
    input: untrusted::Input<'_>,
) {
    let result: Result<IncrResourceList, &'static str> = tlv::read_to_struct!(input);
    let Ok(resources) = result else {
        log::error!("bad message. type = {:?}", MessageType::IncrementalServer);
        return;
    };

    log::debug!(
        "Received {} resources: {:?}",
        if resources.flag { "added" } else { "deleted" },
        &resources
    );

    let flag = resources.flag;
    let Some(resources) = resources.resources else {
        return;
    };

    let mut ipnets = HashSet::new();
    for resource in resources {
        let ipnet_list: Vec<IpNet> = resource.into();
        ipnets.extend(ipnet_list.into_iter());
    }

    // 异步处理路由
    if flag {
        _ = core_tx.send(InternalCoreEvent::TunnelCommand(TunnelCommand::Resource(
            ResourceEvent::AddResources(ipnets),
        )));
    } else {
        _ = core_tx.send(InternalCoreEvent::TunnelCommand(TunnelCommand::Resource(
            ResourceEvent::DeleteResource(ipnets),
        )));
    }
}

pub(crate) async fn handle_client_strategy(
    core_tx: CoreEventSender,
    input: untrusted::Input<'_>,
) -> Option<i8> {
    let result: Result<Vec<u8>, &'static str> = tlv::read_to_struct!(input);
    let Ok(bytes) = result else {
        log::error!("bad message. type = {:?}", MessageType::DeliveryStrategy);
        return None;
    };
    let conditions = if bytes.is_empty() {
        None
    } else {
        serde_json::from_slice(&bytes)
            .map_err(|error| log::error!("Failed to deserialize message {error}"))
            .ok()
    };
    log::debug!("Received local strategy: {:?}", &conditions);
    if core_tx
        .send(InternalCoreEvent::Strategy(conditions))
        .is_err()
    {
        log::error!("Core already down or thread panicked");
        return Some(-3);
    }

    None
}

/// 通知消息
pub(crate) async fn handle_notification(core_tx: CoreEventSender, input: untrusted::Input<'_>) {
    let result: Result<u8, &'static str> = tlv::read_to_struct!(input);
    let Ok(message_type) = result else {
        log::error!("bad message. type = {:?}", MessageType::Notification);
        return;
    };

    if core_tx
        .send(InternalCoreEvent::Notification(message_type))
        .is_err()
    {
        log::error!("Core already down or thread panicked");
    }
}

pub(crate) async fn handle_refresh_token(
    core_tx: CoreEventSender,
    input: untrusted::Input<'_>,
) -> Option<i8> {
    let result: Result<Vec<u8>, &'static str> = tlv::read_to_struct!(input);
    let Ok(bytes) = result else {
        log::error!("bad message. type = {:?}", MessageType::RefreshToken);
        return None;
    };
    if let Ok(payload) = serde_json::from_slice::<Value>(&bytes) {
        let data = &payload["data"];
        log::debug!("Received reconnect token");
        if let Some(expire) = data["expire"].as_u64() {
            if core_tx
                .send(InternalCoreEvent::ReconnectToken(
                    data["ticket"].as_str().unwrap_or_default().to_string(),
                    expire,
                ))
                .is_err()
            {
                log::error!("Core already down or thread panicked");
                return Some(-3);
            }
        }
    }
    None
}
pub(crate) async fn handle_change_pwd_result<L: EventListener>(
    event_listener: L,
    input: untrusted::Input<'_>,
) -> Option<i8> {
    let result: Result<Vec<u8>, &'static str> = tlv::read_to_struct!(input);
    let Ok(bytes) = result else {
        log::error!("bad message. type = {:?}", MessageType::ChangePwdResult);
        return None;
    };

    event_listener.notify_change_pwd_result(&bytes);
    if let Ok(result) = serde_json::from_slice::<Value>(&bytes) {
        if result["code"].as_str() == Some("SUCCESS") {
            log::debug!("Password changed");
            return Some(-2);
        }
    }
    log::debug!("Password change failed");
    None
}

pub(crate) async fn handle_require_mfa<L: EventListener>(
    core_tx: CoreEventSender,
    event_listener: L,
    input: untrusted::Input<'_>,
) -> Option<i8> {
    let result: Result<AccessResource, &'static str> = tlv::read_to_struct!(input);
    let Ok(resource) = result else {
        log::error!("bad message. type = {:?}", MessageType::RequiredMfa);
        return None;
    };

    let command =
        TunnelCommand::Resource(ResourceEvent::AddBlock(Some(IpNet::from(resource.ip()))));
    if core_tx
        .send(InternalCoreEvent::TunnelCommand(command))
        .is_err()
    {
        log::error!("Core already down or thread panicked");
        return Some(-1);
    }
    event_listener.notify_need_auth();
    None
}

/// 取消认证或认证完成后的处理
pub(crate) async fn cancel_or_complete_auth_post_process(
    event_type: MessageType,
    input: untrusted::Input<'_>,
    core_tx: CoreEventSender,
    callback_tx: oneshot::Sender<bool>,
) -> Option<i8> {
    match event_type {
        MessageType::MfaAuthCompleted => {
            let result: Result<MfaCompleteAuth, &'static str> = tlv::read_to_struct!(input);
            let Ok(event) = result else {
                log::error!("bad message. type = {:?}", MessageType::MfaAuthCompleted);
                return None;
            };

            let command = TunnelCommand::Resource(ResourceEvent::DeleteBlock(
                Some(IpNet::from(event.resource.ip())),
                callback_tx,
            ));
            if core_tx
                .send(InternalCoreEvent::TunnelCommand(command))
                .is_err()
            {
                log::error!("Core already down or thread panicked");
                return Some(-1);
            }
        }
        MessageType::CancelMfaAuth => {
            let result: Result<AccessResource, &'static str> = tlv::read_to_struct!(input);
            let Ok(resource) = result else {
                log::error!("bad message. type = {:?}", MessageType::CancelMfaAuth);
                return None;
            };

            let command = TunnelCommand::Resource(ResourceEvent::DeleteBlock(
                Some(IpNet::from(resource.ip())),
                callback_tx,
            ));
            if core_tx
                .send(InternalCoreEvent::TunnelCommand(command))
                .is_err()
            {
                log::error!("Core already down or thread panicked");
                return Some(-1);
            }
        }
        _ => {}
    }
    None
}

/// 处理代理响应
pub async fn handle_proxy_response(input: untrusted::Input<'_>) -> Result<(), ()> {
    let result: Result<ProxyResponse, &'static str> = tlv::read_to_struct!(input);
    let Ok(response) = result else {
        log::error!("bad message. type = {:?}", MessageType::ProxyResponse);
        return Err(());
    };

    match if response.is_chunk {
        REQUESTS.get(&response.seq).await
    } else {
        REQUESTS.remove(&response.seq).await
    } {
        Some((mut callback_tx, _)) => {
            if callback_tx
                .send(ProxyHttpResult::Ok(response.data, response.is_chunk))
                .await
                .is_err()
            {
                log::warn!("response timeout. seq = {}", response.seq);
            }
        }
        None => {
            log::warn!("response timeout. seq = {}", response.seq);
        }
    }
    Ok(())
}

/// 处理代理响应chunk数据包
pub async fn handle_proxy_response_chunk(input: untrusted::Input<'_>) -> Result<(), ()> {
    let result: Result<ResponseChunk, &'static str> = tlv::read_to_struct!(input);
    let Ok(chunk) = result else {
        log::error!("bad message. type = {:?}", MessageType::ProxyResponseChunk);
        return Err(());
    };

    // chunk数据包发送完
    if chunk.data.is_empty() {
        if let Some((_, mut chunk_tx)) = REQUESTS.remove(&chunk.seq).await {
            chunk_tx.close_channel();
        }
    } else {
        match REQUESTS.get(&chunk.seq).await {
            Some((_, mut chunk_tx)) => {
                if chunk_tx.send(chunk.data).await.is_err() {
                    log::warn!("response timeout. seq = {}", chunk.seq);
                }
            }
            None => {
                log::warn!("response timeout. seq = {}", chunk.seq);
            }
        }
    }
    Ok(())
}

pub async fn handle_proxy_fail(input: untrusted::Input<'_>) -> Result<(), ()> {
    let result: Result<ProxyFail, &'static str> = tlv::read_to_struct!(input);
    let Ok(fail) = result else {
        log::error!("bad message. type = {:?}", MessageType::ProxyFail);
        return Err(());
    };

    match REQUESTS.remove(&fail.seq).await {
        Some((mut callback_tx, _)) => {
            if callback_tx
                .send(ProxyHttpResult::Fail(fail.message))
                .await
                .is_err()
            {
                log::warn!("response timeout. seq = {}", fail.seq);
            }
        }
        None => {
            log::warn!("response timeout. seq = {}", fail.seq);
        }
    }
    Ok(())
}

/// 接收溯源资源数据包
pub async fn handle_traceability_resources(
    #[cfg(feature = "dns_server")] core_tx: CoreEventSender,
    input: untrusted::Input<'_>,
) -> Result<(), ()> {
    let result: Result<TraceabilityResources, &'static str> = tlv::read_to_struct!(input);
    #[cfg_attr(
        not(all(feature = "traceability", feature = "dns_server")),
        allow(unused_variables)
    )]
    let Ok(traceability_resources) = result
    else {
        log::error!(
            "bad message. type = {:?}",
            MessageType::TraceabilityResources
        );
        return Err(());
    };

    #[cfg(any(feature = "traceability", feature = "dns_server"))]
    for traceability_resource in traceability_resources.resources {
        if traceability_resources.flag {
            // 添加DNS记录
            #[cfg(feature = "dns_server")]
            {
                if let Some(domain) = traceability_resource.url.domain() {
                    if let Some(ip) = traceability_resource.only_one_ip() {
                        _ = core_tx.send(InternalCoreEvent::DnsRecord(domain.to_owned(), ip));
                    }
                }
            }

            // 溯源
            #[cfg(feature = "traceability")]
            crate::traceability::add_resource(traceability_resource).await;
        } else {
            // 删除DNS记录
            #[cfg(feature = "dns_server")]
            {
                if let Some(domain) = traceability_resource.url.domain() {
                    _ = core_tx.send(InternalCoreEvent::RemoveDnsRecord(domain.to_owned()));
                }
            }

            #[cfg(feature = "traceability")]
            crate::traceability::remove_resource(traceability_resource).await;
        }
    }
    Ok(())
}

/// 处理溯源资源虚拟IP地址
pub async fn handle_traceability_resources_virtual_ips(
    core_tx: CoreEventSender,
    input: untrusted::Input<'_>,
) {
    let result: Result<TraceabilityResourcesVirtualIps, &'static str> = tlv::read_to_struct!(input);
    let Ok(virtual_ips) = result else {
        log::error!(
            "bad message. type = {:?}",
            MessageType::TraceabilityResourcesVirtualIps
        );
        return;
    };

    log::debug!("Received virtual mapping: {:?}", &virtual_ips.mapping);

    let virtual_resources: HashSet<IpNet> = virtual_ips
        .mapping
        .iter()
        .map(|map| IpNet::from(map.virtual_ip))
        .collect();

    // 异步处理路由
    if virtual_ips.flag {
        #[cfg(feature = "traceability")]
        for map in virtual_ips.mapping.iter() {
            TRACEABILITY_RESOURCES_VIRTUAL_IP_MAPPING
                .insert(map.real, map.virtual_ip)
                .await;
        }
        _ = core_tx.send(InternalCoreEvent::TunnelCommand(TunnelCommand::Resource(
            ResourceEvent::AddResources(virtual_resources),
        )));
    } else {
        #[cfg(feature = "traceability")]
        for map in virtual_ips.mapping.iter() {
            TRACEABILITY_RESOURCES_VIRTUAL_IP_MAPPING
                .remove(&map.real)
                .await;
        }
        _ = core_tx.send(InternalCoreEvent::TunnelCommand(TunnelCommand::Resource(
            ResourceEvent::DeleteResource(virtual_resources),
        )));
    }
}

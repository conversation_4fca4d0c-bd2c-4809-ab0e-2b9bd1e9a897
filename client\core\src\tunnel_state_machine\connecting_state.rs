use futures::{channel::mpsc, StreamExt};

use err_ext::ErrorExt;
use types::tunnel::{ErrorStateCause, TunnelStateTransition};

use crate::{
    tunnel::TunTunnel,
    tunnel_state_machine::{
        connected_state::{ConnectedState, ConnectedStateBootstrap, TunnelEventsReceiver},
        disconnected_state::DisconnectedState,
        TunnelArgs, TunnelCommand, TunnelEvent,
    },
};

use super::{
    error_state::ErrorState, EventConsequence, EventResult, SharedTunnelStateValues,
    TunnelCommandReceiver, TunnelMetadata, TunnelState, TunnelStateWrapper,
};

pub struct ConnectingState {
    tunnel: TunTunnel,
    tunnel_events: TunnelEventsReceiver,
    tunnel_metadata: Option<TunnelMetadata>,
}

impl ConnectingState {
    fn into_connected_state_bootstrap(self, metadata: TunnelMetadata) -> ConnectedStateBootstrap {
        ConnectedStateBootstrap {
            tunnel: self.tunnel,
            metadata,
            tunnel_events: self.tunnel_events,
        }
    }

    async fn reset_routes(shared_values: &mut SharedTunnelStateValues) {
        if let Err(error) = shared_values.route_manager.clear_routes() {
            log::error!("{}", error.display_chain_with_msg("Failed to clear routes"));
        }
        #[cfg(target_os = "linux")]
        if let Err(error) = shared_values.route_manager.clear_routing_rules().await {
            log::error!(
                "{}",
                error.display_chain_with_msg("Failed to clear routing rules")
            );
        }
    }

    async fn disconnect(self, shared_values: &mut SharedTunnelStateValues) -> EventConsequence {
        Self::reset_routes(shared_values).await;

        EventConsequence::NewState(DisconnectedState::enter(shared_values, ()).await)
    }

    async fn handle_commands(
        self,
        command: Option<TunnelCommand>,
        shared_values: &mut SharedTunnelStateValues,
    ) -> EventConsequence {
        use self::EventConsequence::*;

        match command {
            Some(TunnelCommand::Dns(servers)) => {
                _ = shared_values.set_dns_servers(servers);
                SameState(self.into())
            }
            Some(TunnelCommand::AskIsOffline(tx)) => {
                let connectivity = shared_values.offline_monitor.connectivity().await;
                _ = tx.send(connectivity.is_offline());
                SameState(self.into())
            }
            Some(TunnelCommand::Disconnect(_)) | None => self.disconnect(shared_values).await,
            _ => SameState(self.into()),
        }
    }

    async fn handle_tunnel_events(
        mut self,
        event: Option<TunnelEvent>,
        shared_values: &mut SharedTunnelStateValues,
    ) -> EventConsequence {
        use self::EventConsequence::*;

        match event {
            Some(TunnelEvent::Created(metadata)) => NewState(
                ConnectedState::enter(shared_values, self.into_connected_state_bootstrap(metadata))
                    .await,
            ),
            Some(TunnelEvent::Down) => {
                self.tunnel_metadata = None;

                SameState(self.into())
            }
            None => {
                // The channel was closed
                log::debug!("The tunnel disconnected unexpectedly");
                self.disconnect(shared_values).await
            }
        }
    }
}

#[cfg(windows)]
#[allow(dead_code)]
fn is_recoverable_routing_error(error: &crate::routing::Error) -> bool {
    matches!(error, crate::routing::Error::AddRoutesFailed(_))
}

#[async_trait::async_trait]
impl TunnelState for ConnectingState {
    type Bootstrap = ();

    async fn enter(
        shared_values: &mut SharedTunnelStateValues,
        _no_need: (),
    ) -> (TunnelStateWrapper, TunnelStateTransition) {
        match shared_values.tunnel_parameters.clone() {
            None => ErrorState::enter(shared_values, ErrorStateCause::TunnelParameterMissing).await,
            Some(parameters) => {
                // open tunnel
                let (event_tx, event_rx) = mpsc::channel(1);

                let args = TunnelArgs { event_tx };

                let tunnel = match TunTunnel::open(parameters, args).await {
                    Ok(tunnel) => tunnel,
                    Err(error) => {
                        log::error!("{}", error.display_chain_with_msg("Failed to start tunnel"));
                        let block_reason = match error {
                            crate::tunnel::Error::EnableIpv6Error => {
                                ErrorStateCause::Ipv6Unavailable
                            }
                            _ => ErrorStateCause::StartTunnelError,
                        };
                        return ErrorState::enter(shared_values, block_reason).await;
                    }
                };

                let connecting_state = ConnectingState {
                    tunnel,
                    tunnel_events: event_rx,
                    tunnel_metadata: None,
                };
                (
                    TunnelStateWrapper::from(connecting_state),
                    TunnelStateTransition::Connecting,
                )
            }
        }
    }

    async fn handle_event(
        mut self,
        commands: &mut TunnelCommandReceiver,
        shared_values: &mut SharedTunnelStateValues,
    ) -> EventConsequence {
        let result = tokio::select! {
            command = commands.next() => EventResult::Command(command),
            event = self.tunnel_events.next() => EventResult::Event(event),
        };

        match result {
            EventResult::Command(command) => self.handle_commands(command, shared_values).await,
            EventResult::Event(event) => self.handle_tunnel_events(event, shared_values).await,
        }
    }
}

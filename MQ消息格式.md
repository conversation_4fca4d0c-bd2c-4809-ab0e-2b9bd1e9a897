## 资源

交换机名称: `server_list_exchange` 交换机类型: `Fanout`

<table>
    <tr>
        <th>类型</th>
        <th>说明</th>
        <th>消息格式</th>
    </tr>
    <tr>
        <td>0</td>
        <td>删除资源</td>
        <td><pre>
{
    "id": "资源ID",
    "tenant": "租户代码"
}
</pre></td>
    </tr>
    <tr>
        <td>1</td>
        <td>添加资源</td>
        <td><pre>
{
    "id": "资源ID",
    "tenant": "租户代码",
    "url": "访问地址(可选, 如果不是URL格式的, 不返回该字段)",
    "ips": [
        {
            "ip": "原始IP",
            "virtualIp": "伪IP(可选)",
            "ports": [
                22,
                [
                    23,
                    25
                ]
            ]
        }
    ],
    "ranges": [
        {
            "start": "起始IP",
            "end": "结束IP",
            "ports": [
                22,
                [
                    23,
                    25
                ]
            ]
        }
    ],
    "cidr": [
        {
            "prefix": "掩码",
            "ip": "IP",
            "ports": [
                22,
                [
                    23,
                    25
                ]
            ]
        }
    ]
}
</pre></td>
    </tr>
</table>

## 其他消息

交换机名称: `sdp_exchange` 交换机类型: `Fanout`

| 类型 | 说明                 | 数据(utf-8 编码)                        | 备注                                                             |
| :--- | :------------------- | :-------------------------------------- | ---------------------------------------------------------------- |
| 0    | 用户禁用             | 租户编码\|用户名                        |                                                                  |
| 1    | 用户被删除           | 租户编码\|用户名                        |                                                                  |
| 2    | 设备禁用             | 设备 ID                                 |                                                                  |
| 3    | 用户未生效           | 租户编码\|用户名                        |                                                                  |
| 4    | 用户已失效           | 租户编码\|用户名                        |                                                                  |
| 5    | 用户密码变更         | 16 字节设备 ID, 租户编码\|用户名        |                                                                  |
| 6    | 用户绑定码变更       | 租户编码\|用户名                        |                                                                  |
| 7    | 租户禁用             | 租户编码                                |                                                                  |
| 8    | 关闭会话             | 16 字节设备 ID                          |                                                                  |
| 9    | 自主下线其他在线终端 | 16 字节设备 ID, 租户编码\|用户名       |                                                                  |
| 100  | 用户异地登录         | TLV 类型的 IP 地址, 后跟 16 字节设备 ID | 网关内部广播通知消息, 集群环境下, 通知其他网关上连接的客户端下线 |

## 会话流量统计

```
|Tag::UTF8String|32|会话ID|
|Tag::Uint64|8|时间戳|
|Tag::Uint64|8|上行流量|
|Tag::Uint64|8|下行流量|
```

原始数据长度

56 = 32(会话 ID) + 8(时间戳) + 8(上行流量) + 8(下行流量)

按 TLV 进行编码

64 = 34(会话 ID) + 10(时间戳) + 10(上行流量) + 10(下行流量)

## 会话访问资源流量统计

```
|Tag::UTF8String|32|会话ID|
|Tag::Uint64|8|时间戳|
|Tag::Ipv4|目标IP长度|目标IP|
|Tag::Uint16|2|目标端口|
|Tag::Uint8|1|协议号|
|Tag::Uint64|8|上行流量大小|
|Tag::Uint64|8|下行流量大小|
```

原始数据长度

32(会话 ID) + 8(时间戳) + 4/16(目标 IP 地址) + 0/2(目标端口) + 1(协议号) + 8(上行流量) + 8(下行流量)

最大值: 75

按 TLV 进行编码

34(会话 ID) + 10(时间戳) + 6/18(目标 IP 地址) + 2/4(目标端口) + 3(协议号) + 10(上行流量) + 10(下行流量)

最大值: 89

use std::time::Duration;

use futures::{SinkExt, StreamExt};
use tokio::net::TcpStream;
use tokio_rustls::client::TlsStream;
use tokio_util::codec::Framed;

use base::packet::MessageCodec;
use types::backend::{BackendStateTransition, ConnectionConfig, ErrorStateCause};

use super::{
    error_state::ErrorState, proxy_connected_state::ProxyConnectedState, EventConsequence,
};
use crate::{
    backend_state_machine::{
        disconnected_state::DisconnectedState, AfterDisconnect, BackendCommand,
        BackendCommandReceiver, BackendState, BackendStateWrapper, ResponseTx,
        SharedBackendStateValues,
    },
    EventListener,
};

pub struct ProxyConnectingState {
    framed: Framed<TlsStream<TcpStream>, MessageCodec>,
}

impl ProxyConnectingState {
    async fn disconnect<L: EventListener>(
        mut self,
        shared_values: &mut SharedBackendStateValues<L>,
        _after_disconnect: AfterDisconnect,
    ) -> EventConsequence {
        if self.framed.close().await.is_err() {
            log::error!("Controller stream already closed");
        }

        EventConsequence::NewState(DisconnectedState::enter(shared_values, None).await)
    }
}

#[async_trait::async_trait]
impl<L: EventListener> BackendState<L> for ProxyConnectingState {
    type Bootstrap = (ResponseTx<(), crate::Error>, ConnectionConfig);

    async fn enter(
        shared_values: &mut SharedBackendStateValues<L>,
        args: Self::Bootstrap,
    ) -> (BackendStateWrapper, BackendStateTransition) {
        match tokio::time::timeout(
            Duration::from_secs(5),
            shared_values.proxy_connect(args.0, args.1),
        )
        .await
        {
            Ok(Some(framed)) => ProxyConnectedState::enter(shared_values, framed).await,
            _ => ErrorState::enter(shared_values, ErrorStateCause::ConnectTimeout).await,
        }
    }

    async fn handle_event(
        self,
        commands: &mut BackendCommandReceiver,
        shared_values: &mut SharedBackendStateValues<L>,
    ) -> EventConsequence {
        use EventConsequence::*;

        let command = commands.next().await;
        match command {
            Some(BackendCommand::Disconnect(tx)) => {
                let result = self
                    .disconnect(shared_values, AfterDisconnect::Nothing)
                    .await;
                if let Some(tx) = tx {
                    _ = tx.send(());
                }
                result
            }
            Some(BackendCommand::Login { .. }) => {
                self.disconnect(shared_values, AfterDisconnect::Nothing)
                    .await
            }
            None => Finished,
            Some(_) => SameState(self.into()),
        }
    }
}

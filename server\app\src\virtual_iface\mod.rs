use std::{
    net::{IpAddr, Ipv4Addr},
    sync::Arc,
};

use base::net::Addr;

use tokio::{sync::RwLock, task::JoinSet};
use tokio_util::sync::CancellationToken;
use tracing::{error, info, trace, warn, Instrument};
use tun_rs::{Devi<PERSON>Builder, Layer};

use crate::{
    comm::CommCommand,
    nat::LightNat,
    packet::{client::ClientPacket, server::set_fake_address},
    Error, CLIENTS,
};

// 客户端发送数据包给虚拟网卡
pub type ReqSender = flume::Sender<ClientPacket>;

pub async fn open(
    name: &str,
    ip: Ipv4Addr,
    src_ip: Ipv4Addr,
    shutdown_token: CancellationToken,
) -> Result<(ReqSender, Arc<RwLock<LightNat>>), Error> {
    info!(
        name,
        %ip,
        "creating virtual interface"
    );

    let device = Arc::new(
        DeviceBuilder::new()
            .layer(Layer::L3)
            .name(name)
            .mtu(1500)
            .ipv4(ip, 24, None)
            .enable(true)
            .build_async()
            .map_err(Error::IoError)?,
    );

    let reader = device.clone();
    let writer = device.clone();

    let (req_sender, req_receiver) = flume::unbounded::<ClientPacket>();

    let nat = Arc::new(RwLock::new(LightNat::new()));
    let nat_for_read = nat.clone();
    let nat_for_write = nat.clone();

    let mut handles = JoinSet::new();
    handles.spawn(
        async move {
            let mut packet = vec![0u8; device.mtu().unwrap() as _];
            loop {
                if let Ok(size) = reader.recv(packet.as_mut_slice()).await {
                    let Some(mut packet) =
                        base::utils::net::parse_ip_packet(packet[..size].to_vec())
                    else {
                        continue;
                    };
                    let (ip, port, mapping) = match packet.next_protocol {
                        1 | 58 => {
                            let identifier = packet.identifier.unwrap();
                            let access_addr = Addr(packet.src, identifier);

                            let mut nat_guard = nat_for_read.write().await;
                            (
                                access_addr.0,
                                identifier,
                                nat_guard
                                    .delete_by_access_addr(access_addr, identifier)
                                    .await,
                            )
                        }
                        6 | 17 => {
                            let (src_port, dest_port) = packet.ports.unwrap();
                            let access_addr = Addr(packet.src, src_port);
                            let nat_guard = nat_for_read.read().await;
                            (
                                access_addr.0,
                                dest_port,
                                nat_guard.get(access_addr, dest_port).await,
                            )
                        }
                        _ => {
                            warn!(protocol = packet.next_protocol, "unsupported");
                            continue;
                        }
                    };

                    let Some(mapping) = mapping else {
                        warn!(protocol = packet.next_protocol, "no NAT for {ip}:{port}");
                        continue;
                    };

                    let clients = CLIENTS.read().await;
                    if let Some(client) = clients.get(&mapping.3) {
                        set_fake_address(&client.tenant, &mut packet, mapping.0, mapping.1).await;

                        // 设置目标地址
                        packet.set_dest(mapping.2 .0);

                        // 设置IP校验和
                        packet.update_ip_checksum();

                        // 设置标识符或目标端口
                        packet.set_dest_port(mapping.2 .1);

                        // 设置校验和
                        packet.update_next_protocol_checksum();

                        trace!(peer_addr = %client.peer_addr, "{:?}", &packet.data);
                        let command = CommCommand::Packet(packet);
                        client.handle.send(command).await;
                    }
                }
            }
        }
        .instrument(tracing::error_span!("VirtualServerPacketProcessor")),
    );

    handles.spawn(
        async move {
            while let Ok((device_id, mut packet, tracing, is_virtual)) =
                req_receiver.recv_async().await
            {
                let Some((access_addr, client_local_addr)) = packet.address() else {
                    continue;
                };

                let nat_guard = nat_for_write.write().await;

                if let Some(nat_state) = if let Some(nat_state) =
                    nat_guard.get_exists(&device_id, client_local_addr, access_addr)
                {
                    drop(nat_guard);
                    Some(nat_state)
                } else {
                    drop(nat_guard);
                    let mut nat_guard = nat_for_write.write().await;
                    let nat_state = nat_guard
                        .get_or_map(
                            tracing,
                            is_virtual,
                            device_id,
                            client_local_addr,
                            access_addr,
                        )
                        .await;
                    drop(nat_guard);
                    nat_state
                } {
                    // 设置源地址
                    packet.set_src(IpAddr::V4(src_ip));

                    // 设置IP校验和
                    packet.update_ip_checksum();

                    // 设置标识符或目标端口
                    packet.set_src_port(nat_state.port());

                    // 设置校验和
                    packet.update_next_protocol_checksum();

                    if writer.send(&packet.data).await.is_err() {
                        error!("virtual interface receiver exits");
                        break;
                    }
                }
            }
        }
        .instrument(tracing::error_span!("VirtualClientPacketProcessor")),
    );

    tokio::spawn(async move {
        shutdown_token.cancelled().await;
        handles.abort_all();
        info!("Virtual interface shutdown");
    });

    Ok((req_sender, nat))
}

import{bE as z,cF as v,cG as k,cH as D,aR as c,aS as S,aE as n,u,b3 as h,t as g,bz as x,s as m,bx as i,I as a,H as p}from"./index-8ff3976b.js";const w={props:{type:{type:String,default:""},userId:{type:String,default:""},limit:{type:Number,default:0}},data(){return{tableData:[],page:1,size:5,total:0,loading:!1}},methods:{getList(){this.loading=!0,v({userId:this.userId},this.page-1,this.size,this.type).then(e=>{this.loading=!1,this.tableData=e.data.content,this.total=e.data.totalElements}).catch(e=>{this.loading=!1,console.error("获取终端列表失败:",e)})},handleSizeChange(e){this.size=e,this.getList()},handleCurrentChange(e){this.page=e,this.getList()},handleDelete(e){this.$confirm("是否确定移除该终端？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",roundButton:!0,center:!0,closeOnClickModal:!1,closeOnPressEscape:!1}).then(()=>{k({ids:[e.id],operatePlace:1,userId:this.userId}).then(r=>{this.alert.success("移除成功"),this.getList()}).catch(r=>{})}).catch(()=>{})},handleClick(e){D({trustStatus:!e.creditStatus,id:e.id,operatePlace:1}).then(r=>{this.alert.success("更新成功"),this.getList()}).catch(r=>{})}},created(){this.getList()}},I={key:0,style:{"margin-bottom":"15px"}},L={key:1,class:"pagination"};function B(e,r,d,P,t,o){const l=c("el-table-column"),_=c("el-button"),b=c("el-table"),f=c("el-pagination"),C=S("loading");return n(),u("div",null,[d.type?(n(),u("div",I,"您的设备登录数量已达上限，请从下方至少选择"+h(t.total+1-d.limit)+"个设备进行移除，以便继续登录。",1)):g("",!0),x((n(),m(b,{data:t.tableData,border:"",style:{width:"100%"}},{default:i(()=>[a(l,{prop:"name",label:"终端名称",resizable:!1}),a(l,{prop:"terminalId",label:"终端ID",resizable:!1}),a(l,{prop:"mac",label:"MAC地址",width:"150",resizable:!1}),a(l,{label:"信任状态",width:"90",resizable:!1},{default:i(s=>[p(h(s.row.creditStatus?"已信任":"未信任"),1)]),_:1}),a(l,{label:"操作",width:"130",resizable:!1},{default:i(s=>[d.type?g("",!0):(n(),m(_,{key:0,class:"terminal-btn",onClick:y=>o.handleClick(s.row),link:"",type:"primary"},{default:i(()=>[p(h(s.row.creditStatus?"取消信任":"信任"),1)]),_:2},1032,["onClick"])),a(_,{class:"terminal-btn",link:"",type:"primary",onClick:y=>o.handleDelete(s.row)},{default:i(()=>[p(" 移除 ")]),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[C,t.loading]]),t.total?(n(),u("div",L,[a(f,{onSizeChange:o.handleSizeChange,onCurrentChange:o.handleCurrentChange,"current-page":t.page,"page-sizes":[5,10,15,20],"page-size":t.size,layout:"total, sizes, prev, pager, next, jumper",total:t.total},null,8,["onSizeChange","onCurrentChange","current-page","page-size","total"])])):g("",!0)])}const N=z(w,[["render",B],["__scopeId","data-v-5c7d4207"]]);export{N as t};

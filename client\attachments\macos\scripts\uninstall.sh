#!/usr/bin/env bash

set -ue

ASSUMEYES="n"

while [[ "$#" -gt 0 ]]; do
    case $1 in
    --yes) ASSUMEYES="y" ;;
    *)
        echo "Unknown parameter: $1"
        exit 1
        ;;
    esac
    shift
done

[[ $ASSUMEYES == "y" ]] || read -r -p "您确定要停止并卸载安全令？ (y/n) "
if [[ $ASSUMEYES == "y" || "$REPLY" =~ [Yy]$ ]]; then
    echo "Uninstalling 安全令 ..."
else
    echo "Aborting uninstall"
    exit 0
fi

echo "Stopping GUI process ..."
sudo pkill -x "oneid" || echo "No GUI process found"

echo "Stopping and unloading oneid-daemon system daemon ..."
DAEMON_PLIST_PATH="/Library/LaunchDaemons/com.jingantech.oneid.plist"
sudo launchctl unload -w "$DAEMON_PLIST_PATH"
sudo rm -f "$DAEMON_PLIST_PATH"

echo "Removing app from /Applications ..."
sudo rm -rf /Applications/安全令.app
sudo pkgutil --forget com.jingantech.oneid || true

echo "Removing login item ..."
osascript -e 'tell application "System Events" to delete login item "安全令"' 2>/dev/null || true

[[ $ASSUMEYES == "y" ]] || read -r -p "您确定要删除用户数据？ (y/n)"
if [[ $ASSUMEYES == "y" || "$REPLY" =~ [Yy]$ ]]; then
    for user in /Users/<USER>
        user_settings_dir="$user/.config/oneid"
        if [[ -d "$user_settings_dir" ]]; then
            echo "Deleting settings and logs at $user_settings_dir"
            sudo rm -rf "$user_settings_dir"
        fi
    done
fi

# When run from a non-standard directory, like when detecting that the app bundle is gone,
# we must also delete the uninstall script itself
rm -f "$0" || true

[workspace]
members = [
  "base",
  "proxy-request",
  "version",
  "tlv/tlv",
  "tlv/tlv-derive",
  "tlv/tlv-types",
  "client/types",
  "client/windows-net",
  "client/core",
  "client/sdp-dbus",
  "client/communicate-interface",
  # "client/cli",
  "client/src-tauri",
  "client/encrypt",
  "client/encrypt-files",
  "client/config",
  "client/daemon",
  "tools/build-tool",
  "tools/package",
  "tools/tunctl",
  "server/app",
  "server/share",
  "server/ebpf",
  "server/xdp",
  "server/codec",
  "server/core",
  "err_ext",
]
resolver = "2"

[workspace.dependencies]
biometric = {git = "https://git.jingantech.net/pub/biometric.git", branch = "master"}
coarsetime = "0.1.34"
exception_logging = {git = "https://git.jingantech.net/pub/exception_logging.git", branch = "master"}
gm-sm2 = {git = "https://git.jingantech.net/pub/gm-rs.git"}
gm-sm3 = {git = "https://git.jingantech.net/pub/gm-rs.git"}
gm-sm4 = {git = "https://git.jingantech.net/pub/gm-rs.git"}
hex = {git = "https://git.jingantech.net/pub/hex.git", branch = "master"}
netsh = {git = "https://git.jingantech.net/pub/netsh.git", branch = "master"}
once_cell = "1.19"
paths = {git = "https://git.jingantech.net/pub/paths.git", branch = "master"}
tokio = "1"
tun-rs = {git = "https://gitee.com/luoffei/tun-rs.git", branch = "api"}
uniqueid = {git = "https://git.jingantech.net/pub/uniqueid.git", branch = "master"}

[workspace.dependencies.pkcs8]
default-features = false
features = ["alloc", "pem"]
version = "0.10.2"

[patch.crates-io]
auto-launch = {git = "https://gitee.com/luoffei/auto-launch.git", branch = "main"}
ksni = {git = "https://gitee.com/luoffei/ksni.git", branch = "master"}
pki-types = {git = "https://gitee.com/luoffei/pki-types.git", branch = "tlcp-1.11.0", package = "rustls-pki-types"}
reqwest = {git = "https://gitee.com/luoffei/reqwest.git", branch = "tlcp-0.12.13"}
ring = {git = "https://gitee.com/luoffei/ring.git", branch = "tlcp-0.17.13"}
rustls = {git = "https://gitee.com/luoffei/rustls.git", branch = "tlcp-0.23.25"}
rustls-webpki = {git = "https://gitee.com/luoffei/webpki.git", branch = "tlcp-0.103.0", package = "rustls-webpki"}
tao = {git = "https://gitee.com/luoffei/tao.git", branch = "0.16.x"}
tauri = {git = "https://gitee.com/luoffei/tauri.git", branch = "1.x"}
tauri-build = {git = "https://gitee.com/luoffei/tauri.git", branch = "1.x"}
tokio-tungstenite = {git = "https://gitee.com/luoffei/tokio-tungstenite.git", branch = "master"}

[patch."https://github.com/tauri-apps/plugins-workspace"]
tauri-plugin-autostart = {git = "https://gitee.com/luoffei/plugins-workspace", branch = "v1"}
tauri-plugin-single-instance = {git = "https://gitee.com/luoffei/plugins-workspace", branch = "v1"}

[profile.dev]
panic = "abort" # Strip expensive panic clean-up logic

[profile.release]
codegen-units = 1 # Compile crates one after another so the compiler can optimize better
lto = true # Enables link to optimizations
opt-level = "s" # Optimize for binary size
panic = "abort" # Strip expensive panic clean-up logic
# strip = true # Remove debug symbols
# debug = true

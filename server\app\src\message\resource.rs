use amq_protocol::types::FieldTable;
use deadpool_lapin::Pool;
use futures::{future::BoxFuture, FutureExt, StreamExt};
use lapin::{
    options::{
        BasicAckOptions, BasicConsumeOptions, BasicQosOptions, QueueBindOptions,
        QueueDeclareOptions,
    },
    Channel,
};
use rand::Rng;
use tokio_util::sync::CancellationToken;
use tracing::{error, info, warn, Instrument};

use super::{SERVER_LIST_EXCHANGE, SERVER_LIST_QUEUE};
use crate::{
    message::MQCommand,
    types::{AddResource, DelResource},
    Error, EventSender, InternalEvent,
};

fn do_listen(
    pool: Pool,
    channel: Channel,
    event_sender: EventSender,
    shutdown_token: CancellationToken,
) -> BoxFuture<'static, Result<(), lapin::Error>> {
    async move {
        // 声明队列
        let mut queue_declare_options = QueueDeclareOptions::default();
        // 连接断开时, 自动删除队列
        queue_declare_options.auto_delete = true;
        let num: u8 = rand::rng().random_range(0..100);
        let server_list_queue = format!("{}_{}", SERVER_LIST_QUEUE, num);
        channel
            .queue_declare(
                &server_list_queue,
                queue_declare_options,
                FieldTable::default(),
            )
            .await?;
        let bind_options = QueueBindOptions::default();
        channel
            .queue_bind(
                &server_list_queue,
                SERVER_LIST_EXCHANGE,
                SERVER_LIST_QUEUE,
                bind_options,
                FieldTable::default(),
            )
            .await?;
        let qos_options = BasicQosOptions::default();
        channel.basic_qos(1, qos_options).await?;
        let mut resources_consumer = channel
            .basic_consume(
                &server_list_queue,
                "server_list_consumer",
                BasicConsumeOptions::default(),
                FieldTable::default(),
            )
            .await?;
        tokio::spawn(
            async move {
                loop {
                    tokio::select! {
                        _ = shutdown_token.cancelled() => return,
                        result = resources_consumer.next() => {
                            if let Some(Ok(delivery)) = result {
                                // 消息标记 0: 删除资源, 1: 添加资源
                                let tag = delivery.data[0];
                                if let Some(message) = if tag == 0 {
                                    if let Ok(del_resource) =
                                        serde_json::from_slice::<DelResource>(&delivery.data[1..])
                                    {
                                        Some(InternalEvent::MQMessage(MQCommand::DelResource {
                                            tenant: del_resource.tenant,
                                            resource: del_resource.id,
                                        }))
                                    } else {
                                        error!("Deserialization of resource deletion message failed");

                                        None
                                    }
                                } else {
                                    if let Ok(add_resource) =
                                        serde_json::from_slice::<AddResource>(&delivery.data[1..])
                                    {
                                        Some(InternalEvent::MQMessage(MQCommand::AddResource {
                                            tenant: add_resource.tenant,
                                            resource: add_resource.resource,
                                        }))
                                    } else {
                                        error!("Deserialization of resource addition message failed");

                                        None
                                    }
                                } {
                                    event_sender.send(message).await;
                                }

                                delivery
                                    .ack(BasicAckOptions::default())
                                    .await
                                    .expect("basic_ack");
                            } else {
                                break;
                            }
                        }
                    }
                }

                error!("reconnecting...");

                let retry_future = async move {
                    let mut timeout = 1;

                    loop {
                        let channel = get_channel!(pool, timeout);

                        if let Err(err) = do_listen(
                            pool.clone(),
                            channel,
                            event_sender.clone(),
                            shutdown_token.child_token(),
                        )
                        .await
                        {
                            error!("listen error: {err}");

                            tokio::time::sleep(std::time::Duration::from_millis(
                                2 << std::cmp::max(12, timeout),
                            ))
                            .await;
                            timeout += 1;
                            continue;
                        }
                        info!("reconnected");
                        break;
                    }
                };

                tokio::spawn(retry_future);
            }
            .instrument(tracing::error_span!(parent: None, "ResourceConsumer")),
        );

        Ok(())
    }
    .boxed()
}

/// 监听资源变化
pub(super) async fn listen(
    pool: Pool,
    event_sender: EventSender,
    shutdown_token: CancellationToken,
) -> Result<(), Error> {
    let conn = pool
        .get()
        .await
        .map_err(|error| Error::MqConsumerError(error.to_string()))?;

    let channel = conn
        .create_channel()
        .await
        .map_err(|error| Error::MqConsumerError(error.to_string()))?;
    channel.on_error(|error| {
        warn!("resource channel status is abnormal: {error}");
    });

    do_listen(pool, channel, event_sender, shutdown_token)
        .await
        .map_err(|error| Error::MqConsumerError(error.to_string()))?;

    Ok(())
}

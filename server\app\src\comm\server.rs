use std::{
    collections::{HashMap, HashSet},
    net::{Ip<PERSON>ddr, SocketAddr},
    sync::Arc,
    time::Duration,
};

use base::{
    packet::{Message, MessageCodec, MessageType},
    spa::SPAType,
};
use err_ext::ErrorExt;
use futures::{SinkExt, StreamExt};
use http::HeaderValue;
use moka::future::Cache;
use proxy_request::HttpRequest;
use serde_json::Value;
use tlv::Serialize;
use tlv_types::{AuthPacket, AuthResult, ChangePwdByVerifyCode, ModePayload, ProxyRequest};
use tokio::{net::TcpStream, sync::oneshot, task::JoinSet};
use tokio_rustls::server::TlsStream;
use tokio_util::{codec::Framed, sync::CancellationToken};
use tracing::{debug, error, Instrument};

use crate::{
    backend::{client::BackendCommonClient, proxy},
    cache::CacheManager,
    message::MqHandle,
    packet::client::ClientPacketHandle,
    EventSender,
};

use super::{
    server_handler::{self, MixHandleArgs},
    AuthUser, Error, Session,
};

pub(super) enum ChannelMessage {
    Exit,
    Message {
        message: Message,
        callback: Option<oneshot::Sender<()>>,
    },
}

struct HttpProxySender(flume::Sender<ChannelMessage>);

impl proxy::Sender for HttpProxySender {
    async fn send(&self, message: Message) -> Result<(), proxy_request::Error> {
        if self
            .0
            .send_async(ChannelMessage::Message {
                message,
                callback: None,
            })
            .await
            .is_err()
        {
            error!("connection already close or thread panicked.");
        }

        Ok(())
    }
}

/// 认证
pub(super) struct MixServer {
    pub(super) authorization_interval: u64,
    pub(super) dns_servers: Option<Vec<IpAddr>>,

    pub(super) peer_addr: SocketAddr,
    pub(super) local_addr: SocketAddr,
    pub(super) stream: Option<TlsStream<TcpStream>>,
    pub(super) event_sender: EventSender,
    pub(super) cache_manager: CacheManager,
    pub(super) backend_client: Arc<BackendCommonClient>,
    pub(super) client_packet_handle: ClientPacketHandle,
    pub(super) mq_handle: MqHandle,
    pub(super) resource_whitelist: Option<Arc<HashSet<SocketAddr>>>,
    pub(super) spa_cache: Arc<Cache<IpAddr, HashMap<String, SPAType>>>,
    pub(super) shutdown_token: CancellationToken,
}

impl MixServer {
    #[tracing::instrument(name = "Authenticator", parent = None, skip_all, fields(peer_addr = %self.peer_addr))]
    pub(super) async fn start(mut self) -> Result<(), ()> {
        let stream = self.stream.take();
        if stream.is_none() {
            return Ok(());
        }

        let codec = MessageCodec {};
        let framed = Framed::new(stream.unwrap(), codec);
        let (tx, rx) = flume::bounded::<ChannelMessage>(16);
        let (mut sink, mut stream) = framed.split();

        let write_half = tokio::spawn(async move {
            loop {
                match rx.recv_async().await {
                    Err(_) => break sink,
                    Ok(message) => match message {
                        ChannelMessage::Exit => {
                            break sink;
                        }
                        ChannelMessage::Message { message, callback } => {
                            if let Err(err) = sink.send(message).await {
                                error!("{err}");
                                if let Some(tx) = callback {
                                    _ = tx.send(());
                                }
                                break sink;
                            }
                            if let Some(tx) = callback {
                                _ = tx.send(());
                            }
                        }
                    },
                }
            }
        });

        let mut proxy_request_set = JoinSet::new();

        let timeout = Duration::from_secs(15);
        loop {
            tokio::select! {
                _ = self.shutdown_token.cancelled() => break,
                next = tokio::time::timeout(timeout, stream.next()) => match next {
                    Ok(None) => {
                        break;
                    }
                    Ok(Some(Err(err))) => {
                        error!("{err}");
                        break;
                    }
                    Ok(Some(Ok(message))) => {
                        let message_type = message.r#type();
                        match message_type {
                            MessageType::Authentication => {
                                let input = message.payload();
                                let Ok(auth_pkt): Result<AuthPacket, &'static str> =
                                    tlv::read_to_struct!(input)
                                else {
                                    error!(?message_type, "bad message");
                                    break;
                                };

                                if !self
                                    .check_spa_matched(
                                        &auth_pkt.device_id,
                                        &[SPAType::Auth],
                                        message_type,
                                    )
                                    .await
                                {
                                    break;
                                }

                                // 执行认证
                                match self.handle_auth_request(tx.clone(), auth_pkt.payload).await {
                                    Ok(session) => {
                                        _ = tx.send_async(ChannelMessage::Exit).await;
                                        let Ok(sink) = write_half.await else {
                                            return Err(());
                                        };
                                        let framed = sink.reunite(stream).unwrap();

                                        let backend_client = self.backend_client.new_client(
                                            &session.device_id,
                                            &session.tenant,
                                            &session.env,
                                        );

                                        let args = MixHandleArgs {
                                            authorization_interval: self.authorization_interval,
                                            session,
                                            stream: framed,
                                            cache_manager: self.cache_manager,
                                            event_sender: self.event_sender,
                                            resource_whitelist: self.resource_whitelist,
                                            backend_client: Arc::new(backend_client),
                                            client_packet_handle: self.client_packet_handle,
                                            mq_handle: self.mq_handle,
                                        };

                                        server_handler::handle(args).await;
                                        return Ok(());
                                    }
                                    Err(err) => {
                                        error!("{err}");
                                        break;
                                    }
                                }
                            }
                            MessageType::ProxyRequest => {
                                let input = message.payload();
                                let Ok(request): Result<ProxyRequest, &'static str> =
                                    tlv::read_to_struct!(input)
                                else {
                                    error!(?message_type, "bad message");
                                    break;
                                };
                                let Ok(mut http_request) =
                                    serde_json::from_slice::<HttpRequest>(&request.data)
                                else {
                                    error!(?message_type, "bad message");
                                    break;
                                };

                                if !self
                                    .check_spa_matched(
                                        &request.device_id,
                                        &[SPAType::Proxy, SPAType::Auth],
                                        message_type,
                                    )
                                    .await
                                {
                                    break;
                                }

                                proxy_request_set.spawn({
                                    let tx = tx.clone();
                                    let base_url = self.backend_client.base_url();
                                    _ = http_request.url.set_scheme(base_url.scheme());
                                    _ = http_request.url.set_host(base_url.host_str());
                                    _ = http_request.url.set_port(base_url.port());

                                    if let Some(headers) = http_request.headers.as_mut() {
                                        let ip = self.peer_addr.ip().to_string();
                                        _ = headers.0.insert(
                                            "x-forwarded-for",
                                            HeaderValue::from_str(ip.as_str()).unwrap(),
                                        );
                                    }

                                    async move {
                                        let response_sender = HttpProxySender(tx);
                                        let url = http_request.url.clone();
                                        if let Err(err) = proxy::execute(
                                            &request.device_id,
                                            request.seq,
                                            http_request,
                                            response_sender,
                                        )
                                        .await
                                        {
                                            error!(%url, "http proxy fail. {}", err.display_chain());
                                        }
                                    }
                                }.in_current_span());
                            }
                            MessageType::ChangePwdByVerifyCode => {
                                let input = message.payload();
                                let Ok(request): Result<ChangePwdByVerifyCode, &'static str> =
                                    tlv::read_to_struct!(input)
                                else {
                                    error!(?message_type, "bad message");
                                    break;
                                };

                                if !self
                                    .check_spa_matched(
                                        &request.device_id,
                                        &[SPAType::ChangePwd],
                                        message_type,
                                    )
                                    .await
                                {
                                    break;
                                }

                                // 修改密码
                                _ = super::common::handle_change_password_request(
                                    self.peer_addr,
                                    self.backend_client,
                                    tx.clone(),
                                    request.data,
                                )
                                .await
                                .inspect_err(|err| {
                                    error!("{err}");
                                });
                                break;
                            }
                            _ => {
                                error!(?message_type, "received unexpected message",);
                                break;
                            }
                        }
                    }
                    Err(_) => {
                        debug!("wait timeout, close connection");
                        break;
                    }
                }
            }
        }

        drop(tx);

        // 关闭接口请求
        proxy_request_set.abort_all();

        let Ok(mut sink) = write_half.await else {
            return Err(());
        };

        if let Err(err) = sink.close().await {
            error!("{err}");
        }

        Err(())
    }

    /// 检查SPA类型与请求类型是否匹配
    ///
    /// - 1. 在经过SNAT后, 同一网络中, 某一个设备经过SPA认证后,
    /// 即端口针对这个网络已放开
    /// 其他设备在认证时, SPA可能还未验证完成, TCP连接先进来了,
    /// 会导致验证失败 睡眠100ms, 等待SPA验证完成
    ///
    /// - 2. 已经通过其他类型的SPA打开端口, 但是当前请求的类型与已有的不符
    /// 此时, 需要等待新的SPA认证完成
    async fn check_spa_matched(
        &self,
        device_id: &str,
        expected_type: &[SPAType],
        message_type: MessageType,
    ) -> bool {
        let mut times = 1;
        loop {
            let result = self
                .spa_cache
                .get(&self.peer_addr.ip())
                .await
                .map(|devices| devices.get(device_id).map(|spa_type| *spa_type))
                .unwrap_or_default();
            if let Some(spa_type) = result {
                if expected_type.contains(&spa_type) {
                    break true;
                }
            }
            if times >= 200 {
                if result.is_none() {
                    error!(device = device_id, "client is not authorized by SPA");
                } else {
                    error!(?message_type, "message does not match SPA type");
                }

                break false;
            }
            debug!("waiting for SPA verification");
            times += 1;

            // 尽可能加速, 间隔时间设置为10ms
            _ = tokio::time::sleep(Duration::from_millis(10)).await;
        }
    }

    async fn handle_auth_request(
        &self,
        tx: flume::Sender<ChannelMessage>,
        auth_pkt: Value,
    ) -> Result<Session, Error> {
        debug!("received authentication packet");

        let auth_result = super::auth::auth(
            self.peer_addr,
            auth_pkt,
            &self.cache_manager,
            self.backend_client.clone(),
            tx.clone(),
        )
        .await?;

        // 填充重连票据
        let token = hex::to_string!(&auth_result.token);
        let user = match self
            .cache_manager
            .get::<AuthUser>(&format!("{}|{token}", &auth_result.tenant))
            .await
        {
            Some(user) => user,
            None => {
                error!("the token does not exist or has expired. {token}");
                return Err(Error::AuthenticationFailed);
            }
        };

        let response = AuthResult {
            token,
            expire_tip_day: auth_result.expire_tip_day as u32,
            expire_day: auth_result.expire_day as u32,
            dns: self.dns_servers.clone(),
            data: ModePayload::Mix {
                reconnect_token: user.ticket,
                expire: user.expire,
            },
            client_ip: Some(self.peer_addr.ip()),
        };

        let auth_response = Message::new(MessageType::AuthSuccess, &response.serialize());
        _ = tx
            .send_async(ChannelMessage::Message {
                message: auth_response,
                callback: None,
            })
            .await;
        debug!("authentication successfully");
        Ok(Session {
            peer_addr: self.peer_addr,
            local_addr: self.local_addr,
            device_id: auth_result.device_id,
            tenant: auth_result.tenant,
            username: auth_result.username,
            user_id: user.user_id,
            env: auth_result.env,
            id: user.session_id,
        })
    }
}

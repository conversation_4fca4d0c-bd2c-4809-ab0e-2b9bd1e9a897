use clap::Parser;
use colored::Colorize;
use std::{collections::HashMap, io, path::PathBuf, process::Stdio};
use tokio::{
    io::{AsyncBufReadExt, BufReader},
    process::Command,
    sync::broadcast::Sender,
};

type File<'a> = elf::ElfBytes<'a, elf::endian::AnyEndian>;

#[derive(Debug, Parser)]
pub struct Options {
    /// Verbose
    #[clap(long, short)]
    pub verbose: bool,

    /// Build and run the release target
    #[clap(long, short)]
    pub release: bool,

    #[clap(long)]
    pub disasm: bool,
}

pub async fn run(tx: Sender<()>, opts: Options) -> Result<(), anyhow::Error> {
    crate::build_ebpf::run(tx.clone(), opts.verbose).await?;

    if opts.disasm {
        println!(
            "{}",
            "\n========================= 2. Disasm =========================\n".red()
        );

        piped("bpfel", "primary", std::io::stdout())?;
        piped("bpfel", "secondary", std::io::stdout())?;

        return Ok(());
    }

    println!(
        "{}",
        "\n========================= 2. tunctl =========================\n".red()
    );

    let mut command = Command::new("cargo");

    let dir = PathBuf::from("tools/tunctl");
    command.current_dir(&dir).arg("build");

    if opts.release {
        command.arg("--release");
    }

    let mut rx = tx.subscribe();
    let build_envs: HashMap<&str, String> = HashMap::new();
    print_command_output!(opts.verbose, command, build_envs, rx);

    println!(
        "{}",
        "\n========================= 3. 网关 =========================\n".red()
    );

    let mut command = Command::new("cargo");

    let dir = PathBuf::from("server/app");
    command.current_dir(&dir).arg("build");

    if opts.release {
        command.arg("--release");
    }

    let mut rx = tx.subscribe();
    let build_envs: HashMap<&str, String> = HashMap::new();
    print_command_output!(opts.verbose, command, build_envs, rx);
    Ok(())
}

pub fn piped<O: io::Write>(input: &str, target: &str, mut out: O) -> Result<(), anyhow::Error> {
    let prog = std::fs::read(format!("server/xdp/src/bpf/sdp-{input}-{target}.ebpf"))?;
    let file = File::minimal_parse(&prog)?;

    writeln!(out, "===== {input}-{target} =====")?;
    dump(&file, &format!("server/xdp/sdp-{target}"), &mut out)?;
    dump(&file, ".text", &mut out)?;

    Ok(())
}

fn dump<O: io::Write>(file: &File, section: &str, mut out: O) -> Result<(), anyhow::Error> {
    if let Some(header) = file.section_header_by_name(section)? {
        if let Ok((data, _compression)) = file.section_data(&header) {
            writeln!(out, "----- {section} -----")?;
            for insn in rbpf::disassembler::to_insn_vec(data) {
                writeln!(out, "  {}", insn.desc)?;
            }
        }
    }

    Ok(())
}

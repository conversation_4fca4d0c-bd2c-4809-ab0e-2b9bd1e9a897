use config::AppConfig;
#[cfg(feature = "cluster")]
use url::Url;

fn main() {
    let resource_dir = match paths::resource_dir() {
        Ok(dir) => dir,
        Err(error) => {
            log::error!("{error}");
            std::process::exit(1)
        }
    };

    // 升级配置文件
    let _ = config::migrations::migrate_all(&resource_dir);

    // 配置文件中的控制器地址与程序包默认地址不匹配时, 覆盖默认配置
    #[cfg(feature = "cluster")]
    let matched = {
        // 加载配置文件
        let app_config = config::init(resource_dir);

        if let (Some(cluster_config_url), Some(cluster_exterbak_config_url)) = (
            config::CLUSTER_CONFIG_URL,
            config::CLUSTER_EXTERNAL_CONFIG_URL,
        ) {
            if app_config.cluster_config_url.is_none()
                || app_config.cluster_external_config_url.is_none()
            {
                false
            } else {
                use std::ops::Deref;

                let internal = app_config.cluster_config_url.as_ref().unwrap();
                let external = app_config.cluster_config_url.as_ref().unwrap();

                urls_equal(internal.deref(), cluster_config_url).is_ok()
                    && urls_equal(external.deref(), cluster_exterbak_config_url).is_ok()
            }
        } else {
            true
        }
    };

    #[cfg(all(feature = "sdp", not(feature = "cluster")))]
    let matched = true;

    #[cfg(any(feature = "cluster", feature = "sdp"))]
    {
        if !matched {
            let default_config = AppConfig::default();
            _ = default_config.save_file();
        }
    }
}

/// 判断两个 URL 是否等价（忽略大小写、默认端口、query 顺序、path 末尾 `/` 差异）。
#[cfg(feature = "cluster")]
fn urls_equal(ua: &Url, b: &str) -> Result<bool, url::ParseError> {
    let ub = Url::parse(b)?;
    // scheme + host
    if !ua.scheme().eq_ignore_ascii_case(ub.scheme()) {
        return Ok(false);
    }
    let ha = ua.host_str().map(str::to_lowercase);
    let hb = ub.host_str().map(str::to_lowercase);
    if ha != hb {
        return Ok(false);
    }

    // 端口（把默认端口算上）
    if ua.port_or_known_default() != ub.port_or_known_default() {
        return Ok(false);
    }

    // 比较 path，去掉末尾多余的 `/`
    let pa = ua.path().trim_end_matches('/');
    let pb = ub.path().trim_end_matches('/');
    if pa != pb {
        return Ok(false);
    }

    // 比较 query：解析成 (key, value) 列表、排序后再比
    let mut qa: Vec<_> = ua.query_pairs().collect();
    let mut qb: Vec<_> = ub.query_pairs().collect();
    qa.sort();
    qb.sort();
    if qa != qb {
        return Ok(false);
    }

    // 如果需要比较 fragment，下面这行打开即可
    // if ua.fragment() != ub.fragment() { return Ok(false); }

    Ok(true)
}

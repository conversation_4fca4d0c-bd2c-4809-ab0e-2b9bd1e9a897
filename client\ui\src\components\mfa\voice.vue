<template>
  <div class="mfa-content">
    <h3 v-if="type">声纹登录</h3>
    <div v-if="firstStep && type === 'login'">
      <!-- <h3>声纹登录</h3>  -->
      <div class="inputItem">
        <div class="inputContent">
          <el-input
            ref="username"
            v-model="user.username"
            placeholder="请输入用户名"
          />
        </div>
      </div>
      <el-button
        ref="nextStepBtn"
        class="Btn"
        :disabled="!user.username"
        @click="generateRequestId"
      >
        下一步
      </el-button>
      <userAgreement />
    </div>
    <div v-else>
      <img
        :src="src"
        :class="type ? 'mfa-content-img' : 'mfa-content-imgmode'"
        alt=""
      />
      <div class="auth-tip">
        请打开<span class="mfa-text">【安全令APP】</span>验证声纹
      </div>
      <p v-if="type" style="font-size: 12px">
        用户:
        <span @click="cancel" style="color: #f9780c; cursor: pointer"
          >&nbsp;{{ user.username }}&nbsp;
          <el-icon
            color="#F9780C"
            style="vertical-align: middle; cursor: pointer"
            ><EditPen
          /></el-icon>
        </span>
      </p>
    </div>
  </div>
</template>

<script>
import { getRequestId, pollingMfa, cancelAuth } from "@/api/service";
import voiceAuthImg from "/assets/images/mfa/voice-auth.png";
import userAgreement from "../../views/userAgreement.vue";

export default {
  name: "VOICE",
  components: {
    userAgreement,
  },
  props: {
    participantGroupId: {
      type: String,
      default: "",
    },
    userId: {
      type: String,
      default: "",
    },
    type: {
      type: String,
      default: "",
    },
    name: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      polling: undefined,
      requestId: "",
      src: voiceAuthImg,
      code: "voice",
      user: {
        username: "" || this.name,
      },
      firstStep: true,
    };
  },
  created() {
    if (this.type !== "login") {
      this.generateRequestId();
    }
  },
  methods: {
    // 获取requestId
    generateRequestId() {
      let data = {
        participantTypes: this.type === "login" ? "03" : "01,03",
        participantKeyword: this.userId || this.user.username,
      };
      getRequestId(
        this.participantGroupId,
        data.participantTypes,
        data.participantKeyword,
        this.code
      )
        .then((result) => {
          this.requestId = result.data;
          this.$emit("requestId", this.requestId);
          this.startPolling();
          this.firstStep = false;
        })
        .catch((error) => {
          this.$emit("generateRequestIdFailCallbackFunc", error);
        });
    },
    // 轮询验证结果
    startPolling() {
      this.polling = setInterval(() => {
        if (!this.requestId) {
          this.stopPolling();
          return;
        }
        pollingMfa(this.requestId)
          .then((result) => {
            if (result.data === "DOING") {
              return;
            }
            switch (result.data) {
              case "FAILED":
                this.alert.error(result.data.errorMsg || "系统错误");
                break;
              case "SUCCESS":
                this.Complete();
                break;
            }
            this.stopPolling();
            this.requestId = "";
          })
          .catch(() => {
            this.requestId = "";
            this.cancel();
            this.$emit("childEventCancel", this.requestId);
            this.$emit("cancelMfaCallbackFunc");
          });
      }, 1000);
    },
    // 停止轮询
    stopPolling() {
      this.polling && clearInterval(this.polling);
    },
    Complete() {
      this.stopPolling();
      this.$emit("mfaCallbackFunc", {
        requestId: this.requestId,
        type: this.code,
      });
    },
    cancel() {
      this.stopPolling();
      if (this.requestId) {
        let requestId = this.requestId;
        this.requestId = "";
        cancelAuth(requestId);
      }
      this.firstStep = true;
    },
  },
};
</script>

<style scoped lang="less">
.inputItem {
  text-indent: 0.6em;
  position: relative;
  margin-top: 1.8rem;
  border-bottom: 1px solid #d1d3db;
  // border-radius: 30px;
  margin-bottom: 10px;
  &:hover {
    border-bottom: 1px solid #f9780c;
  }
}

.login_icon {
  color: #bfbfbf;
  font-size: 22px;
}

.inputContent {
  padding: 0.4rem;
  width: 240px;
}

:deep(.el-input__wrapper) {
  border: 0 !important;
  padding: 1px 1px;
}

.Btn {
  margin: 15px 0 10px;
  width: 100%;
  color: #f59547;
  background: #fff;
  border: 1px solid #f59547;
  outline: none;
  height: 45px;
  border-radius: 30px;
}
</style>

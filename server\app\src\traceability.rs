//! 溯源

use once_cell::sync::Lazy;
use std::{
    collections::HashMap,
    net::{IpAddr, Ipv4Addr, SocketAddr},
    sync::atomic::{AtomicU32, Ordering},
};
use tracing::{debug, error};

use tlv_types::TraceabilityResource;
use tokio::sync::RwLock;

/// 溯源资源虚拟IP映射
pub static TRACEABILITY_VIRTUAL_IP_MAPPING: Lazy<RwLock<HashMap<SocketAddr, IpAddr>>> =
    Lazy::new(|| RwLock::new(Default::default()));

/// 溯源应用真实IP映射
pub static TRACEABILITY_REAL_IP_MAPPING: Lazy<RwLock<HashMap<SocketAddr, IpAddr>>> =
    Lazy::new(|| RwLock::new(Default::default()));

/// 溯源资源使用的虚拟IP网段
const VIRTUAL_NETWORK: u32 = u32::from_be_bytes([10, 7, 0, 0]);
static SERIAL: AtomicU32 = AtomicU32::new(1);

pub async fn add_resources<'a, T>(resources: T) -> Option<Vec<(IpAddr, IpAddr)>>
where
    T: Iterator<Item = &'a TraceabilityResource>,
{
    let mut virtual_mapping = TRACEABILITY_VIRTUAL_IP_MAPPING.write().await;
    let mut real_mapping = TRACEABILITY_REAL_IP_MAPPING.write().await;

    let mut virtual_resources = vec![];
    for resource in resources {
        let Some(port) = resource.url.port_or_known_default() else {
            continue;
        };

        for ip in &resource.ips {
            let virtual_ip = real_mapping
                .iter()
                .find(|(real, _)| real.ip() == *ip)
                .map(|(_, virtual_ip)| Some(*virtual_ip))
                .unwrap_or_else(next_virtual_ip);

            let Some(virtual_ip) = virtual_ip else {
                error!("no remaining virtual IP available");
                return None;
            };

            debug!("virtual map: {}-{}", ip, virtual_ip);
            virtual_resources.push((*ip, virtual_ip));
            virtual_mapping.insert(SocketAddr::new(virtual_ip, port), *ip);
            real_mapping.insert(SocketAddr::new(*ip, port), virtual_ip);
        }
    }
    if !virtual_resources.is_empty() {
        return Some(virtual_resources);
    }
    None
}

pub async fn del_resource(resource: &TraceabilityResource) -> Option<Vec<(IpAddr, IpAddr)>> {
    let Some(port) = resource.url.port_or_known_default() else {
        return None;
    };

    let mut virtual_resources = vec![];

    let mut virtual_mapping = TRACEABILITY_VIRTUAL_IP_MAPPING.write().await;
    let mut real_mapping = TRACEABILITY_REAL_IP_MAPPING.write().await;
    for ip in &resource.ips {
        if let Some(virtual_ip) = real_mapping.remove(&SocketAddr::new(*ip, port)) {
            virtual_resources.push((*ip, virtual_ip));
            _ = virtual_mapping.remove(&SocketAddr::new(virtual_ip, port));
        }
    }

    Some(virtual_resources)
}

fn next_virtual_ip() -> Option<IpAddr> {
    let mut virtual_ip = VIRTUAL_NETWORK + SERIAL.fetch_add(1, Ordering::Acquire);
    // 第四段为0
    if virtual_ip << 24 == 0 {
        _ = SERIAL.fetch_add(1, Ordering::Acquire);
        virtual_ip += 1;
    }

    // 不在********子网
    if (virtual_ip >> 16) << 16 != VIRTUAL_NETWORK {
        return None;
    }

    Some(IpAddr::V4(Ipv4Addr::from(virtual_ip)))
}

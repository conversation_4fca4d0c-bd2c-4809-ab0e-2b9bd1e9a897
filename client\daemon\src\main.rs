use std::{path::PathBuf, thread, time::Duration};

use oneidservice::{log::log_dir, runtime::new_runtime_builder, ErrorExt};
use tokio::sync::broadcast;

mod cli;

#[cfg(windows)]
mod system_service;

#[cfg(target_os = "linux")]
mod systemd_service;

const DAEMON_LOG_FILENAME: &str = "daemon.log";

fn main() -> Result<(), Box<dyn std::error::Error>> {
    let config = cli::get_config();

    init_daemon_logging(config).unwrap_or_else(|error| {
        eprintln!("{error}");
        std::process::exit(1);
    });

    log::trace!("Using configuration: {:?}", config);

    if let Err(error) = uniqueid::device_id() {
        log::error!("Failed to generate device ID: {}", error.1);
        std::process::exit(1);
    }

    #[cfg(windows)]
    if persistent_port::is_persistent_port(50021) || persistent_port::is_persistent_port(52028) {
        log::error!("Port 50021 or 52028 is a reserved port");
        // std::process::exit(1);
    }

    let runtime = new_runtime_builder().build().unwrap_or_else(|error| {
        eprintln!("{}", error.display_chain());
        std::process::exit(1);
    });

    let exit_code = match runtime.block_on(run_platform(config)) {
        Ok(_) => 0,
        Err(error) => {
            log::error!("{}", error);
            1
        }
    };
    log::debug!("Process exiting with code {}", exit_code);
    std::process::exit(exit_code);
}

fn init_daemon_logging(config: &cli::Config) -> Result<Option<PathBuf>, String> {
    let log_dir = log_dir().map_err(|e| e.display_chain_with_msg("Unable to get log directory"))?;

    init_logger(config, log_dir.join(DAEMON_LOG_FILENAME))?;

    log::info!("Logging to {}", log_dir.display());
    Ok(Some(log_dir))
}

fn init_logger(config: &cli::Config, log_file: PathBuf) -> Result<(), String> {
    oneidservice::log::init_logger(config.log_level, &log_file, true)
        .map_err(|e| e.display_chain_with_msg("Unable to initialize logger"))?;
    log_panics::init();
    exception_logging::enable();
    oneidservice::version::log_version();
    Ok(())
}

#[cfg(windows)]
async fn run_platform(config: &cli::Config) -> Result<(), String> {
    if config.run_as_service {
        system_service::run()
    } else if config.register_service {
        let install_result = system_service::install_service().map_err(|e| e.display_chain());
        if install_result.is_ok() {
            println!("Installed the service.");
        }
        install_result
    } else {
        run_standalone().await
    }
}

#[cfg(target_os = "linux")]
async fn run_platform(config: &cli::Config) -> Result<(), String> {
    if config.register_service {
        let install_result = systemd_service::install_service().map_err(|e| e.display_chain());
        if install_result.is_ok() {
            println!("Installed the service.");
        }
        return install_result;
    }
    run_standalone().await
}

#[cfg(not(any(windows, target_os = "linux")))]
async fn run_platform(_config: &cli::Config) -> Result<(), String> {
    run_standalone().await
}

async fn run_standalone() -> Result<(), String> {
    if !running_as_admin() {
        return Err(
            "Running daemon as a non-administrator user, clients might refuse to connect"
                .to_owned(),
        );
    }

    let (shutdown_tx, _shutdown_rx) = broadcast::channel(1);
    let shutdown_handle = shutdown_tx.clone();
    oneidservice::shutdown::set_shutdown_signal_handler(move || {
        shutdown_handle.send(()).unwrap();
    })
    .map_err(|e| e.display_chain())?;

    _ = oneidservice::service::run(shutdown_tx).await;

    log::info!("OneID daemon is quitting");
    thread::sleep(Duration::from_millis(500));
    Ok(())
}

#[cfg(unix)]
fn running_as_admin() -> bool {
    let uid = unsafe { libc::getuid() };
    uid == 0
}

#[cfg(windows)]
fn running_as_admin() -> bool {
    // TODO: Check if user is administrator correctly on Windows.
    true
}

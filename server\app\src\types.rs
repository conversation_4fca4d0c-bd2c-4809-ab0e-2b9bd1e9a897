use ipnet::{Ipv4AddrRange, Ipv4Net, Ipv6AddrRange, Ipv6Net};
use std::{
    collections::{HashMap, HashSet},
    fmt,
    hash::Hash,
    net::IpAddr,
    ops::{AddAssign, SubAssign},
};
use tlv::Serialize;
use tlv_types::{Port, Resource, ResourceList};
use url::Url;

/// 后端资源结构
#[derive(Clone, serde::Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct BackendResource {
    /// 资源ID
    pub id: String,
    /// 访问地址
    pub url: Option<Url>,
    /// IP地址
    pub ips: Option<Vec<IpAndPort>>,
    /// IP段
    pub ranges: Option<Vec<tlv_types::Range>>,
    /// CIDR IP
    pub cidr: Option<Vec<tlv_types::Cidr>>,
}

impl BackendResource {
    pub fn virtuals(&self) -> Option<HashMap<IpAddr, IpAddr>> {
        self.ips
            .as_deref()
            .map(|ips| {
                let mut maps = HashMap::new();
                for ele in ips {
                    if let Some(virtual_ip) = ele.virtual_ip {
                        maps.insert(virtual_ip, ele.ip);
                    }
                }
                if maps.is_empty() {
                    None
                } else {
                    Some(maps)
                }
            })
            .flatten()
    }
}

impl fmt::Debug for BackendResource {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        f.debug_struct("BackendResource")
            .field("id", &self.id)
            .field("url", &self.url.as_ref().map(|url| url.as_str()))
            .field("ips", &self.ips)
            .field("ranges", &self.ranges)
            .field("cidr", &self.cidr)
            .finish()
    }
}

impl PartialEq for BackendResource {
    fn eq(&self, other: &Self) -> bool {
        self.id == other.id
    }
}

impl Eq for BackendResource {}

impl Hash for BackendResource {
    fn hash<H: std::hash::Hasher>(&self, state: &mut H) {
        self.id.hash(state);
    }
}

/// IP和端口
#[derive(Debug, Clone, serde::Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct IpAndPort {
    /// IP
    pub ip: IpAddr,
    /// 伪IP
    pub virtual_ip: Option<IpAddr>,
    /// 端口
    pub ports: Option<HashSet<Port>>,
}

/// 添加资源MQ消息结构
#[derive(Debug, Clone, serde::Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct AddResource {
    /// 租户
    pub tenant: String,
    #[serde(flatten)]
    pub resource: BackendResource,
}

/// 删除资源MQ消息结构
#[derive(Debug, Clone, serde::Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct DelResource {
    /// 租户
    pub tenant: String,
    /// 资源ID
    pub id: String,
}

impl From<&BackendResource> for Servers {
    fn from(resource: &BackendResource) -> Self {
        Self {
            ips: resource
                .ips
                .as_deref()
                .map(|ip| {
                    ip.iter()
                        .map(|ip| {
                            if let Some(virtual_ip) = ip.virtual_ip {
                                virtual_ip
                            } else {
                                ip.ip
                            }
                        })
                        .collect()
                })
                .unwrap_or_default(),
            range: resource
                .ranges
                .as_deref()
                .map(|ranges| {
                    ranges
                        .iter()
                        .map(|range| (range.start, range.end))
                        .collect()
                })
                .unwrap_or_default(),
            cidr: resource
                .cidr
                .as_deref()
                .map(|cidrs| cidrs.iter().map(|cidr| (cidr.prefix, cidr.ip)).collect())
                .unwrap_or_default(),
        }
    }
}

impl TryFrom<&BackendResource> for tlv_types::TraceabilityResource {
    type Error = ();

    fn try_from(resource: &BackendResource) -> Result<Self, Self::Error> {
        if let (Some(url), Some(ips)) = (&resource.url, &resource.ips) {
            Ok(tlv_types::TraceabilityResource {
                id: resource.id.to_owned(),
                url: url.to_owned(),
                ips: ips
                    .iter()
                    .map(|ip| {
                        if let Some(virtual_ip) = ip.virtual_ip {
                            virtual_ip
                        } else {
                            ip.ip
                        }
                    })
                    .collect(),
            })
        } else {
            Err(())
        }
    }
}

#[derive(serde::Serialize, serde::Deserialize, Default, Debug, Clone)]
pub struct Servers {
    pub ips: HashSet<IpAddr>,
    pub range: HashSet<(IpAddr, IpAddr)>,
    pub cidr: HashSet<(u8, IpAddr)>,
}

impl AddAssign for Servers {
    fn add_assign(&mut self, rhs: Self) {
        self.ips.extend(rhs.ips.into_iter());
        self.cidr.extend(rhs.cidr.into_iter());
        self.range.extend(rhs.range.into_iter());
    }
}

impl SubAssign for Servers {
    fn sub_assign(&mut self, rhs: Self) {
        self.ips.retain(|ip| !rhs.ips.contains(ip));
        self.cidr.retain(|cidr| !rhs.cidr.contains(cidr));
        self.range.retain(|range| !rhs.range.contains(range));
    }
}

impl Servers {
    pub fn as_bytes(&self) -> Vec<u8> {
        let resources: Vec<Resource> = self.into();
        let resources = ResourceList {
            resources: if resources.is_empty() {
                None
            } else {
                Some(resources)
            },
        };
        resources.serialize()
    }
}

impl From<&Servers> for Vec<Resource> {
    fn from(servers: &Servers) -> Self {
        let mut resources = vec![];
        for ip in &servers.ips {
            let resource = match ip {
                IpAddr::V4(ip) => Resource::Ipv4(*ip),
                IpAddr::V6(ip) => Resource::Ipv6(*ip),
            };
            resources.push(resource);
        }
        for (start, end) in &servers.range {
            match (start, end) {
                (IpAddr::V4(start), IpAddr::V4(end)) => {
                    resources.push(Resource::Ipv4Range(Ipv4AddrRange::new(*start, *end)));
                }
                (IpAddr::V6(start), IpAddr::V6(end)) => {
                    resources.push(Resource::Ipv6Range(Ipv6AddrRange::new(*start, *end)));
                }
                (..) => {}
            }
        }
        for (prefix_len, ip) in &servers.cidr {
            match ip {
                IpAddr::V4(ip) => {
                    resources.push(Resource::Ipv4Net(Ipv4Net::new(*ip, *prefix_len).unwrap()));
                }
                IpAddr::V6(ip) => {
                    resources.push(Resource::Ipv6Net(Ipv6Net::new(*ip, *prefix_len).unwrap()));
                }
            }
        }
        resources
    }
}

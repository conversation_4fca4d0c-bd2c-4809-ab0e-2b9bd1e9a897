use std::{env, fs, path::PathBuf, process::Command};

use time::{format_description::well_known::Iso8601, OffsetDateTime};

#[cfg(windows)]
fn make_lang_id(p: u16, s: u16) -> u16 {
    (s << 10) | p
}

fn main() {
    let out_dir = PathBuf::from(env::var_os("OUT_DIR").unwrap());
    fs::write(out_dir.join("git-commit-date.txt"), commit_date()).unwrap();

    #[cfg(windows)]
    {
        // https://github.com/microsoft/windows-rs/issues/1425#issuecomment-1011282564
        println!("cargo:rustc-link-arg=/DELAYLOAD:wlanapi.dll");
        println!("cargo:rustc-link-lib=dylib=delayimp");

        let mut res = winresource::WindowsResource::new();
        let product_version = semver::Version::parse(version::PRODUCT_VERSION).unwrap();

        res.set_version_info(
            winresource::VersionInfo::FILEVERSION,
            product_version.major << 48 | product_version.minor << 32 | product_version.patch << 16,
        );
        res.set("ProductVersion", version::FILE_VERSION);
        res.set_icon("../src-tauri/icons/oneid_icon.ico");
        res.set_language(make_lang_id(
            windows_sys::Win32::System::SystemServices::LANG_CHINESE as u16,
            windows_sys::Win32::System::SystemServices::SUBLANG_CHINESE_SIMPLIFIED as u16,
        ));
        res.set_manifest_file("oneidcore.manifest");
        res.compile().expect("Unable to generate windows resources");
    }

    println!(
        "cargo:rustc-env=BUILD_TIME={}",
        OffsetDateTime::now_local()
            .unwrap()
            .format(&Iso8601::DATE_TIME)
            .unwrap()
    );

    built::write_built_file().expect("Failed to acquire build-time information");
}

fn commit_date() -> String {
    let output = Command::new("git")
        .args(["log", "-1", "--date=short", "--pretty=format:%cd"])
        .output()
        .expect("Unable to get git commit date");
    std::str::from_utf8(&output.stdout)
        .unwrap()
        .trim()
        .to_owned()
}

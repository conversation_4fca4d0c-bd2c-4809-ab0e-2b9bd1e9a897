use core::fmt;
use serde::{Deserialize, Serialize};

/// Event emitted from the states in `oneidcore::tunnel_state_machine` when the tunnel state
/// machine enters a new state.
#[derive(<PERSON><PERSON>, Debug)]
pub enum TunnelStateTransition {
    /// No connection is established and network is unsecured.
    Disconnected,
    /// Network is secured but tunnel is still connecting.
    Connecting,
    /// Tunnel is connected.
    Connected,
    /// Tunnel is disconnected but usually secured by blocking all connections.
    Error(ErrorState),
}

/// Represents the tunnel state machine entering an error state during a [`TunnelStateTransition`].
#[derive(<PERSON>lone, Debug, Serialize, Deserialize, PartialEq, Eq)]
#[serde(rename_all = "snake_case")]
pub struct ErrorState {
    /// Reason why the tunnel state machine ended up in the error state
    cause: ErrorStateCause,
}

impl ErrorState {
    pub fn new(cause: ErrorStateCause) -> Self {
        Self { cause }
    }

    pub fn cause(&self) -> &ErrorStateCause {
        &self.cause
    }
}

/// Reason for the tunnel state machine entering an [`ErrorState`].
#[derive(<PERSON><PERSON>, Debug, <PERSON><PERSON><PERSON>, Deserialize, PartialEq, Eq)]
#[serde(rename_all = "snake_case")]
#[serde(tag = "reason", content = "details")]
pub enum ErrorStateCause {
    /// Failed to configure IPv6 because it's disabled in the platform.
    Ipv6Unavailable,
    /// Failed to set system DNS server.
    SetDnsError,
    SetRouteError,
    /// Failed to start connection to remote server.
    StartTunnelError,
    /// Tunnel parameter missing
    TunnelParameterMissing,
    /// This device is offline, no tunnels can be established.
    IsOffline,
}

impl ErrorStateCause {
    #[cfg(target_os = "macos")]
    pub fn prevents_filtering_resolver(&self) -> bool {
        matches!(self, Self::SetDnsError)
    }
}

impl fmt::Display for ErrorStateCause {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        use self::ErrorStateCause::*;
        let description = match *self {
            Ipv6Unavailable => "Failed to configure IPv6 because it's disabled in the platform",
            SetDnsError => "Failed to set system DNS server",
            SetRouteError => "Failed to set routes",
            StartTunnelError => "Failed to start connection to remote server",
            TunnelParameterMissing => "Tunnel parameter missing",
            IsOffline => "This device is offline, no tunnels can be established",
        };

        write!(f, "{description}")
    }
}

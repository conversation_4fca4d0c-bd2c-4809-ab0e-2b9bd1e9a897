//! 密码相关
use crate::plugins::Result;
use communicate_interface::ControlServiceClient;
use log::trace;
use tauri::{async_runtime::Mutex, State};
use types::backend::{ChangePwdCode, ChangePwdOldPwd};

use crate::plugins::ErrorResponse;

/// 通过验证码修改密码
#[tauri::command(async)]
pub async fn change_password_by_code(
    request: ChangePwdCode,
    rpc: State<'_, Mutex<Option<ControlServiceClient>>>,
) -> Result<String> {
    if request.connection_config.authorization.is_none() {
        log::error!(target: "app", "Missing SPA authorization");
        return Err(ErrorResponse::new(600, None));
    }
    let authorization = request.connection_config.authorization.clone().unwrap();
    trace!(target: "app", "receive change password by code event: {:?}", &authorization.username);

    let rpc_guard = rpc.lock().await;
    let mut rpc = rpc_guard.clone().unwrap();
    drop(rpc_guard);

    match rpc
        .change_pwd_by_code(tonic::Request::new(request.into()))
        .await
    {
        Ok(response) => Ok(response.into_inner()),
        Err(err) => {
            log::error!(target: "app", "Failed to reset password. {:?}", err);
            Err(ErrorResponse::new(600, Some("连接服务失败")))
        }
    }
}

/// 通过旧密码修改密码
#[tauri::command(async)]
pub async fn change_password(
    request: ChangePwdOldPwd,
    rpc: State<'_, Mutex<Option<ControlServiceClient>>>,
) -> Result<()> {
    trace!(target: "app", "Receive change password event: {}", &request.username);

    let rpc_guard = rpc.lock().await;
    let mut rpc = rpc_guard.clone().unwrap();
    drop(rpc_guard);
    match rpc
        .change_pwd_by_old_pwd(tonic::Request::new(request.into()))
        .await
    {
        Ok(_) => Ok(()),
        Err(error) => {
            log::error!(target: "app", "Failed to change password. {}", error);
            Err(ErrorResponse::new(600, Some("连接服务失败")))
        }
    }
}

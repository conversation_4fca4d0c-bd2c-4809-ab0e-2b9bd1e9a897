use cfg_if::cfg_if;
use std::{env, fs, path::PathBuf, process::Command};

use tauri_build::Attributes;
#[cfg(windows)]
use tauri_build::WindowsAttributes;

fn main() {
    #[cfg(windows)]
    {
        // https://github.com/microsoft/windows-rs/issues/1425#issuecomment-1011282564
        println!("cargo:rustc-link-arg=/DELAYLOAD:winbio.dll");
        println!("cargo:rustc-link-lib=dylib=delayimp");
    }

    let out_dir = PathBuf::from(env::var_os("OUT_DIR").unwrap());
    fs::write(out_dir.join("git-commit-date.txt"), commit_date()).unwrap();

    #[allow(unused_mut)]
    let mut attributes = Attributes::default();
    #[cfg(windows)]
    {
        let manifest = include_str!("app.manifest");
        let windows_attributes = WindowsAttributes::new();
        attributes = attributes.windows_attributes(windows_attributes.app_manifest(manifest));
    }

    // sdp = 0x01
    // iam = 0x02
    // cluster = 0x80

    cfg_if! {
        if #[cfg(all(feature="sdp", feature = "iam", feature = "cluster"))] {
            println!("cargo:rustc-env=SDP_MODEL=3C")
        } else if #[cfg(all(feature="sdp", feature = "iam"))] {
            println!("cargo:rustc-env=SDP_MODEL=3S")
        } else if #[cfg(all(feature="sdp", feature = "cluster"))] {
            println!("cargo:rustc-env=SDP_MODEL=1C")
        } else if #[cfg(all(feature="iam", feature = "cluster"))] {
            println!("cargo:rustc-env=SDP_MODEL=2C")
        }else if #[cfg(all(feature="sdp"))] {
            println!("cargo:rustc-env=SDP_MODEL=1S")
        } else {
           println!("cargo:rustc-env=SDP_MODEL=2S")
        }
    };

    tauri_build::try_build(attributes).unwrap_or_else(|error| panic!("{error}"));
}

fn commit_date() -> String {
    let output = Command::new("git")
        .args(["log", "-1", "--date=short", "--pretty=format:%cd"])
        .output()
        .expect("Unable to get git commit date");
    std::str::from_utf8(&output.stdout)
        .unwrap()
        .trim()
        .to_owned()
}

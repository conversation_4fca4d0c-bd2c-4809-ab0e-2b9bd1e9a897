use futures::stream::StreamExt;

use types::tunnel::TunnelStateTransition;

use crate::tunnel_state_machine::{connecting_state::ConnectingState, TunnelCommand};

use super::{
    EventConsequence, SharedTunnelStateValues, TunnelCommandReceiver, TunnelState,
    TunnelStateWrapper,
};

pub struct DisconnectedState;

#[async_trait::async_trait]
impl TunnelState for DisconnectedState {
    type Bootstrap = ();

    #[cfg_attr(
        any(target_os = "windows", target_os = "linux"),
        allow(unused_variables)
    )]
    async fn enter(
        _shared_values: &mut SharedTunnelStateValues,
        _not_need: (),
    ) -> (TunnelStateWrapper, TunnelStateTransition) {
        (
            TunnelStateWrapper::from(DisconnectedState),
            TunnelStateTransition::Disconnected,
        )
    }

    async fn handle_event(
        self,
        commands: &mut TunnelCommandReceiver,
        shared_values: &mut SharedTunnelStateValues,
    ) -> EventConsequence {
        use self::EventConsequence::*;

        match commands.next().await {
            Some(TunnelCommand::Dns(servers)) => {
                // Same situation as allow LAN above.
                shared_values
                    .set_dns_servers(servers)
                    .expect("Failed to reconnect after changing custom DNS servers");

                SameState(self.into())
            }
            Some(TunnelCommand::AskIsOffline(tx)) => {
                let connectivity = shared_values.offline_monitor.connectivity().await;
                _ = tx.send(connectivity.is_offline());
                SameState(self.into())
            }
            Some(TunnelCommand::Down) => {
                _ = shared_values.set_dns_servers(None);
                SameState(self.into())
            }
            Some(TunnelCommand::Connect) => {
                NewState(ConnectingState::enter(shared_values, ()).await)
            }
            None => Finished,
            Some(_) => SameState(self.into()),
        }
    }
}

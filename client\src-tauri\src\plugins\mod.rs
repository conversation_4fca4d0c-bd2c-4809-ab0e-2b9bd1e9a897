use crate::{enable_tray_menu, state::AppState, tunnel, window};
use ::config::AppConfig;
use communicate_interface::ControlServiceClient;
use data_url::DataUrl;
use kuchiki::{traits::TendrilSink, NodeRef};
use markup5ever::{local_name, namespace_url, ns, QualName};
use serde::Serialize;
use std::{ops::Deref, time::Duration};
#[cfg(target_os = "macos")]
use tauri::TitleBarStyle;
use tauri::{App<PERSON><PERSON><PERSON>, Builder, Manager, State, WindowUrl, Wry};
use tokio::sync::{Mutex, RwLock};

mod app;

#[cfg(windows)]
pub mod bio;

#[cfg(feature = "iam")]
pub mod iam;

#[cfg(feature = "cluster")]
pub mod cluster;
mod config;
#[cfg(feature = "sdp")]
pub mod sdp;
mod updater;

/// 返回给前端的错误消息
#[derive(Serialize)]
pub struct ErrorResponse {
    /// 错误码
    pub code: i32,
    /// 消息
    pub msg: Option<String>,
}

impl ErrorResponse {
    pub fn new(code: i32, msg: Option<&str>) -> Self {
        ErrorResponse {
            code,
            msg: msg.map(ToOwned::to_owned),
        }
    }
}

impl From<(i32, Option<String>)> for ErrorResponse {
    fn from(err: (i32, Option<String>)) -> Self {
        ErrorResponse {
            code: err.0,
            msg: err.1,
        }
    }
}

type Result<T> = core::result::Result<T, ErrorResponse>;

#[tauri::command(async)]
async fn create_main_window(
    app_handle: AppHandle<Wry>,
    config: State<'_, Mutex<AppConfig>>,
    app_state: State<'_, RwLock<AppState>>,
    rpc: State<'_, Mutex<Option<ControlServiceClient>>>,
) -> Result<()> {
    let app_state = app_state.read().await;
    let device_id = app_state.device_id.clone().unwrap();
    let system_info = app_state.system_info.clone();
    drop(app_state);

    let app_config = config.lock().await;
    let config = serde_json::to_string(&app_config.deref()).unwrap();
    log::trace!(target: "app", "create main window: {:?}", app_config);
    drop(app_config);

    let rpc_guard = rpc.lock().await;
    let rpc = rpc_guard.clone().unwrap();
    drop(rpc_guard);

    if tokio::time::timeout(
        Duration::from_secs(60),
        tunnel::check_tunnel_state(app_handle.clone(), rpc),
    )
    .await
    .is_err()
    {
        crate::async_exit(&app_handle).await;
        return Ok(());
    }

    // Close splashscreen
    if let Some(splashscreen) = app_handle.get_window("splashscreen") {
        splashscreen.close().unwrap();
    }

    let init_script = format!(
        r#"
    window.CONFIG = {};
    window.DEVICE_ID = "{}";
    window.PRODUCT_NAME = "{}";
    window.PRODUCT_VERSION = "{}";
    window.USER_AGENT = "{}";
    "#,
        config,
        device_id,
        &app_handle.package_info().name,
        version::PRODUCT_VERSION,
        base::generate_ua(&system_info),
    );

    window::create_main_window(&app_handle, init_script);

    enable_tray_menu(&app_handle);
    Ok(())
}

#[tauri::command(async)]
pub async fn user_agreement(app_handle: AppHandle<Wry>, mut url: WindowUrl) -> Result<()> {
    const LABEL: &str = "user_agreement";
    if let Some(window) = app_handle.get_window(LABEL) {
        _ = window.unminimize();
        _ = window.show();
        _ = window.set_focus();
        return Ok(());
    }

    if let WindowUrl::External(url) = &mut url {
        if url.scheme() == "data" {
            if let Ok(data_url) = DataUrl::process(url.as_str()) {
                let (body, _) = data_url.decode_to_vec().unwrap();
                let html = String::from_utf8_lossy(&body).into_owned();

                if html.contains('<') && html.contains('>') {
                    let document = kuchiki::parse_html().one(html);

                    if let Ok(ref node) = document.select_first("script") {
                        let node = node.as_node();
                        node.detach();
                    }

                    let node = NodeRef::new_element(
                        QualName::new(None, ns!(html), local_name!("script")),
                        None,
                    );

                    node.append(NodeRef::new_text(String::from(
                        r#"
document.addEventListener("contextmenu", (event) =>
    event.preventDefault()
);
document.addEventListener("keydown", (e) => {
    if (e.code === "F5") e.preventDefault();
    if (e.ctrlKey && e.code === "KeyP") e.preventDefault();
    if (e.ctrlKey && e.code === "KeyR") e.preventDefault();
});

if (window.__TAURI__) {
    const stylesheet = new CSSStyleSheet();
    stylesheet.replaceSync(
        "body { overflow-x: hidden; } .title { display: none; } .content { margin: 0; border: none; width: 934px; }"
    );
    document.adoptedStyleSheets = [stylesheet];
}

function openHomePage(url) {
    if (window.__TAURI__) {
        const {open} = window.__TAURI__.shell;
        open(url);
    } else {
        window.open(url, "_blank");
    }
}
"#,
                    )));

                    if let Ok(ref body) = document.select_first("body") {
                        body.as_node().append(node);
                    }

                    url.set_path(&format!("text/html,{}", document.to_string()));
                }
            }
        }
    }

    let builder = tauri::window::WindowBuilder::new(&app_handle, LABEL.to_string(), url)
        .title("用户协议")
        .center()
        .fullscreen(false)
        .resizable(false)
        .maximizable(false)
        .minimizable(false)
        .skip_taskbar(false)
        .decorations(true)
        .focused(true);

    #[cfg(target_os = "macos")]
    let builder = builder
        .content_protected(true)
        .accept_first_mouse(true)
        .title_bar_style(TitleBarStyle::Transparent);

    _ = builder.inner_size(974.0, 654.0).build();
    Ok(())
}

pub fn commands(builder: Builder<Wry>) -> Builder<Wry> {
    builder.invoke_handler(tauri::generate_handler![
        app::graceful_exit,
        app::reset,
        app::has_logged_in,
        app::set_logged_in,
        config::patch,
        create_main_window,
        user_agreement,
        updater::check_update,
        updater::download,
        updater::install,
        updater::cancel_update,
    ])
}

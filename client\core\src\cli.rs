use clap::{arg, Parser, ValueEnum};
use std::path::PathBuf;

use crate::build_info::version_info;

/// OneID 核心服务
#[derive(Parser, Debug)]
#[clap(name = ABOUT, about = ABOUT, long_about = None, version = version_info())]
pub struct AppArgs {
    /// Log filter.
    #[arg(short)]
    pub filter: String,

    /// Running mode.
    #[arg(short, value_enum, default_value_t = Mode::Standalone)]
    pub mode: Mode,

    /// Resources dir.
    #[arg(short)]
    pub resources_dir: PathBuf,
}

#[derive(Debug, Copy, Clone, PartialEq, Eq, PartialOrd, Ord, ValueEnum)]
pub enum Mode {
    Standalone,
    Embed,
}

static ABOUT: &str = "OneID Core";

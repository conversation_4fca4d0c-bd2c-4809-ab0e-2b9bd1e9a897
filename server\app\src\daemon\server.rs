use std::{fs, io, str::FromStr};

use tokio::{
    io::{AsyncBufReadExt, AsyncWriteExt, BufReader},
    net::UnixListener,
    sync::{
        mpsc,
        oneshot::{self, Sender},
    },
};
use tracing::{error, info, Instrument};
use tracing_subscriber::{reload::Handle, EnvFilter, Registry};

use crate::{
    cli::Command,
    daemon::{OnlineClient, SOCK_DIR, SOCK_PATH},
    traceability::TRACEABILITY_VIRTUAL_IP_MAPPING,
    EventSender, CLIENTS,
};

pub async fn serve(
    reload_handle: Handle<EnvFilter, Registry>,
    event_sender: EventSender,
) -> io::Result<()> {
    cleanup_socket()?;
    mkdir_socket()?;

    let listener = UnixListener::bind(SOCK_PATH)?;
    info!("listening on {}", SOCK_PATH);

    let (tx, mut rx) = mpsc::channel::<(String, Sender<bool>)>(1);

    let realod_handle_cloned = reload_handle.clone();
    tokio::spawn(
        async move {
            while let Some((filter, callback)) = rx.recv().await {
                if let Ok(new_filter) = EnvFilter::from_str(&filter) {
                    let result = reload_handle.modify(|filter| {
                        *filter = new_filter;
                    });

                    _ = callback.send(result.is_ok());

                    if result.is_ok() {
                        info!(r#"apply log filter `{}`"#, filter);
                    }
                } else {
                    _ = callback.send(false);
                }
            }
        }
        .in_current_span(),
    );

    tokio::spawn(async move {
        loop {
            match listener.accept().await {
                Ok((mut socket, _addr)) => {
                    let reload_filter_producer = tx.clone();
                    let realod_handle_cloned = realod_handle_cloned.clone();
                    let event_sender = event_sender.clone();
                    tokio::spawn(async move {
                        let (reader, mut writer) = socket.split();
                        let mut reader = BufReader::new(reader);
                        let mut line = String::new();

                        while let Ok(n) = reader.read_line(&mut line).await {
                            if n == 0 {
                                break;
                            }
                            let command = line.trim();

                            let mut response = String::from("Unknown command");

                            if let Ok(command) = serde_json::from_str::<Command>(command) {
                                match command {
                                    // 退出
                                    Command::Quit => {
                                        response.clear();
                                        response.push_str("Quitting");
                                        event_sender
                                            .send(crate::InternalEvent::TriggerShutdown)
                                            .await;
                                    }
                                    // 查看日志级别
                                    Command::ViewLogFilter => {
                                        _ = realod_handle_cloned.with_current(|current| {
                                            response = format!("{current}");
                                        });
                                    }
                                    // 设置日志级别
                                    Command::SetLogFilter { filter } => {
                                        let (tx, rx) = oneshot::channel();
                                        _ = reload_filter_producer
                                            .send((filter.to_string(), tx))
                                            .await;

                                        if rx.await.unwrap_or_default() {
                                            response.clear();
                                            response.push_str("Log level changed");
                                        }
                                    }
                                    // 查看客户端连接信息
                                    Command::ViewClients => {
                                        let clients = CLIENTS.read().await;
                                        let times = (clients.len() / 1000) + 1;
                                        for i in 0..times {
                                            let mut values = vec![];
                                            let mut index = 0;
                                            for (device_id, client) in clients.iter().skip(i * 1000)
                                            {
                                                values.push(OnlineClient {
                                                    device_id: device_id.clone(),
                                                    unit: client.tenant.clone(),
                                                    username: client.username.clone(),
                                                    peer_addr: client.peer_addr,
                                                    online: client.online,
                                                });
                                                index += 1;
                                                if index == 1000 {
                                                    break;
                                                }
                                            }
                                            response =
                                                serde_json::to_string(&values).unwrap_or_default();
                                            if i + 1 == times {
                                                response.push_str("END");
                                            }
                                            response.push_str("\n");
                                            _ = writer.write_all(response.as_bytes()).await;
                                        }
                                        break;
                                    }
                                    // 查看溯源资源映射信息
                                    Command::ViewTraceMapping => {
                                        let mapping = TRACEABILITY_VIRTUAL_IP_MAPPING.read().await;
                                        response =
                                            serde_json::to_string(&*mapping).unwrap_or_default();
                                    }
                                    _ => (),
                                }
                            }

                            response.push_str("\n");
                            _ = writer.write_all(response.as_bytes()).await;
                        }
                    });
                }
                Err(err) => {
                    error!("daemon thread exit. {err}");
                    break;
                }
            }
        }
    });

    Ok(())
}

fn cleanup_socket() -> Result<(), std::io::Error> {
    if fs::exists(SOCK_PATH)? {
        fs::remove_file(SOCK_PATH)?;
    }
    Ok(())
}

fn mkdir_socket() -> Result<(), std::io::Error> {
    if !fs::exists(SOCK_DIR)? {
        fs::create_dir_all(SOCK_DIR)?;
    }
    Ok(())
}

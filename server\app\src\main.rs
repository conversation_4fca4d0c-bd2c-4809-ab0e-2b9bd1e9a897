use std::{
    collections::{HashMap, HashSet},
    io,
    net::{IpAddr, SocketAddr},
    sync::Arc,
    time::Duration,
};

use crate::xdp::XdpCommand;
use arp::ArpHandle;
use backend::client::BackendCommonClient;
use base::packet::{Message, MessageType};
use cache::CacheManager;
use clap::Parser;
use cli::AppArgs;
use comm::{ClientHandle, CommCommand};
use config::{Config, Segments};
use enums::CloseReason;

use flume::Receiver;
use message::MQCommand;
use moka::{
    future::{Cache, FutureExt},
    notification::{ListenerFuture, RemovalCause},
};
use nat::{LightNat, Nat};
use network::Network;
use once_cell::sync::Lazy;
use packet::server::ServerPacketSender;
use serde_json::Value;
use spa::SPAValidator;
use tlv::Serialize;
use tlv_types::{IncrResourceList, TraceabilityResourcesVirtualIps, VirtualIpAddr};
use tokio::sync::RwLock;
use tokio_util::sync::CancellationToken;
use tracing::{debug, error, info, trace, warn, Instrument};
use types::Servers;
use virtual_iface::ReqSender;
use xdp::XdpCommandSender;

use crate::{
    comm::listen::CommArgs,
    message::MqArgs,
    packet::{client::ClientForwardInitArgs, server::ServerForwardInitArgs},
    types::BackendResource,
};

mod arp;
#[allow(dead_code)]
mod backend;
mod build_info;
#[allow(dead_code)]
mod cache;
mod cli;
mod comm;
#[allow(dead_code)]
mod config;
mod constants;
mod daemon;
mod enums;
mod logging;
mod message;
mod nat;
mod network;
mod packet;
mod shutdown;
mod spa;
mod traceability;
mod traffic;
mod types;
mod virtual_iface;
mod xdp;

#[derive(thiserror::Error, Debug)]
pub enum Error {
    #[error("Bpf error. {0}")]
    BpfError(#[from] aya::EbpfError),

    #[error("Ebpf program error. {0}")]
    EbpfProgramError(#[from] aya::programs::ProgramError),

    #[error("Ebpf map error. {0}")]
    EbpfMapError(#[from] aya::maps::MapError),

    #[error("IO error. {0}")]
    IoError(#[from] io::Error),

    #[error("Failed to create ARP client for interface {0}. {1}")]
    ArpError(String, #[source] io::Error),

    #[error("Failed to create MQ client. {0}")]
    MqBuildError(#[from] deadpool_lapin::BuildError),

    #[error("Failed to create MQ consumer. {0}")]
    MqConsumerError(String),

    #[error("Failed to create MQ producer. {0}")]
    MqProducerError(String),
}

#[allow(dead_code)]
struct Client {
    tenant: String,
    username: String,
    peer_addr: SocketAddr,
    handle: ClientHandle,
    /// 在线时间
    online: u64,
}

/// 客户端连接缓存, key为设备ID
static CLIENTS: Lazy<RwLock<HashMap<String, Client>>> =
    Lazy::new(|| RwLock::new(HashMap::default()));

/// 虚拟IP映射
///
/// {tenant: {virtual_ip: real_ip}}
static VIRTUAL_IP_MAPPING: Lazy<RwLock<HashMap<String, HashMap<IpAddr, IpAddr>>>> =
    Lazy::new(|| RwLock::new(Default::default()));

pub enum InternalEvent {
    /// MQ消息
    MQMessage(MQCommand),
    // /// 转发数据包
    // ForwardPacket(ForwardPacket),
    /// 客户端接入
    PostClientOnline {
        device_id: String,
        ip: IpAddr,
        tenant: String,
        username: String,
        env: Value,
        peer_addr: SocketAddr,
        handle: ClientHandle,
    },
    /// 关闭客户端
    PostClientOffline {
        device_id: String,
        peer_addr: SocketAddr,
    },
    // /// 通信指令
    // CommCommand {
    //     device_id: String,
    //     command: CommCommand,
    // },
    /// 广播命令
    BroadcastCommand(CommCommand),
    /// 后端服务启动后加载的资源列表
    LoadedResource(HashMap<String, HashSet<BackendResource>>),
    /// 关闭
    TriggerShutdown,
}

#[derive(Clone)]
pub struct EventSender(flume::Sender<InternalEvent>);

impl EventSender {
    pub async fn send(&self, event: InternalEvent) {
        if self.0.send_async(event).await.is_err() {
            error!("server already down or thread panicked.");
        }
    }
}

fn main() {
    let start = std::time::Instant::now();
    // 解析参数
    let args = AppArgs::parse();

    let (config, log_dir, filter) = match args.command {
        cli::Command::Serve {
            config,
            log_dir,
            filter,
        } => (config, log_dir, filter),
        command => {
            if let Err(e) = daemon::execute_command(command) {
                eprintln!("{e}");
            }
            return;
        }
    };

    let (token, reload_handle) = logging::init(&log_dir, &filter);

    let future = async {
        let shutdown_token = CancellationToken::new();
        let token = shutdown_token.clone();
        shutdown::set_shutdown_signal_handler(move || {
            token.cancel();
        })
        .unwrap();

        let config = match config::load(config) {
            Ok(config) => config,
            Err(err) => {
                error!("{err}");
                std::process::exit(1);
            }
        };

        let networks = network::info(&config.interface);
        info!("network interface information: {networks:#?}");

        // 全局事件通道
        let (event_sender, event_rx) = flume::unbounded();
        let event_sender = EventSender(event_sender);

        // 创建网卡
        let segments = config.segments();
        let mut light_nat = None;
        let mut req_sender = None;
        let virtual_ip = if let Some((ip, src_ip)) = segments.virtual_ip() {
            let (sender, nat) = match virtual_iface::open(
                &config.interface.r#virtual.name,
                ip,
                src_ip,
                shutdown_token.child_token(),
            )
            .await
            {
                Ok(r) => r,
                Err(err) => {
                    error!("{err}");
                    std::process::exit(1);
                }
            };
            req_sender = Some(sender);
            light_nat = Some(nat);
            Some(IpAddr::V4(ip))
        } else {
            None
        };

        // 启动ARP
        let addresses = config.interface.primary.multi_nic_addresses.clone();
        let arp_handle = match arp::spawn(virtual_ip, &networks, addresses.clone()).await {
            Ok(r) => r,
            Err(err) => {
                error!("{err}");
                std::process::exit(1);
            }
        };

        daemon::serve(reload_handle, event_sender.clone())
            .await
            .unwrap();

        // 等待后台启动
        let backend_client = BackendCommonClient::new(config.backend.clone());
        let backend_client = Arc::new(backend_client);
        backend::setup(
            config.backend_interval,
            backend_client.clone(),
            arp_handle.clone(),
            segments.clone(),
            event_sender.clone(),
        )
        .await;

        let server = match create_server(
            config,
            networks,
            event_sender,
            event_rx,
            req_sender,
            light_nat,
            arp_handle,
            backend_client,
            shutdown_token,
        )
        .await
        {
            Ok(server) => server,
            Err(err) => {
                error!("{err}");
                std::process::exit(1);
            }
        };

        info!(elapsed = ?start.elapsed(), "started.");

        server.run().await;

        info!(elapsed = ?start.elapsed(), "stopped.");

        std::process::exit(0);
    };

    tokio::runtime::Builder::new_multi_thread()
        .enable_all()
        .thread_name("sdp")
        .build()
        .unwrap()
        .block_on(future.instrument(tracing::error_span!("Application")));
    drop(token);
}

async fn create_server(
    config: Config,
    networks: Vec<Network>,
    event_sender: EventSender,
    event_rx: Receiver<InternalEvent>,
    req_sender: Option<ReqSender>,
    light_nat: Option<Arc<RwLock<LightNat>>>,
    arp_handle: ArpHandle,
    backend_client: Arc<BackendCommonClient>,
    shutdown_token: CancellationToken,
) -> Result<Server, Error> {
    let primary_interface = config.interface.primary.base.iface.clone();
    let addresses = config.interface.primary.multi_nic_addresses.clone();
    let segments = config.segments();
    let pub_ports = config.pub_ports();
    let resource_whitelist = config.resource_whitelist();

    let (server_packet_sender, server_packet_receiver) = ServerPacketSender::new();

    // 启动ebpf程序
    let (client_packet_senders, xdp_command_senders) = xdp::start_ebpf(
        config.interface,
        segments.clone(),
        server_packet_sender,
        config.spa_port,
        shutdown_token.child_token(),
    );

    info!("creating nat table");
    // NAT
    let nat = {
        let mut used_ports = HashSet::default();
        pub_ports.iter().for_each(|port| match port {
            tlv_types::Port::Single(port) => {
                used_ports.insert(*port);
            }
            tlv_types::Port::Range(range) => {
                used_ports.extend((*range.start())..=(*range.end()));
            }
        });
        // SPA和TCP端口
        used_ports.insert(config.spa_port);
        used_ports.insert(config.server.port);
        let nat = Nat::new(xdp_command_senders.clone(), used_ports);
        Arc::new(nat)
    };

    info!("creating spa cache");
    // SPA 认证缓存
    let primary_xdp_sender = xdp_command_senders.primary_xdp_sender();
    let spa_cache = Arc::new(
        Cache::builder()
            .time_to_idle(Duration::from_secs(15))
            .async_eviction_listener(move |k, _v, cause| -> ListenerFuture {
                let primary_xdp_sender = primary_xdp_sender.clone();
                async move {
                    if cause != RemovalCause::Replaced {
                        debug!(k = ?*k, "evict spa cache");
                        primary_xdp_sender.send(XdpCommand::ClosePort(*k)).await;
                    }
                }
                .boxed()
            })
            .build(),
    );

    // SPA 防重放缓存
    let primary_xdp_sender = xdp_command_senders.primary_xdp_sender();
    let anti_replay = Arc::new(
        Cache::builder()
            .time_to_live(Duration::from_secs(15))
            .async_eviction_listener(move |_k, v: SocketAddr, _cause| -> ListenerFuture {
                let primary_xdp_sender = primary_xdp_sender.clone();
                async move {
                    primary_xdp_sender
                        .send(XdpCommand::DelSPARecord(v.ip(), v.port()))
                        .await;
                }
                .boxed()
            })
            .build(),
    );

    // Redis 缓存
    let cache_manager = CacheManager::new(config.redis).await;

    let spa_validator = SPAValidator {
        sec_key: config.sec_key,
        time_deviation: config.spa_time_deviation,
        xdp_command_sender: xdp_command_senders.primary_xdp_sender(),
        cache_manager: cache_manager.clone(),
        backend_client: backend_client.clone(),
        preauthed: spa_cache.clone(),
        anti_replay: anti_replay.clone(),
    };

    // 服务端数据包
    let args = ServerForwardInitArgs {
        spa_port: config.spa_port,
        spa_validator: Arc::new(spa_validator),
        nat: nat.clone(),
        primary_xdp_sender: xdp_command_senders.primary_xdp_sender(),
        packet_receiver: server_packet_receiver,
        shutdown_token: shutdown_token.child_token(),
    };
    packet::server::spawn(args).await;

    // 客户端数据包
    let args = ClientForwardInitArgs {
        networks,
        client_packet_senders,
        xdp_command_senders: xdp_command_senders.clone(),
        arp_handle: arp_handle.clone(),
        nat: nat.clone(),

        primary_interface,
        addresses,
        segments: segments.clone(),
        req_sender,
        shutdown_token: shutdown_token.child_token(),
    };
    let client_packet_handle = packet::client::spawn(args);

    // MQ消息通知
    let (session_traffic_tx, session_traffic_rx) = tokio::sync::mpsc::channel(16);
    let (session_resource_traffic_tx, session_resource_traffic_rx) = tokio::sync::mpsc::channel(16);
    let args = MqArgs {
        url: config.mq.url,
        event_sender: event_sender.clone(),
        session_traffic_rx,
        session_resource_traffic_rx,
        shutdown_token: shutdown_token.child_token(),
    };

    let mq_handle = message::spawn(args).await?;

    // 推送流量统计信息
    traffic::spawn(
        config.push_traffic_interval,
        session_traffic_tx,
        session_resource_traffic_tx,
        shutdown_token.child_token(),
    );

    // 启动TLCP服务
    let resource_whitelist = Some(Arc::new(resource_whitelist));
    let comm_args = CommArgs {
        authorization_interval: config.authorization_interval,
        event_sender: event_sender.clone(),
        primary_xdp_sender: xdp_command_senders.primary_xdp_sender(),
        keepalive: config.keepalive,
        server_config: config.server,
        dns_servers: config.dns,
        cache_manager: cache_manager.clone(),
        client_packet_handle,
        backend_client,
        mq_handle,
        resource_whitelist: resource_whitelist.clone(),
        spa_cache,
        shutdown_token: shutdown_token.child_token(),
    };
    comm::listen::spawn(comm_args).await;

    Ok(Server {
        rx: event_rx,
        primary_xdp_sender: xdp_command_senders.primary_xdp_sender(),
        resource_whitelist,
        nat,
        light_nat,
        resources: HashMap::new(),
        segments,
        arp_handle,
        shutdown_token,
    })
}

struct Server {
    rx: flume::Receiver<InternalEvent>,
    primary_xdp_sender: XdpCommandSender,
    resource_whitelist: Option<Arc<HashSet<SocketAddr>>>,
    nat: Arc<Nat>,
    light_nat: Option<Arc<RwLock<LightNat>>>,
    /// 资源列表, 包含虚拟映射中的虚拟地址列表
    resources: HashMap<String, HashSet<BackendResource>>,
    segments: Segments,
    arp_handle: ArpHandle,
    shutdown_token: CancellationToken,
}

impl Server {
    async fn run(mut self) {
        loop {
            if let Ok(event) = self.rx.recv_async().await {
                match event {
                    InternalEvent::MQMessage(command) => {
                        debug!(?command, "mqcommand");
                        self.handle_mq_command(command).await;
                    }
                    InternalEvent::PostClientOnline {
                        ip,
                        device_id,
                        tenant,
                        username,
                        env,
                        peer_addr,
                        handle,
                    } => {
                        self.primary_xdp_sender
                            .send(XdpCommand::Connected(peer_addr.ip(), peer_addr.port()))
                            .await;

                        self.post_new_client(ip, &tenant, &username, &env, &handle)
                            .await;

                        let timestamp = coarsetime::Clock::now_since_epoch().as_millis();
                        let mut clients = CLIENTS.write().await;
                        if let Some(client) = clients.insert(
                            device_id.clone(),
                            Client {
                                tenant,
                                username,
                                peer_addr,
                                handle,
                                online: timestamp,
                            },
                        ) {
                            warn!(device = device_id, pre_addr = %client.peer_addr, "Repeat connection");
                            client.handle.send(CommCommand::Kill).await;
                        }
                    }
                    InternalEvent::PostClientOffline {
                        device_id,
                        peer_addr,
                    } => {
                        self.primary_xdp_sender
                            .send(XdpCommand::DelSPARecord(peer_addr.ip(), peer_addr.port()))
                            .await;

                        self.primary_xdp_sender
                            .send(XdpCommand::CloseConnect(peer_addr.ip(), peer_addr.port()))
                            .await;

                        let mut clients = CLIENTS.write().await;
                        if let Some(client) = clients.get(&device_id) {
                            if client.peer_addr != peer_addr {
                                continue;
                            }
                        }

                        _ = clients.remove(&device_id);
                        drop(clients);

                        self.nat.delete_by_device_id(&device_id).await;

                        if let Some(light_nat) = &self.light_nat {
                            let mut light_nat = light_nat.write().await;
                            light_nat.delete_by_device_id(&device_id).await;
                            drop(light_nat);
                        }
                    }
                    InternalEvent::BroadcastCommand(command) => {
                        let clients = CLIENTS.read().await;
                        for client in clients.values() {
                            client.handle.send(command.clone()).await;
                        }
                    }
                    InternalEvent::LoadedResource(resources) => {
                        self.resources = resources;
                    }
                    InternalEvent::TriggerShutdown => break,
                }
            }
        }

        trace!("signal received, shutting down");

        let token = self.shutdown();
        token.cancel();
        Self::cleanup().await;
    }

    async fn cleanup() {
        let clients = CLIENTS.read().await;

        trace!("waiting for {} connections to close", clients.len());

        for client in clients.values() {
            let message = Message::new(
                MessageType::CloseReason,
                &(CloseReason::SERVER as u8).serialize(),
            );
            client
                .handle
                .send(CommCommand::Message(message.clone()))
                .await;
            client.handle.send(CommCommand::Kill).await;
        }
        drop(clients);

        tokio::time::sleep(tokio::time::Duration::from_millis(50)).await;
    }

    fn shutdown(self) -> CancellationToken {
        self.shutdown_token
    }

    async fn handle_mq_command(&mut self, command: MQCommand) {
        match command {
            MQCommand::AddResource { tenant, resource } => {
                self.handle_add_resource(tenant, resource).await;
            }
            MQCommand::DelResource { tenant, resource } => {
                self.handle_del_resource(tenant, resource).await;
            }
            MQCommand::UserState {
                reason,
                tenant,
                username,
            } => {
                let reason = CloseReason::from_mq(reason);
                debug!(username = &username, state = ?reason, "user state change");

                let mut clients = CLIENTS.write().await;

                let message = Message::new(MessageType::CloseReason, &(reason as u8).serialize());
                let mut device_ids = vec![];
                for (device_id, client) in clients.iter() {
                    if client.tenant == tenant && client.username == username {
                        client
                            .handle
                            .send(CommCommand::Message(message.clone()))
                            .await;
                        client.handle.send(CommCommand::Kill).await;

                        device_ids.push(device_id.to_owned());
                    }
                }
                device_ids.iter().for_each(|device_id| {
                    _ = clients.remove(device_id);
                });
            }
            MQCommand::UserPasswordChange {
                device_id,
                username,
                tenant,
            } => {
                debug!(username = &username, "user password change");

                let mut clients = CLIENTS.write().await;
                let message = Message::new(
                    MessageType::CloseReason,
                    &(CloseReason::UserPwdChanged as u8).serialize(),
                );
                let mut device_ids = vec![];
                for (_device_id, client) in clients.iter() {
                    if client.tenant == tenant && client.username == username {
                        if &device_id != _device_id {
                            client
                                .handle
                                .send(CommCommand::Message(message.clone()))
                                .await;
                            client.handle.send(CommCommand::Kill).await;
                            device_ids.push(_device_id.to_owned());
                        }
                    }
                }
                device_ids.iter().for_each(|device_id| {
                    _ = clients.remove(device_id);
                });
            }
            MQCommand::DisableDevice(device_id) => {
                debug!(device = &device_id, "device disabled");

                let mut clients = CLIENTS.write().await;
                if let Some(client) = clients.remove(&device_id) {
                    let message = Message::new(
                        MessageType::CloseReason,
                        &(CloseReason::DeviceDisabled as u8).serialize(),
                    );
                    client.handle.send(CommCommand::Message(message)).await;
                    client.handle.send(CommCommand::Kill).await;
                }
            }
            MQCommand::EndSession(device_id) => {
                debug!(device = &device_id, "end session");

                let mut clients = CLIENTS.write().await;
                if let Some(client) = clients.remove(&device_id) {
                    let message = Message::new(
                        MessageType::CloseReason,
                        &(CloseReason::EndSession as u8).serialize(),
                    );
                    client.handle.send(CommCommand::Message(message)).await;
                    client.handle.send(CommCommand::Kill).await;
                }
            }
            MQCommand::DisableTenant(tenant) => {
                debug!(tenant = &tenant, "tenant disabled");
                let mut clients = CLIENTS.write().await;
                let message = Message::new(
                    MessageType::CloseReason,
                    &(CloseReason::TenantDisabled as u8).serialize(),
                );
                let mut device_ids = vec![];
                for (_device_id, client) in clients.iter() {
                    if client.tenant == tenant {
                        client
                            .handle
                            .send(CommCommand::Message(message.clone()))
                            .await;
                        client.handle.send(CommCommand::Kill).await;
                        device_ids.push(_device_id.to_owned());
                    }
                }
                device_ids.iter().for_each(|device_id| {
                    _ = clients.remove(device_id);
                });
            }
            #[allow(unused_variables)]
            MQCommand::LoginElseWhere { ip, device_id } => {
                let mut clients = CLIENTS.write().await;
                if let Some(client) = clients.remove(&device_id) {
                    let message = Message::new(
                        MessageType::CloseReason,
                        &(CloseReason::LoginElseWhere as u8).serialize(),
                    );
                    client.handle.send(CommCommand::Message(message)).await;
                    client.handle.send(CommCommand::Kill).await;
                }
            }
            MQCommand::EndSessionAutomatically {
                unit,
                username,
                device_id,
            } => {
                let mut clients = CLIENTS.write().await;
                if let Some(client) = clients.remove(&device_id) {
                    if client.tenant == unit && client.username == username {
                        let message = Message::new(
                            MessageType::CloseReason,
                            &(CloseReason::EndSessionAutomatically as u8).serialize(),
                        );
                        client.handle.send(CommCommand::Message(message)).await;
                        client.handle.send(CommCommand::Kill).await;
                    } else {
                        clients.insert(device_id, client);
                    }
                }
            }
        }
    }

    #[allow(unused_variables)]
    async fn post_new_client(
        &self,
        ip: IpAddr,
        tenant: &str,
        username: &str,
        env: &Value,
        handle: &ClientHandle,
    ) {
        // self.xdp_handle
        //     .send_async(XdpCommand::AllowConnect(false, ip))
        //     .await;

        // 发送资源白名单
        if let Some(ref resource_whitelist) = self.resource_whitelist {
            debug!(resources = ?resource_whitelist, "sending whitelist");
            let whitelist = tlv_types::Whitelist {
                ips: Some(resource_whitelist.iter().map(|addr| addr.ip()).collect()),
            };

            let message = Message::new(MessageType::ResourceWhitelist, &whitelist.serialize());
            handle.send(CommCommand::Message(message)).await;
        }

        if let Some(unit_resources) = self.resources.get(tenant) {
            // 溯源资源列表
            let mut traceability_resources = vec![];

            // 所有IP列表
            let mut servers = Servers::default();
            for resource in unit_resources {
                if let Ok(traceability_resource) = resource.try_into() {
                    traceability_resources.push(traceability_resource);
                }
                servers += resource.into();
            }
            let traceability_resources = tlv_types::TraceabilityResources {
                flag: true,
                resources: traceability_resources,
            };
            let message = Message::new(
                MessageType::TraceabilityResources,
                &traceability_resources.serialize(),
            );
            handle.send(CommCommand::Message(message)).await;

            // 资源列表
            debug!(resources = ?servers, "sending resources");
            let bytes = servers.as_bytes();
            let message = Message::new(MessageType::ServerList, &bytes);
            handle.send(CommCommand::Message(message)).await;
        }

        // 溯源资源虚拟IP
        use crate::traceability::TRACEABILITY_REAL_IP_MAPPING;
        let real_mapping = TRACEABILITY_REAL_IP_MAPPING.read().await;

        let mut virtual_resources = TraceabilityResourcesVirtualIps {
            flag: true,
            mapping: Default::default(),
        };
        for (addr, virtual_ip) in real_mapping.iter() {
            virtual_resources.mapping.push(VirtualIpAddr {
                real: addr.ip(),
                virtual_ip: *virtual_ip,
            });
        }

        let message = Message::new(
            MessageType::TraceabilityResourcesVirtualIps,
            &virtual_resources.serialize(),
        );
        handle.send(CommCommand::Message(message)).await;
    }

    async fn handle_add_resource(&mut self, tenant: String, resource: BackendResource) {
        // 获取资源对应IP地址的Mac地址
        if let Some(ips) = &resource.ips {
            for ip in ips {
                let iface = self.segments.find_interface(&ip.ip);
                self.arp_handle.send(iface, ip.ip).await;
            }
        }

        let mut commands = vec![];

        // 新的资源IP地址
        let servers: Servers = (&resource).into();

        // 溯源资源
        if let Ok(traceability_resource) = (&resource).try_into() {
            let resources = vec![traceability_resource];
            if let Some(mapping) = traceability::add_resources(resources.iter()).await {
                let traceability_resources = tlv_types::TraceabilityResources {
                    flag: true,
                    resources,
                };
                let traceability_command = CommCommand::Message(Message::new(
                    MessageType::TraceabilityResources,
                    &traceability_resources.serialize(),
                ));

                let virtual_apps = TraceabilityResourcesVirtualIps {
                    flag: true,
                    mapping: mapping
                        .into_iter()
                        .map(|(real, virtual_ip)| VirtualIpAddr { real, virtual_ip })
                        .collect(),
                };
                let message = Message::new(
                    MessageType::TraceabilityResourcesVirtualIps,
                    &virtual_apps.serialize(),
                );
                let command = CommCommand::Message(message);
                commands.push(traceability_command);
                commands.push(command);
            }
        }

        // 添加虚拟IP映射
        let resource_virtual_ips = resource.virtuals();
        let mut virtual_ip_mapping = VIRTUAL_IP_MAPPING.write().await;
        let inner_map = virtual_ip_mapping.entry(tenant.clone()).or_default();
        let unit_resources = self.resources.entry(tenant.clone()).or_default();

        if let Some(previous_resource) = unit_resources.replace(resource) {
            let previous_servers: Servers = (&previous_resource).into();
            let mut removed_servers = previous_servers.clone();
            removed_servers -= servers.clone();
            let mut added_servers = servers;
            added_servers -= previous_servers;

            // 需要删除的IP
            let removed_resources: Vec<tlv_types::Resource> = (&removed_servers).into();
            if !removed_resources.is_empty() {
                let resource_list = IncrResourceList {
                    flag: false,
                    resources: Some(removed_resources),
                };
                let message =
                    Message::new(MessageType::IncrementalServer, &resource_list.serialize());
                commands.push(CommCommand::Message(message));
            }

            // 需要添加的IP
            let added_resources: Vec<tlv_types::Resource> = (&added_servers).into();
            if !added_resources.is_empty() {
                let resource_list = IncrResourceList {
                    flag: true,
                    resources: Some(added_resources),
                };
                let message =
                    Message::new(MessageType::IncrementalServer, &resource_list.serialize());
                commands.push(CommCommand::Message(message));
            }

            // 删除之前的虚拟IP映射
            let previous_virtual_ips = previous_resource.virtuals();
            if resource_virtual_ips != previous_virtual_ips {
                if let Some(previous_virtual_ips) = previous_resource.virtuals() {
                    previous_virtual_ips.keys().for_each(|virtual_ip| {
                        inner_map.remove(virtual_ip);
                    });
                }
            }
        } else {
            // 需要添加的IP
            let added_resources: Vec<tlv_types::Resource> = (&servers).into();
            let resource_list = IncrResourceList {
                flag: true,
                resources: Some(added_resources),
            };
            let message = Message::new(MessageType::IncrementalServer, &resource_list.serialize());
            commands.push(CommCommand::Message(message));
        }
        if let Some(virtual_ips) = resource_virtual_ips {
            inner_map.extend(virtual_ips);
        }

        drop(virtual_ip_mapping);

        let clients = CLIENTS.read().await;
        for client in clients.values().filter(|client| client.tenant == tenant) {
            for command in &commands {
                client.handle.send(command.clone()).await;
            }
        }
    }

    async fn handle_del_resource(&mut self, tenant: String, id: String) {
        if let Some(resources) = self.resources.get_mut(&tenant) {
            if let Some(resource) = resources.take(&BackendResource {
                id,
                url: None,
                ips: None,
                ranges: None,
                cidr: None,
            }) {
                // 删除虚拟IP映射
                if let Some(virtual_ips) = resource.virtuals() {
                    let mut virtual_ip_mapping = VIRTUAL_IP_MAPPING.write().await;
                    let inner_map = virtual_ip_mapping.entry(tenant.clone()).or_default();
                    virtual_ips.keys().for_each(|virtual_ip| {
                        inner_map.remove(virtual_ip);
                    });
                }

                let mut commands = vec![];

                let servers: Servers = (&resource).into();

                let mut payload = vec![tlv::Tag::Boolean as u8, 1, 0];
                payload.append(&mut servers.as_bytes());
                let message = Message::new(MessageType::IncrementalServer, &payload);
                let resource_command = CommCommand::Message(message);
                commands.push(resource_command);

                if let Ok(traceability_resource) = (&resource).try_into() {
                    if let Some(mapping) = traceability::del_resource(&traceability_resource).await
                    {
                        let traceability_resources = tlv_types::TraceabilityResources {
                            flag: false,
                            resources: vec![traceability_resource],
                        };
                        let traceability_command = CommCommand::Message(Message::new(
                            MessageType::TraceabilityResources,
                            &traceability_resources.serialize(),
                        ));

                        let virtual_apps = TraceabilityResourcesVirtualIps {
                            flag: false,
                            mapping: mapping
                                .into_iter()
                                .map(|(real, virtual_ip)| VirtualIpAddr { real, virtual_ip })
                                .collect(),
                        };
                        let message = Message::new(
                            MessageType::TraceabilityResourcesVirtualIps,
                            &virtual_apps.serialize(),
                        );
                        let command = CommCommand::Message(message);
                        commands.push(traceability_command);
                        commands.push(command);
                    }
                }

                let clients = CLIENTS.read().await;
                for client in clients.values().filter(|client| client.tenant == tenant) {
                    for command in &commands {
                        client.handle.send(command.clone()).await;
                    }
                }
            }
        }
    }
}

use std::net::SocketAddr;

use super::handler::MessageHandler;
use hyper_util::{rt::TokioExecutor, server::conn};

mod crypto;
mod proxy;
mod socket;
mod tls;

pub use crypto::{
    add_resource, get_http_scheme, remove_resource, SNI_SERVER_CRYPTO_MAP, TRACEABILITY_RESOURCES,
};

#[derive(Clone)]
pub(super) struct Proxy {
    /// listen socket address
    pub listening_on: SocketAddr,
    /// message handler serving incoming http request
    pub msg_handler: MessageHandler,
}

impl Proxy {
    pub(super) async fn start(self) {
        let conn_builder = conn::auto::Builder::new(TokioExecutor::new());

        if let Err(error) = self.start_without_tls(conn_builder).await {
            log::error!("Forward HTTP proxy failed. {error}");
        }
    }
}

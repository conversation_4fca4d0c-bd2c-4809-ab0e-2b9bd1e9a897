use std::{
    collections::HashMap,
    sync::{
        atomic::{AtomicU64, Ordering},
        Arc,
    },
    time::Duration,
};

use base::packet::{Message, MessageType};
use futures_util::StreamExt;
use once_cell::sync::Lazy;
use proxy_request::{
    Body, FormBody, FormPart, HeaderMap, HttpRequest, ProxyResult, ResponseData, ResponseType,
};
use reqwest::{header, ClientBuilder, Method, StatusCode};
use serde_json::Value;
use tlv::Serialize;
use tlv_types::{ProxyResponse, ResponseChunk};
use tokio::sync::Mutex;
use tracing::trace;

type ClientStore = Arc<Mutex<HashMap<String, Client>>>;

fn clients() -> &'static ClientStore {
    static STORE: Lazy<ClientStore> = Lazy::new(Default::default);
    &STORE
}

static PROXY_CLIENT: Lazy<Client> = Lazy::new(|| {
    Client(
        ClientBuilder::new()
            .danger_accept_invalid_certs(true)
            .danger_accept_invalid_hostnames(true)
            .use_rustls_tls()
            .build()
            .unwrap(),
    )
});

pub trait Sender {
    async fn send(&self, message: Message) -> Result<(), proxy_request::Error>;
}

pub async fn execute(
    _device_id: &str,
    seq: u64,
    request: HttpRequest,
    response_sender: impl Sender,
) -> ProxyResult<()> {
    let url = request.url.clone();
    trace!(target: "http_proxy", seq, %url, "proxy http request");

    let response = PROXY_CLIENT.send(request).await?;

    trace!(target: "http_proxy", seq, %url, "proxy http response");

    proxy_response(seq, None, response.1, response.0, response_sender).await
}

/// 代理响应处理
///
/// # Arguments
///
/// - `seq`: 请求序号
/// - `is_forward`: 标记是转发代理还是客户端发起的请求
/// - `down`: 流量统计
/// - `response`: 原始响应
/// - `response_type`: 响应类型
/// - `response_sender`: 发送响应
async fn proxy_response(
    seq: u64,
    down: Option<(Arc<AtomicU64>, Arc<AtomicU64>)>,
    response: reqwest::Response,
    _response_type: ResponseType,
    response_sender: impl Sender,
) -> ProxyResult<()> {
    let url = response.url().clone();

    let mut headers = HashMap::new();
    let mut raw_headers = HashMap::new();
    for (name, value) in response.headers() {
        headers.insert(
            name.as_str().to_string(),
            String::from_utf8(value.as_bytes().to_vec())?,
        );
        raw_headers.insert(
            name.as_str().to_string(),
            response
                .headers()
                .get_all(name)
                .into_iter()
                .map(|v| String::from_utf8(v.as_bytes().to_vec()).map_err(Into::into))
                .collect::<ProxyResult<Vec<String>>>()?,
        );
    }
    let status = response.status().as_u16();

    macro_rules! record_traffic {
        ($down: expr, $len: expr) => {
            if let Some((session_down, down)) = $down.clone() {
                session_down.fetch_add($len, Ordering::Relaxed);
                down.fetch_add($len, Ordering::Relaxed);
            }
        };
    }

    // 消息头
    let response_data = ResponseData {
        url,
        status,
        headers,
        raw_headers,
        data: Value::Null,
    };

    let response_data = serde_json::to_vec(&response_data).unwrap();
    let proxy_response = ProxyResponse {
        seq,
        data: response_data,
        is_chunk: true,
    };

    let message = Message::new(MessageType::ProxyResponse, &proxy_response.serialize());
    record_traffic!(down, message.len() as u64);
    response_sender.send(message).await?;

    // chunk
    let mut stream = response.bytes_stream();

    while let Some(Ok(item)) = stream.next().await {
        let chunk = ResponseChunk {
            seq,
            data: item.to_vec(),
        };
        let message = Message::new(MessageType::ProxyResponseChunk, &chunk.serialize());
        record_traffic!(down, message.len() as u64);
        response_sender.send(message).await?;
    }

    // chunk 结束标记
    let chunk = ResponseChunk { seq, data: vec![] };

    let message = Message::new(MessageType::ProxyResponseChunk, &chunk.serialize());
    record_traffic!(down, message.len() as u64);
    response_sender.send(message).await?;

    Ok(())
}

/// A response with raw bytes.
#[non_exhaustive]
#[derive(Debug)]
pub struct RawResponse {
    /// Response status code.
    pub status: u16,
    /// Response bytes.
    pub data: Vec<u8>,
}

/// The HTTP response.
#[derive(Debug)]
pub struct Response(pub ResponseType, pub reqwest::Response);

impl Response {
    /// Get the [`StatusCode`] of this Response.
    pub fn status(&self) -> StatusCode {
        self.1.status()
    }

    /// Get the headers of this Response.
    pub fn headers(&self) -> &header::HeaderMap {
        self.1.headers()
    }

    /// Reads the response as raw bytes.
    pub async fn bytes(self) -> ProxyResult<RawResponse> {
        let status = self.status().as_u16();
        let data = self.1.bytes().await?.to_vec();
        Ok(RawResponse { status, data })
    }

    // Convert the response into a Stream of [`bytes::Bytes`] from the body.
    //
    // # Examples
    //
    // ```no_run
    // use futures_util::StreamExt;
    //
    // # async fn run() -> Result<(), Box<dyn std::error::Error>> {
    // let client = tauri::api::http::ClientBuilder::new().build()?;
    // let mut stream = client.send(tauri::api::http::HttpRequestBuilder::new("GET", "http://httpbin.org/ip")?)
    //   .await?
    //   .bytes_stream();
    //
    // while let Some(item) = stream.next().await {
    //     println!("Chunk: {:?}", item?);
    // }
    // # Ok(())
    // # }
    // ```
    #[allow(dead_code)]
    pub(crate) fn bytes_stream(
        self,
    ) -> impl futures_util::Stream<Item = ProxyResult<bytes::Bytes>> {
        self.1.bytes_stream().map(|res| res.map_err(Into::into))
    }

    /// Reads the response.
    ///
    /// Note that the body is serialized to a [`Value`].
    pub async fn read(self) -> ProxyResult<ResponseData> {
        let url = self.1.url().clone();

        let mut headers = HashMap::new();
        let mut raw_headers = HashMap::new();
        for (name, value) in self.1.headers() {
            headers.insert(
                name.as_str().to_string(),
                String::from_utf8(value.as_bytes().to_vec())?,
            );
            raw_headers.insert(
                name.as_str().to_string(),
                self.1
                    .headers()
                    .get_all(name)
                    .into_iter()
                    .map(|v| String::from_utf8(v.as_bytes().to_vec()).map_err(Into::into))
                    .collect::<ProxyResult<Vec<String>>>()?,
            );
        }
        let status = self.1.status().as_u16();

        let data = match self.0 {
            ResponseType::Json => self.1.json().await?,
            ResponseType::Text => Value::String(self.1.text().await?),
            ResponseType::Binary => serde_json::to_value(&self.1.bytes().await?)?,
        };

        Ok(ResponseData {
            url,
            status,
            headers,
            raw_headers,
            data,
        })
    }
}

#[derive(Debug, Clone)]
struct Client(reqwest::Client);

impl Client {
    /// Executes an HTTP request
    ///
    /// # Examples
    pub async fn send(&self, mut request: HttpRequest) -> ProxyResult<Response> {
        let method = Method::from_bytes(request.method.to_uppercase().as_bytes())?;

        let mut request_builder = self.0.request(method, request.url.as_str());

        if let Some(query) = request.query {
            request_builder = request_builder.query(&query);
        }

        if let Some(timeout) = request.timeout {
            request_builder = request_builder.timeout(timeout);
        } else {
            request_builder = request_builder.timeout(Duration::from_secs(60));
        }

        if let Some(body) = request.stream {
            request_builder = request_builder.body(body);
        }

        if let Some(body) = request.body {
            request_builder = match body {
                Body::Bytes(data) => request_builder.body(bytes::Bytes::from(data)),
                Body::Text(text) => request_builder.body(bytes::Bytes::from(text)),
                Body::Json(json) => request_builder.json(&json),
                Body::Form(form_body) => {
                    #[allow(unused_variables)]
                    fn send_form(
                        request_builder: reqwest::RequestBuilder,
                        headers: &mut Option<HeaderMap>,
                        form_body: FormBody,
                    ) -> ProxyResult<reqwest::RequestBuilder> {
                        if matches!(
                            headers
                                .as_ref()
                                .and_then(|h| h.0.get("content-type"))
                                .map(|v| v.as_bytes()),
                            Some(b"multipart/form-data")
                        ) {
                            // the Content-Type header will be set by reqwest in the `.multipart`
                            // call
                            headers.as_mut().map(|h| h.0.remove("content-type"));
                            let mut multipart = reqwest::multipart::Form::new();

                            for (name, part) in form_body.0 {
                                let part = match part {
                                    FormPart::File {
                                        file,
                                        mime,
                                        file_name,
                                    } => {
                                        let bytes: Vec<u8> = file.try_into()?;
                                        let mut part = reqwest::multipart::Part::bytes(bytes);
                                        if let Some(mime) = mime {
                                            part = part.mime_str(&mime)?;
                                        }
                                        if let Some(file_name) = file_name {
                                            part = part.file_name(file_name);
                                        }
                                        part
                                    }
                                    FormPart::Text(value) => reqwest::multipart::Part::text(value),
                                };

                                multipart = multipart.part(name, part);
                            }

                            return Ok(request_builder.multipart(multipart));
                        }

                        let mut form = Vec::new();
                        for (name, part) in form_body.0 {
                            match part {
                                FormPart::File { file, .. } => {
                                    let bytes: Vec<u8> = file.try_into()?;
                                    form.push((name, serde_json::to_string(&bytes)?))
                                }
                                FormPart::Text(value) => form.push((name, value)),
                            }
                        }
                        Ok(request_builder.form(&form))
                    }
                    send_form(request_builder, &mut request.headers, form_body)?
                }
            };
        }

        if let Some(headers) = request.headers {
            request_builder = request_builder.headers(headers.0);
        }

        let http_request = request_builder.build()?;

        let response = self.0.execute(http_request).await?;

        Ok(Response(
            request.response_type.unwrap_or(ResponseType::Json),
            response,
        ))
    }
}

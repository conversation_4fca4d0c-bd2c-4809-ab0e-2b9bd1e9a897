use std::{collections::HashSet, sync::Arc, time::Duration};

use tracing::{debug, info};

use crate::{arp::ArpHandle, config::Segments, EventSender, VIRTUAL_IP_MAPPING};

use super::{client::BackendCommonClient, health_check};

pub async fn setup(
    backend_interval: u64,
    backend_client: Arc<BackendCommonClient>,
    arp_handle: ArpHandle,
    segments: Segments,
    event_sender: EventSender,
) {
    let start = std::time::Instant::now();

    // 等待后台服务启动
    while let None = backend_client.get_backend_state().await {
        // 服务未启动
        info!("waiting for connection to backend service");

        tokio::time::sleep(Duration::from_secs(backend_interval)).await;
    }

    // 定时检查后端服务状态
    health_check::spawn(backend_interval, backend_client.clone()).await;

    info!(elapsed = ?start.elapsed(), "service is ready");

    // 加载资源
    let resources = backend_client.fetch_resources().await;
    debug!(all_resources = ?resources);
    tokio::spawn({
        let resources = resources.clone();
        async move {
            let mut traceability_apps = vec![];
            let mut ips = HashSet::new();
            let mut virtual_ip_mapping = VIRTUAL_IP_MAPPING.write().await;

            resources.iter().for_each(|(tenant, resources)| {
                let inner_map = virtual_ip_mapping.entry(tenant.to_owned()).or_default();

                resources.iter().for_each(|resource| {
                    if let Ok(traceability_app) = resource.try_into() {
                        traceability_apps.push(traceability_app);
                    }

                    if let Some(ip_list) = &resource.ips {
                        ip_list.iter().for_each(|ip| {
                            ips.insert(ip.ip);

                            if let Some(virtual_ip) = ip.virtual_ip {
                                inner_map.insert(virtual_ip, ip.ip);
                            }
                        })
                    }
                });
            });
            drop(virtual_ip_mapping);

            // 添加溯源资源
            crate::traceability::add_resources(traceability_apps.iter()).await;

            // 获取所有资源对应IP地址的Mac地址
            for ip in ips {
                let iface = segments.find_interface(&ip);
                arp_handle.send(iface, ip).await;
            }
        }
    });

    event_sender
        .send(crate::InternalEvent::LoadedResource(resources))
        .await;
}

use std::time::Duration;

use crate::{
    backend::client::{BackendClient, BackendCommonClient},
    cache::CacheManager,
};
use base::packet::{Message, MessageType};
use serde_json::Value;
use std::{net::SocketAddr, sync::Arc};
use tracing::{error, info, warn, Instrument};

use super::AccessInfo;
use serde::Serialize;

use crate::message::MqHandle;
use std::{collections::HashSet, net::IpAddr};

pub(super) async fn handle_change_password_request(
    peer_addr: SocketAddr,
    backend_client: Arc<BackendCommonClient>,
    tx: flume::Sender<super::server::ChannelMessage>,
    mut change_pwd_pkt: Value,
) -> Result<(), super::Error> {
    let ip = peer_addr.ip().to_string();

    let mut env = change_pwd_pkt["ENVIRONMENT"].take();
    env["ip"] = Value::String(ip.to_string());

    let tenant = change_pwd_pkt["tenant"]
        .as_str()
        .ok_or(super::Error::EnvMissingField("tenant"))?;

    let buf = match backend_client
        .change_pwd_by_verify_code(tenant, &serde_json::to_vec(&change_pwd_pkt).unwrap(), &env)
        .await
    {
        Ok(buf) => {
            info!("password reset successful, disconnect.");
            buf
        }
        Err((_, buf)) => {
            error!("password reset failed.");
            buf
        }
    };

    use tlv::Serialize as TlvSerialize;
    let message = Message::new(MessageType::ChangePwdResult, &TlvSerialize::serialize(&buf));

    let (callback_tx, rx) = tokio::sync::oneshot::channel();
    _ = tx
        .send_async(super::server::ChannelMessage::Message {
            message,
            callback: Some(callback_tx),
        })
        .await;
    _ = rx.await;
    Ok(())
}

#[derive(Serialize)]
#[serde(rename_all = "camelCase")]
struct Log {
    source: IpAddr,
    target: IpAddr,
    port: Option<u16>,
    protocol: u8,
    username: String,
    device_id: String,
    tenant: String,
}

/// 处理虚拟IP及记录访问日志
///
/// # Arguments
///
/// - `peer`: 客户端IP
/// - `device_id`: 设备ID
/// - `username`: 用户名
/// - `tenant`: 租户
/// - `packet`: 数据包
/// - `accessed`: 访问过的记录
#[allow(clippy::too_many_arguments)]
pub(super) async fn handle_virtual_ip_and_log(
    peer: IpAddr,
    device_id: &str,
    username: &str,
    tenant: &str,
    packet: &mut base::net::IpPacket,
    accessed: &mut HashSet<(IpAddr, Option<u16>)>,
    mq_handle: MqHandle,
) {
    let resource = packet.dest;

    let dest_port = packet.ports.map(|(_, dest_port)| dest_port);

    let target = (resource, dest_port);
    if !accessed.contains(&target) {
        let log = Log {
            source: peer,
            target: resource,
            port: dest_port,
            protocol: packet.next_protocol,
            username: username.to_owned(),
            device_id: device_id.to_owned(),
            tenant: tenant.to_owned(),
        };

        mq_handle.log(serde_json::to_vec(&log).unwrap()).await;
        // 记录访问日志
        match packet.next_protocol {
            1 => {
                info!(target: "audit", username, device_id, target = %target.0, protocol = "ICMP");
            }
            58 => {
                info!(target: "audit", username, device_id, target = %target.0, protocol = "ICMPv6");
            }
            6 => {
                info!(target: "audit", username, device_id, target = %target.0, port = &target.1, protocol = "TCP");
            }
            17 => {
                info!(target: "audit", username, device_id, target = %target.0, port = &target.1, protocol = "UDP");
            }
            _ => (),
        }

        accessed.insert(target);
    }
}

/// 定时加载授权信息
pub(super) async fn spawn_authorize(
    tenant: &str,
    username: &str,
    device_id: &str,
    interval: u64,
    cache_manager: CacheManager,
    tx: flume::Sender<Vec<AccessInfo>>,
) {
    info!("load authorization information task");

    let key = format!("{tenant}|sdp::access_info::{username}::{device_id}");

    tokio::spawn(
        async move {
            let mut tick = tokio::time::interval(Duration::from_secs(interval));

            loop {
                _ = tick.tick().await;

                let access_info = match cache_manager.get::<String>(&key).await {
                    Some(access_info) => serde_json::from_str::<Vec<AccessInfo>>(&access_info)
                        .map_err(|_| {
                            warn!("failed to deserialize authorization.");
                        })
                        .unwrap_or_default(),
                    None => Default::default(),
                };

                if tx.send_async(access_info).await.is_err() {
                    info!("close the task of loading authorization information");
                    break;
                }
            }
        }
        .in_current_span(),
    );
}

/// 定时加载客户端执行策略
pub(super) async fn spawn_client_strategy(
    username: String,
    interval: u64,
    internal_tx: flume::Sender<Message>,
    backend_client: Arc<BackendClient>,
) {
    use tlv::Serialize as TlvSerialize;

    info!("load client policy task");
    tokio::spawn(
        async move {
            let mut tick = tokio::time::interval(Duration::from_secs(interval));
            let mut cached_strategy = Value::Null;
            loop {
                _ = tick.tick().await;

                if internal_tx.is_disconnected() {
                    info!("close the load client policy task");
                    break;
                }

                if let Some(mut strategy) = backend_client
                    .client_execution_policy(&username)
                    .await
                    .ok()
                    .and_then(|result| serde_json::from_slice::<Value>(&result).ok())
                {
                    let strategy = strategy["data"].take();
                    if strategy == cached_strategy {
                        continue;
                    }
                    let message = Message::new(
                        MessageType::DeliveryStrategy,
                        &TlvSerialize::serialize(&strategy),
                    );

                    _ = internal_tx.send_async(message).await;

                    cached_strategy = strategy;
                }
            }
        }
        .in_current_span(),
    );
}

/// 加载授权信息
pub(super) async fn load_authorize(
    tenant: &str,
    username: &str,
    device_id: &str,
    cache_manager: &CacheManager,
) -> Vec<AccessInfo> {
    let key = format!("{tenant}|sdp::access_info::{username}::{device_id}");

    match cache_manager.get::<String>(&key).await {
        Some(access_info) => serde_json::from_str::<Vec<AccessInfo>>(&access_info)
            .map_err(|_| {
                warn!("failed to deserialize access info.");
            })
            .unwrap_or_default(),
        None => Default::default(),
    }
}

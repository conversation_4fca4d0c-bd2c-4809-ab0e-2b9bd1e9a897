<template>
  <div>
    <div v-if="type" style="margin-bottom: 15px;">您的设备登录数量已达上限，请从下方至少选择{{total + 1 - limit}}个设备进行移除，以便继续登录。</div>
    <el-table v-loading="loading" :data="tableData" border style="width: 100%">
      <!-- <el-table-column type="selection" :selectable="selectable" width="55" /> -->
      <el-table-column prop="name" label="终端名称" :resizable="false"></el-table-column>
      <el-table-column prop="terminalId" label="终端ID" :resizable="false"></el-table-column>
      <el-table-column prop="mac" label="MAC地址" width="150" :resizable="false"></el-table-column>
      <el-table-column label="信任状态" width="90" :resizable="false">
        <template #default="scope">
          {{ scope.row.creditStatus ? '已信任' : '未信任' }}
        </template>
      </el-table-column>
      <el-table-column label="操作" width="130" :resizable="false">
        <template #default="scope">
          <el-button
            class="terminal-btn"
            v-if="!type"
            @click="handleClick(scope.row)"
            link
            type="primary"
          >
            {{ scope.row.creditStatus ? '取消信任' : '信任' }}
          </el-button>
          <el-button class="terminal-btn" link type="primary" @click="handleDelete(scope.row)">
            移除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <div class="pagination" v-if="total">
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="page"
        :page-sizes="[5, 10, 15, 20]"
        :page-size="size"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
      ></el-pagination>
    </div>
  </div>
</template>

<script>
import {userLoginDevicePage, userLoginDeviceTrust, userLoginDeviceDel} from '@/api/service'
export default {
  props: {
    type: {
      type: String,
      default: '',
    },
    userId: {
      type: String,
      default: '',
    },
    limit: {
      type: Number,
      default: 0,
    }
  },
  data() {
    return {
      tableData: [],
      page: 1,
      size: 5,
      total: 0,
      loading: false
    }
  },
  methods: {
    getList() {
      this.loading = true
      userLoginDevicePage({userId: this.userId}, this.page - 1, this.size, this.type)
        .then(res => {
          this.loading = false
          this.tableData = res.data.content
          this.total = res.data.totalElements
        })
        .catch(err => {
          this.loading = false
          console.error('获取终端列表失败:', err)
        })
    },

    handleSizeChange(val) {
      this.size = val
      this.getList()
    },

    handleCurrentChange(val) {
      this.page = val
      this.getList()
    },

    handleDelete(res) {
      this.$confirm(`是否确定移除该终端？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        roundButton: true,
        center: true,
        closeOnClickModal: false,
        closeOnPressEscape: false,
      })
        .then(() => {
          // 这里可以添加删除逻辑
          userLoginDeviceDel({ids: [res.id], operatePlace: 1, userId: this.userId})
            .then(result => {
              this.alert.success('移除成功')
              this.getList()
            })
            .catch(err => {})
        })
        .catch(() => {
          // console.log('取消删除')
        })
    },

    handleClick(res) {
      userLoginDeviceTrust({trustStatus: !res.creditStatus, id: res.id, operatePlace: 1})
        .then(result => {
          this.alert.success('更新成功')
          this.getList()
        })
        .catch(err => {})
    },
  },
  created() {
    this.getList()
  },
}
</script>

<style lang="less" scoped>
.pagination {
  display: flex;
  justify-content: flex-end;
  margin-top: 10px;
}
.terminal-btn:hover,
.terminal-btn:focus {
  color: #58aaff !important;
  border: none !important;
  background-color: initial !important;
}
.terminal-btn {
  border: none;
}
</style>

{"name": "unitclient", "version": "0.0.0", "private": true, "description": "unit client ui package", "author": {"name": "<EMAIL>"}, "scripts": {"run": "vite", "build": "vite build", "lint": "eslint --ext .js,.vue --ignore-path .gitignore --fix src", "lint:report": "eslint --ext .js,.vue --ignore-path .gitignore --fix src -f json -o $npm_config_path || exit 0", "slint": "stylelint src/style/element-ui-reset.css", "test": "echo \"Error: no test specified\" && exit 1"}, "main": "index.js", "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@meforma/vue-toaster": "^1.3.0", "@vitejs/plugin-vue": "^4.6.2", "axios": "^1.1.3", "axios-tauri-adapter": "^0.2.0", "babel-eslint": "^10.1.0", "element-plus": "^2.9.7", "font-awesome": "^4.7.0", "js-base64": "^3.7.3", "js-cookie": "^3.0.1", "qrcode-vue3": "^1.4.17", "qrcode.vue": "^3.4.0", "sm-crypto": "^0.3.11", "sm2": "^1.0.1", "standard": "^17.0.0", "v-offline": "^3.4.0", "vite": "^4.5.13", "vue": "^3.2.13", "vue-drag-verify2": "^1.2.0", "vue-infinite-scroll": "^2.0.2", "vue-router": "^4.0.15", "vuex": "^4.0.2"}, "devDependencies": {"@tauri-apps/api": "^1.6.0", "@tauri-apps/cli": "^1.6.3", "eslint": "^8.28.0", "eslint-plugin-html": "^7.1.0", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-vue": "^8.7.1", "less": "^4.1.2", "less-loader": "^11.0.0", "stylelint": "^14.16.0", "stylelint-config-standard": "^29.0.0", "stylelint-order": "^5.0.0", "unplugin-auto-import": "^0.8.8", "unplugin-vue-components": "^0.19.9", "vite-plugin-conditional-compile": "^1.4.5"}, "browserslist": ["> 1%", "last 2 versions", "not dead", "not ie 11"], "_id": "unit@0.0.0", "license": "ISC", "readme": "ERROR: No README data found!"}
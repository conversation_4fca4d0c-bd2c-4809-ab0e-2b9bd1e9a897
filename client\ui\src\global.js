import { listen } from "@tauri-apps/api/event";
import { appWindow } from "@tauri-apps/api/window";
import router from "./router";
import { ElMessageBox } from "element-plus";
import { invoke } from "@tauri-apps/api/tauri";
import store from "./store";
import { emit } from "@tauri-apps/api/event";
import { sendEnvironment, getLinkedTicket } from "./api/service";
import EventBus from "./tools/event-bus";

// window.console.log=function(){
//     emit('log',{level: 'Trace', message: JSON.stringify(arguments)})
// }

/**
 * 执行登录
 *
 * @param {string} username 用户名
 * @param {string} secret 绑定码
 * @param {string} password 密码
 * @param {string} tenantCode 租户
 * @param {string} type 登录类型
 * @param {string} ticket 联动登录/重连票据
 * @param {string} linkedLoginSignature 联动登录签名
 * @param {string} authResultId MFA登录请求ID
 */
export async function executeLogin(
  username,
  secret,
  password,
  tenantCode,
  type,
  ticket,
  linkedLoginSignature,
  authResultId
) {
  return invoke("plugin:sdp|login", {
    request: {
      connection_config: {
        ip: CONFIG.node.ip,
        host: CONFIG.node.host,
        port: parseInt(CONFIG.node.port),
        spa_port: parseInt(CONFIG.node.spa_port),
        authorization: {
          tenant: tenantCode,
          secret: secret,
          username: username,
        },
      },
      payload: {
        type: type,
        ticket: ticket,
        signature: linkedLoginSignature,
        tenant: tenantCode,
        password: password || "",
        authResultId: authResultId || "",
      },
    },
  });
}

// #if [includeCluster]
!(async () =>
  await listen("cluster_config", async () => {
    const event = new Event("showClusterConfig");
    window.dispatchEvent(event);
    await appWindow.unminimize();
    await appWindow.show();
    await appWindow.setFocus();
  }))();
// #elif [includeSDP]
!(async () =>
  await listen("set", async () => {
    const event = new Event("showNodeConfig");
    window.dispatchEvent(event);
    await appWindow.unminimize();
    await appWindow.show();
    await appWindow.setFocus();
  }))();
// #endif
!(async () =>
  await listen("logout", async (event) => {
    if (event.payload * 1 !== -1) {
      store.commit("updateIsLogin", false);
    }
    let message = "连接已断开";
    if (event.payload) {
      switch (event.payload * 1) {
        case 0:
          message = "设备已禁用";
          break;
        case 1:
          message = "用户在其他地方登录";
          break;
        case 2:
          message = "访问失败！请与管理员确认您的，安全访问条件。";
          break;
        case 3:
          message = "用户被禁用";
          break;
        case 4:
          message = "用户不存在";
          break;
        case 5:
          message = "用户未生效";
          break;
        case 6:
          message = "用户已失效";
          break;
        case 7:
          message = "您的密码已更新，需要重新登录";
          break;
        case 8:
          message = "管理员已重置您的绑定码，请重新登录";
          break;
        case 10:
          message = "您的连接已断开，请重新登录";
          break;
        case 11:
          message = "您当前登录的单位被禁用，请联系管理员";
          break;
        case 12:
          message =
            "检测到您的访问可能存在风险，管理员已为您强制退出登录，如需访问资源请稍后重新登录";
          break;
        case 13:
          message = "您当前登录的设备已被移除，请重新登录";
          break;
      }
    }
    if (event.payload * 1 === -1) {
      store.commit("setConnectState", {
        state: true,
        title: "服务器连接异常",
      });
    } else if (event.payload * 1 >= 0) {
      await appWindow.unminimize();
      await appWindow.show();
      await appWindow.setFocus();
      ElMessageBox.close();
      ElMessageBox.confirm(message, "提示", {
        confirmButtonText: "确定",
        roundButton: true,
        center: true,
        showClose: false,
        showCancelButton: false,
        closeOnClickModal: false,
        closeOnPressEscape: false,
      })
        .then(() => {
          let value = {};
          if (event.payload * 1 === 7) {
            value["users"] = {};
            value["users"][CONFIG.selected_tenant.code] = {
              password: null,
              remember_me: false,
              auto_connect: false,
            };
          }

          if (event.payload * 1 === 11) {
            value["selected_tenant"] = null;
          }
          invoke("patch", {
            value: value,
          }).then((config) => {
            window.CONFIG = config;
            router.push("/login");
          });
        });
    }
  }))();

function invokeSdpMfaAuth() {
  appWindow.unminimize();
  appWindow.show();
  appWindow.setFocus();
  // 当前正在进行认证
  if (window.processMfaAuthState) {
    return;
  }

  window.invokeMfaAuth();
}

//mfa 认证
!(async () =>
  await listen("mfa", async () => {
    await router.push("/");
    invokeSdpMfaAuth();
  }))();

//禁止操作
!(async () =>
  await listen("denied_access", async (res) => {
    await router.push("/");
    await appWindow.unminimize();
    await appWindow.show();
    await appWindow.setFocus();
    let msg = res.payload ? "(" + res.payload + ")" : "";
    ElMessageBox.close();
    ElMessageBox.alert(
      '<div class="NoActionMsg"> <img src="/assets/images/jurisdiction.png" alt="" /> <span>抱歉，您目前无法访问' +
      msg +
      "<br/>请稍后重新访问或联系管理员配置相关权限</span></div>",
      "无权访问",
      {
        dangerouslyUseHTMLString: true,
        showConfirmButton: false,
        center: true,
        roundButton: true,
      }
    );
  }))();

// 监听网络状态
!(async () =>
  await listen("network_changed", async (res) => {
    let networkState = res.payload * 1;
    store.commit("setNetworkStatus", networkState === 1);
    store.commit("setConnectState", {
      state: networkState !== 1,
      title: "网络连接异常, 请检查网络设置",
    });
    // await router.push("/");
    new EventBus().emit("reload"); // 刷新当前路由
  }))();

// 登录状态下, 托盘或任务栏请求退出
!(async () =>
  await listen("request_exit", async () => {
    await appWindow.unminimize();
    await appWindow.show();
    await appWindow.setFocus();
    ElMessageBox.close();
    ElMessageBox.confirm("确定退出?", "提示", {
      confirmButtonText: "确定",
      showCancelButton: true,
      cancelButtonText: "取消",
      roundButton: true,
      center: true,
      closeOnClickModal: false,
      closeOnPressEscape: false,
    }).then(
      () => {
        invoke("graceful_exit");
      },
      () => {
        console.log("cancel exit");
      }
    );
  }))();

// 监听环境变量日志
!(async () =>
  await listen("environment", async (res) => {
    console.log("environment", res.payload);
    sendEnvironment(JSON.parse(res.payload))
      .then((res) => {
        console.log("res:日志 ", res);
      })
      .catch((err) => {
        console.log("err:日志 ", err);
      });
  }))();

// 监听获取票据 实现联动登录
!(async () =>
  await listen("web_login_ticket", async (res) => {
    console.log("web_login_ticket", res.payload);
    getLinkedTicket()
      .then((value) => {
        console.log("res:联动票据 ", value);
        emit(res.payload["callback_event_name"], { code: 200, data: value });
      })
      .catch((err) => {
        console.log("err:联动票据 ", err);
        emit(res.payload["callback_event_name"], { code: 500, data: err });
      });
  }))();

//监听后台断开的通知
!(async () =>
  await listen("notification", async (res) => {
    console.log("res:后台断开的通知", res.payload);
    if (res.payload * 1 === 0) {
      store.commit("setConnectState", {
        state: true,
        title: "服务器连接异常",
      });
    } else {
      store.commit("setConnectState", {
        state: false,
        title: "服务器连接异常",
      });
    }
  }))();

// 监听配置变化
// #if [includeCluster]
!(async () =>
  await listen("config_updated", async (res) => {
    console.log("update config", res.payload);
    window.CONFIG = res.payload;

    const router = window.__VUE_APP__.config.globalProperties.$router;
    const currentRoute = router.currentRoute._value;
    if (currentRoute.name === "login") {
      new EventBus().emit("reload"); // 刷新当前路由
    }
  }))();
// #endif

// #if [includeSDP]
// 监听重连事件
!(async () =>
  await listen("recconect_status", async (res) => {
    let reconnectStatus = res.payload.status;
    if (reconnectStatus) {
      console.log("重连成功");
      store.commit("setConnectState", {
        state: false,
        title: "网络连接异常, 请检查网络设置",
      });

      // 刷新当前页
      await router.push("/");
      new EventBus().emit("reload");
    } else {
      console.log("重连失败");
      let message = "连接已断开";

      if (res.payload.show_long_time_inacitivity_tip) {
        message = "检测到长时间未使用, 系统已退出";
      }

      await appWindow.unminimize();
      await appWindow.show();
      await appWindow.setFocus();
      ElMessageBox.close();
      ElMessageBox.confirm(message, "提示", {
        confirmButtonText: "确定",
        roundButton: true,
        center: true,
        showCancelButton: false,
        closeOnClickModal: false,
        closeOnPressEscape: false,
      }).finally(() => {
        invoke("plugin:sdp|logout").then(() => {
          let value = {};
          value["users"] = {};
          value["users"][CONFIG.selected_tenant.code] = {
            secret: null,
            password: null,
          };
          invoke("patch", {
            value: value,
          })
            .then((config) => {
              window.CONFIG = config;
            })
            .finally(() => {
              router.push("/login");
            });
        });
      });
    }
  }))();
// #endif

<template>
  <div class="app-content">
    <headerBar />
    <div
      v-loading.fullscreen.lock="loading"
      class="common-layout"
      element-loading-text="loading_text"
      element-loading-background="rgba(0, 0, 0, 0.5)"
    >
      <h3 class="f_w_no" style="margin: 42px 0 20px 30px; font-size: 18px">
        设置
      </h3>
      <el-tabs
        :tab-position="tabPosition"
        style="height: 100%"
        v-model="activeMenuName"
      >
        <el-tab-pane name="personalCenter">
          <template #label>
            <div class="custom-tabs-label">
              <img
                width="16"
                height="16"
                src="/assets/images/setting/user.png"
                alt=""
              />&nbsp;个人信息
            </div>
          </template>
          <div style="width: 100%; height: 100vh">
            <div style="margin-top: 2%">
              <img
                style="margin-left: 60px"
                src=""
                v-real-img="{ value: imageUrl, defaultValue: defaultImageUrl }"
                class="userhead"
                alt=""
                width="100"
                height="100"
                @error="handleImageError"
              />
              <div class="textstyle">
                <p>
                  账号 : <span>&nbsp;&nbsp;{{ username || "-" }}</span>
                </p>
                <p>
                  姓名 :
                  <span>&nbsp;&nbsp;{{ user.displayName || "-" }}</span>
                </p>
                <p>
                  电话 : <span>&nbsp;&nbsp;{{ user.mobile || "-" }}</span>
                </p>
                <p>
                  邮箱 : <span>&nbsp;&nbsp;{{ user.email || "-" }}</span>
                </p>
                <p style="display: flex">
                  <span style="font-weight: normal"
                    >组织机构 : &nbsp;&nbsp;</span
                  >
                  <span style="width: 78%">
                    <span v-for="(item, index) in user.groups" :key="index"
                      >{{ item.v || "-" }}
                      <span v-if="index !== user.groups.length - 1"
                        >,&nbsp;</span
                      >
                    </span>
                  </span>
                </p>
                <p>
                  所在单位 : &nbsp;&nbsp;<span
                    >{{ (tenant && tenant.name) || "-" }}&nbsp;</span
                  >
                </p>
              </div>
            </div>
          </div>
        </el-tab-pane>
        <el-tab-pane name="changePwd" style="text-align: left">
          <template #label>
            <div class="custom-tabs-label">
              <img
                src="/assets/images/setting/change.png"
                width="16"
                height="16"
                alt=""
              />&nbsp;修改密码
            </div>
          </template>
          <changePwd :username="username" />
        </el-tab-pane>
        <el-tab-pane name="terminalPage" style="text-align: left">
          <template #label>
            <div class="custom-tabs-label">
              <img
                src="/assets/images/setting/terminal.png"
                width="16"
                height="16"
                alt=""
              />&nbsp;终端列表
            </div>
          </template>
          <div style="margin-left: 10px">
            <h3 style="margin-top: 0">终端列表</h3>
            <terminalPage
              v-if="activeMenuName === 'terminalPage'"
              ref="terminalRef"
              :userId="user.id"
            />
          </div>
        </el-tab-pane>
        <el-tab-pane name="about" style="text-align: left">
          <template #label>
            <div class="custom-tabs-label">
              <img
                src="/assets/images/setting/our.png"
                width="16"
                height="16"
                alt=""
              />&nbsp;关于我们
            </div>
          </template>
          <about />
        </el-tab-pane>
        <el-tab-pane disabled name="reset" style="text-align: left">
          <template #label>
            <div class="custom-tabs-label" @click="resetSettings">
              <img
                width="16"
                height="16"
                src="/assets/images/setting/recover.png"
                alt=""
              />&nbsp;恢复出厂设置
            </div>
          </template>
        </el-tab-pane>
      </el-tabs>
    </div>
  </div>
</template>

<script>
import { invoke } from "@tauri-apps/api";
import { computed } from "vue";
import { useStore } from "vuex";
import headerBar from "@/components/Header.vue";
import changePwd from "./changePwd.vue";
import about from "./about.vue";
import { userinfo } from "../api/service.js";
import terminalPage from "@/components/terminalPage.vue";

export default {
  name: "Setting",
  components: {
    headerBar,
    changePwd,
    about,
    terminalPage,
  },
  setup() {
    const store = useStore();
    return {
      tenant: CONFIG.selected_tenant,
      username: computed(() => store.getters.username),
      currentUser: computed(() => store.getters.currentUser),
    };
  },
  data() {
    return {
      // 激活菜单名称
      activeMenuName: "personalCenter",
      loading: false,
      loading_text: "加载中...",
      tabPosition: "left",
      // 用户信息
      user: {},

      // changeData: {},
      imageUrl: "",
      defaultImageUrl: "/assets/user.png", // 默认图片地址
    };
  },
  created() {
    this.currentUserInfo();
    if (this.$route?.query?.type) this.activeMenuName = this.$route.query.type;
  },
  mounted() {},
  methods: {
    // 恢复出厂设置
    resetSettings() {
      this.$confirm("是否恢复出厂设置？", "恢复出厂设置", {
        confirmButtonText: "确认",
        cancelButtonText: "取消",
        roundButton: true,
        center: true,
        closeOnClickModal: false,
        closeOnPressEscape: false,
      })
        .then(() => {
          invoke("reset")
            .then((config) => {
              window.CONFIG = config;
              this.$router.push({
                path: "/login",
                query: { date: new Date().getTime() },
              });
              this.$message({
                type: "success",
                message: "恢复出厂设置成功",
              });
            })
            .catch(() => {
              this.$message({
                type: "error",
                message: "恢复出厂设置失败",
              });
            });
        })
        .catch(() => {});
    },
    // 个人信息
    currentUserInfo() {
      if (this.currentUser && this.currentUser.username) {
        this.user = this.currentUser;
        if (this.currentUser.avatarId) {
          this.imageUrl =
            "http://sso.jingantech.com/identity-admin/v8/v1/sdk/files/" +
            this.currentUser.avatarId +
            "?TENANT=" +
            CONFIG.selected_tenant.code; // 在线图片地址
        } else {
          this.imageUrl = this.defaultImageUrl;
        }
      } else {
        userinfo()
          .then((res) => {
            this.user = res.data;
            if (res.data.avatarId) {
              this.imageUrl =
                "http://sso.jingantech.com/identity-admin/v8/v1/sdk/files/" +
                res.data.avatarId +
                "?TENANT=" +
                CONFIG.selected_tenant.code; // 在线图片地址
            } else {
              this.imageUrl = this.defaultImageUrl;
            }
          })
          .catch((err) => {});
      }
    },
    // 加载失败时，将图片地址设置为默认图片地址
    handleImageError(event) {
      event.target.src = this.defaultImageUrl;
    },
  },
};
</script>
<style lang="less" scoped>
.el-row {
  justify-content: flex-end;
}

.el-row div {
  margin-right: auto;
  font-size: 17px;
  line-height: 35px;
}

.setting-item {
  padding: 16px 0;
  cursor: pointer;
}

.setting-item:hover {
  background-color: #fafafa;
}

.el-divider {
  margin: 0;
}

.el-row:last-child {
  margin-bottom: 0;
}

.el-col {
  border-radius: 4px;
}

.grid-content {
  border-radius: 4px;
  min-height: 36px;
}

.h4 {
  font-size: 16px;
  font-weight: normal;
  margin: 0;
}

.input-text {
  height: 40px;
  line-height: 40px;
  /* margin-bottom: 10px; */
}

.common-layout {
  padding-top: 30px;
  width: 860px;
  height: 100vh;
}

.ArrowRight {
  margin-top: 10px;
  color: #666;
}

.el-drawer__body {
  padding: 0 20px;
}

.el-drawer__header {
  margin-bottom: 20px;
}

._text {
  margin-bottom: 5px;
  font-size: 14px;
  color: #999;
}

.el-tabs {
  /* height: 100%; */
  margin: 10px 0 0 10px;
}

.custom-tabs-label {
  text-align: left;
}

.custom-tabs-label > img {
  vertical-align: middle;
}

.textstyle {
  width: 100%;
  font-size: 14px;
  line-height: 25px;
  padding-left: 62px;
  margin-top: 20px;
  color: #53565d;

  span {
    font-weight: 600;
  }
}

:deep(.el-tabs__item:hover) {
  color: #53565d;
}

:deep(.el-tabs__item.is-active) {
  color: #53565d;
  background-color: #f4f5f7;
}

:deep(.el-tabs__item) {
  color: #53565d;
  font-weight: normal;
}
</style>

use std::time::Duration;

use futures::{
    channel::{mpsc, oneshot},
    SinkExt, StreamExt,
};
use tokio::net::TcpStream;
use tokio_rustls::client::TlsStream;
use tokio_util::codec::Framed;

use base::packet::{Message, MessageCodec};
use tlv_types::ModePayload;
use types::backend::{BackendStateTransition, ErrorStateCause};

use crate::{
    backend_proxy::ProxyBackend,
    backend_state_machine::{
        connected_state::ConnectedState, connecting_state::ConnectingState,
        disconnected_state::DisconnectedState, error_state::ErrorState, AfterDisconnect,
        BackendCommand, BackendCommandReceiver, BackendState, BackendStateWrapper, EventResult,
        SharedBackendStateValues,
    },
    EventListener,
};

use super::EventConsequence;

pub struct ProxyConnectedState {
    backend_close_tx: oneshot::Sender<()>,
    close_rx: oneshot::Receiver<()>,

    packet_tx: mpsc::UnboundedSender<Message>,
    /// 在代理模式下, 进行认证时, 将拆分的流合并返回
    return_tx: mpsc::Sender<oneshot::Sender<Option<Framed<TlsStream<TcpStream>, MessageCodec>>>>,
}

impl ProxyConnectedState {
    async fn disconnect<L: EventListener>(
        self,
        shared_values: &mut SharedBackendStateValues<L>,
        _after_disconnect: AfterDisconnect,
    ) -> EventConsequence {
        _ = self.backend_close_tx.send(());

        EventConsequence::NewState(DisconnectedState::enter(shared_values, None).await)
    }

    async fn handle_commands<L: EventListener>(
        self,
        command: Option<BackendCommand>,
        shared_values: &mut SharedBackendStateValues<L>,
    ) -> EventConsequence {
        use self::EventConsequence::*;

        match command {
            None => SameState(self.into()),
            Some(command) => match command {
                BackendCommand::Disconnect(tx) => {
                    let result = self
                        .disconnect(shared_values, AfterDisconnect::Nothing)
                        .await;
                    if let Some(tx) = tx {
                        _ = tx.send(());
                    }
                    result
                }
                BackendCommand::HttpRequest(message) => {
                    self.send_message(shared_values, message).await
                }
                // 处理登录事件
                BackendCommand::Login {
                    tx: login_tx,
                    system_info,
                    payload,
                } => {
                    let (tx, rx) = oneshot::channel();
                    // 保持当前变量的声明周期
                    let _this_wrapper = match self.send_return(shared_values, tx).await {
                        NewState(state) => return NewState(state),
                        SameState(this) => this,
                        Finished => return Finished,
                    };

                    let framed = match rx.await {
                        Ok(result) => match result {
                            Some(framed) => framed,
                            None => {
                                return NewState(
                                    ErrorState::enter(shared_values, ErrorStateCause::AuthFailed)
                                        .await,
                                )
                            }
                        },
                        Err(_) => {
                            return NewState(
                                ErrorState::enter(shared_values, ErrorStateCause::ConnectTimeout)
                                    .await,
                            )
                        }
                    };

                    log::trace!("Merge duplex stream");

                    let new_state = match tokio::time::timeout(
                        Duration::from_secs(5),
                        shared_values.login_in_proxy(framed, login_tx, system_info, payload),
                    )
                    .await
                    {
                        Ok(Some((framed, mode_type, dns, ip))) => {
                            let local_addr = framed.get_ref().get_ref().0.local_addr().unwrap();

                            match mode_type {
                                ModePayload::Mix { .. } => {
                                    ConnectedState::enter(shared_values, (framed, dns, ip.unwrap()))
                                        .await
                                }
                                ModePayload::Standard { .. } => {
                                    log::error!("Unsupported mode");
                                    let connecting_state = ConnectingState { framed };
                                    (
                                        BackendStateWrapper::from(connecting_state),
                                        BackendStateTransition::Connecting(local_addr),
                                    )
                                }
                            }
                        }
                        _ => ErrorState::enter(shared_values, ErrorStateCause::AuthFailed).await,
                    };
                    NewState(new_state)
                }
                BackendCommand::ChangePwdByCode(change_tx, system_info, message) => {
                    let (tx, rx) = oneshot::channel();
                    // 保持当前变量的声明周期
                    let _this_wrapper = match self.send_return(shared_values, tx).await {
                        NewState(state) => return NewState(state),
                        SameState(this) => this,
                        Finished => return Finished,
                    };

                    let framed = match rx.await {
                        Ok(result) => match result {
                            Some(framed) => framed,
                            None => {
                                return NewState(
                                    ErrorState::enter(shared_values, ErrorStateCause::AuthFailed)
                                        .await,
                                )
                            }
                        },
                        Err(_) => {
                            return NewState(
                                ErrorState::enter(shared_values, ErrorStateCause::ConnectTimeout)
                                    .await,
                            )
                        }
                    };

                    log::trace!("Merge duplex stream");

                    let new_state = match tokio::time::timeout(
                        Duration::from_secs(5),
                        shared_values.change_password_in_proxy(
                            framed,
                            change_tx,
                            system_info,
                            message,
                        ),
                    )
                    .await
                    {
                        Ok(_) => DisconnectedState::enter(shared_values, None).await,
                        _ => ErrorState::enter(shared_values, ErrorStateCause::AuthFailed).await,
                    };
                    NewState(new_state)
                }
                _ => SameState(self.into()),
            },
        }
    }

    async fn send_message<L: EventListener>(
        self,
        shared_values: &mut SharedBackendStateValues<L>,
        message: Message,
    ) -> EventConsequence {
        if self.packet_tx.unbounded_send(message).is_err() {
            log::error!("Backend channel close");
            self.disconnect(shared_values, AfterDisconnect::Nothing)
                .await
        } else {
            EventConsequence::SameState(self.into())
        }
    }

    async fn send_return<L: EventListener>(
        mut self,
        shared_values: &mut SharedBackendStateValues<L>,
        result_tx: oneshot::Sender<Option<Framed<TlsStream<TcpStream>, MessageCodec>>>,
    ) -> EventConsequence {
        if self.return_tx.send(result_tx).await.is_err() {
            log::error!("Backend channel close");
            self.disconnect(shared_values, AfterDisconnect::Nothing)
                .await
        } else {
            EventConsequence::SameState(self.into())
        }
    }

    async fn handle_backend_close_event<L: EventListener>(
        self,
        shared_values: &mut SharedBackendStateValues<L>,
    ) -> EventConsequence {
        EventConsequence::NewState(DisconnectedState::enter(shared_values, None).await)
    }
}

#[async_trait::async_trait]
impl<L: EventListener> BackendState<L> for ProxyConnectedState {
    type Bootstrap = Framed<TlsStream<TcpStream>, MessageCodec>;

    async fn enter(
        _shared_values: &mut SharedBackendStateValues<L>,
        bootstrap: Self::Bootstrap,
    ) -> (BackendStateWrapper, BackendStateTransition) {
        let (close_tx, close_rx) = oneshot::channel();
        let (internal_close_tx, internal_close_rx) = oneshot::channel();
        let (packet_tx, packet_rx) = mpsc::unbounded();
        let (return_tx, return_rx) = mpsc::channel(1);

        let connected_state = ProxyConnectedState {
            backend_close_tx: internal_close_tx,
            close_rx,
            packet_tx,
            return_tx,
        };

        let backend = ProxyBackend {
            ctl_framed: bootstrap,
            backend_close_rx: internal_close_rx,
            backend_close_tx: close_tx,
            ctl_packet_rx: packet_rx,
            return_rx,
        };
        tokio::spawn(async move {
            backend.run().await;
        });
        (
            BackendStateWrapper::from(connected_state),
            BackendStateTransition::ProxyConnected,
        )
    }

    async fn handle_event(
        mut self,
        commands: &mut BackendCommandReceiver,
        shared_values: &mut SharedBackendStateValues<L>,
    ) -> EventConsequence {
        let result = futures::select! {
            command = commands.next() => EventResult::Command(command),
            _ = &mut self.close_rx => EventResult::Close(None),
        };

        match result {
            EventResult::Command(command) => self.handle_commands(command, shared_values).await,
            EventResult::Close(_reason) => self.handle_backend_close_event(shared_values).await,
        }
    }
}

#![cfg_attr(
    all(not(debug_assertions), target_os = "windows"),
    windows_subsystem = "windows"
)]

use std::sync::mpsc;

use communicate_interface::ControlServiceClient;
use tauri::{
    <PERSON>ppHandle, CustomMenuItem, Manager, RunEvent, Runtime, SystemTray, SystemTrayEvent,
    SystemTrayMenu, SystemTrayMenuItem, Wry,
};
use tauri_plugin_autostart::{AutoLaunchManager, MacosLauncher};
use tokio::{
    sync::{Mutex, RwLock},
    task::JoinHandle,
};

use state::AppState;

#[cfg(feature = "cluster")]
mod cluster;
mod comm;
mod core;
#[cfg(not(debug_assertions))]
mod daemon_check;
mod environment;
mod event_listener;
#[cfg(any(feature = "cluster", not(feature = "sdp")))]
mod http;
mod logging;
mod macros;
mod plugins;
mod resource;
mod setup;
mod shutdown;
mod state;
mod system;
mod tunnel;
mod version;
mod web;
mod window;
#[cfg(target_os = "macos")]
mod window_ext;

#[cfg(windows)]
pub use web::AppPort;

#[tokio::main]
async fn main() {
    logging::init_logger().unwrap_or_else(|error| {
        eprintln!("{error}");
        std::process::exit(1)
    });

    let resource_dir = match paths::resource_dir() {
        Ok(dir) => dir,
        Err(error) => {
            log::error!(target: "app", "{error}");
            std::process::exit(1)
        }
    };

    if let Err(error) = config::migrations::migrate_all(&resource_dir).await {
        log::error!(target: "app", "{error}");
    }

    let app_config = config::init(resource_dir.clone());

    let context = tauri::generate_context!();
    let package_info = context.package_info();
    let package_name = &package_info.name;

    let builder = tauri::Builder::default()
        .manage(Mutex::new(app_config))
        .manage(resource_dir.clone())
        .plugin(tauri_plugin_single_instance::init(|app, _argv, _cwd| {
            if app.get_window("splashscreen").is_none() {
                let window = app.get_window("main").unwrap();
                window.set_focus().unwrap();
                window.unminimize().unwrap();
                window.show().unwrap();
            }
        }))
        .plugin(tauri_plugin_autostart::init(
            MacosLauncher::AppleScript,
            None,
        ))
        .setup(move |app| setup::resolve_setup(app, resource_dir))
        .enable_macos_default_menu(false)
        .system_tray(create_tray(package_name))
        .on_system_tray_event(handle_system_tray_event);

    #[cfg(feature = "cluster")]
    let builder = builder.manage(Mutex::new(false));

    #[cfg(feature = "sdp")]
    let builder = builder.plugin(plugins::sdp::plugin::init());

    #[cfg(feature = "cluster")]
    let builder = builder.plugin(plugins::cluster::plugin::init());

    #[cfg(feature = "iam")]
    let builder = builder.plugin(plugins::iam::plugin::init());

    #[cfg(windows)]
    let builder = builder.plugin(plugins::bio::init());

    #[cfg(target_os = "macos")]
    let builder = builder.menu(basic_menu(&package_name));

    let app = plugins::commands(builder)
        .build(context)
        .expect("发生了未知错误！");

    let app_handle = app.handle();

    // create start page
    window::create_start_page(&app_handle);

    app.run(handle_app_event);
}

/// Mac OS上复制粘贴
#[cfg(target_os = "macos")]
pub fn basic_menu(app_name: &str) -> tauri::Menu {
    use tauri::{AboutMetadata, Menu, MenuItem, Submenu};

    let mut menu = Menu::new();

    menu = menu.add_submenu(Submenu::new(
        app_name,
        Menu::new().add_native_item(MenuItem::About(
            app_name.to_owned(),
            AboutMetadata::default(),
        )),
    ));

    let mut edit_menu = Menu::new();
    edit_menu = edit_menu.add_native_item(MenuItem::Cut);
    edit_menu = edit_menu.add_native_item(MenuItem::Copy);
    edit_menu = edit_menu.add_native_item(MenuItem::Paste);
    edit_menu = edit_menu.add_native_item(MenuItem::SelectAll);
    menu = menu.add_submenu(Submenu::new("编辑", edit_menu));

    menu
}

/// 创建任务栏图标
#[allow(unused_variables)]
fn create_tray(package_name: &str) -> SystemTray {
    let quit = CustomMenuItem::new("quit".to_string(), "退出");
    let auto_start = CustomMenuItem::new("auto_start".to_string(), "开机启动").disabled();
    // 默认设置为选中, 否则在Linux中切换没有效果. https://github.com/tauri-apps/tao/pull/133#issuecomment-887307464
    let auto_start = auto_start.selected();

    #[allow(unused_mut)]
    let mut tray_menu = SystemTrayMenu::new();
    #[cfg(not(target_os = "windows"))]
    let mut tray_menu =
        tray_menu.add_item(CustomMenuItem::new("open".to_string(), "打开").disabled());

    #[cfg(not(feature = "cluster"))]
    {
        let set = CustomMenuItem::new("set".to_string(), "控制器地址设置").disabled();
        tray_menu = tray_menu.add_item(set);
    }

    #[cfg(feature = "cluster")]
    {
        let cluster_config =
            CustomMenuItem::new(String::from("cluster_config"), "服务器设置").disabled();
        tray_menu = tray_menu.add_item(cluster_config);
    }

    let tray_menu = tray_menu
        .add_item(auto_start)
        .add_native_item(SystemTrayMenuItem::Separator)
        .add_item(quit);
    SystemTray::new()
        .with_menu(tray_menu)
        .with_tooltip(package_name)
}

pub fn enable_tray_menu(app_handle: &AppHandle<Wry>) {
    let tray_handle = app_handle.tray_handle();
    let menu = tray_handle.get_item("auto_start");
    _ = menu.set_enabled(true);

    #[cfg(not(target_os = "windows"))]
    {
        let menu = tray_handle.get_item("open");
        _ = menu.set_enabled(true);
    }

    #[cfg(not(feature = "cluster"))]
    {
        let menu = tray_handle.get_item("set");
        _ = menu.set_enabled(true);
    }

    #[cfg(feature = "cluster")]
    {
        let menu = tray_handle.get_item("cluster_config");
        _ = menu.set_enabled(true);
    }
}

/// 任务栏图标点击事件
fn handle_system_tray_event(app: &AppHandle<Wry>, e: SystemTrayEvent) {
    match e {
        #[cfg(not(target_os = "macos"))]
        SystemTrayEvent::LeftClick { .. } => {
            let window = app.get_window("main").unwrap();
            window.unminimize().unwrap();
            window.show().unwrap();
            window.set_focus().unwrap();
        }
        SystemTrayEvent::MenuItemClick { id, .. } => match id.as_str() {
            "open" => {
                let window = app.get_window("main").unwrap();
                window.unminimize().unwrap();
                window.show().unwrap();
                window.set_focus().unwrap();
            }
            "set" => {
                app.emit_all("set", "").unwrap();
            }
            "cluster_config" => {
                app.emit_all("cluster_config", "").unwrap();
            }
            "auto_start" => {
                log::trace!(target: "app", "Received auto-start click event.");
                let manager = app.state::<AutoLaunchManager>();
                match manager.is_enabled() {
                    Ok(enabled) => {
                        #[cfg(target_os = "linux")]
                        {
                            let app_state = app.state::<RwLock<AppState>>();
                            if let Ok(read_app_state) = app_state.try_read() {
                                // 之前的状态跟当前状态不一致, 则不处理, 只更新之前的状态
                                if read_app_state.tray_auto_start_state != enabled {
                                    drop(read_app_state);
                                    log::warn!(target: "app", "Reset the startup automatic state");
                                    if let Ok(mut app_state) = app_state.try_write() {
                                        app_state.tray_auto_start_state = enabled;
                                    }
                                    return;
                                }
                            };
                        }

                        if let Err(error) = if enabled {
                            manager.disable()
                        } else {
                            manager.enable()
                        } {
                            log::error!(target: "app", "Failed to change auto launch state. {}", error);
                            return;
                        }

                        #[cfg(target_os = "linux")]
                        {
                            let app_state = app.state::<RwLock<AppState>>();
                            if let Ok(mut app_state) = app_state.try_write() {
                                app_state.tray_auto_start_state = !enabled;
                            };
                        }

                        let tray_handle = app.tray_handle();
                        let auto_start_handle = tray_handle.get_item("auto_start");
                        match auto_start_handle.set_selected(!enabled) {
                            Ok(_) => {
                                log::trace!(target: "app", "{} auto start.", if enabled {"Disable"} else {"Enable"});
                            }
                            Err(error) => {
                                log::error!(
                                    target: "app",
                                    "Failed to set menu `auto_start` state to unselected. {}",
                                    error
                                );
                            }
                        }
                    }
                    Err(err) => {
                        log::error!(target: "app", "Failed to get auto launch state. {}", err);
                    }
                }
            }
            "quit" => {
                let app_state = app.state::<RwLock<AppState>>();
                if let Ok(app_state) = app_state.try_read() {
                    if app_state.user.is_some() {
                        if app.emit_all("request_exit", "").is_ok() {
                            return;
                        }
                    }
                }
                let app_handle = app.app_handle();
                let rx = exit(app_handle);
                log::debug!(target: "app", "Exit");
                rx.recv().unwrap();
            }
            _ => {}
        },
        _ => {}
    }
}

/// 监听app事件
fn handle_app_event(app_handle: &AppHandle<Wry>, event: RunEvent) {
    match event {
        RunEvent::Exit => {
            let app_handle = app_handle.app_handle();
            let rx = exit(app_handle);
            log::debug!(target: "app", "Exit");
            rx.recv().unwrap();
        }
        #[cfg(target_os = "macos")]
        RunEvent::DockExitRequested { api } => {
            log::debug!(target: "app", "Dock exit");

            let app_state = app_handle.state::<RwLock<AppState>>();
            if let Ok(app_state) = app_state.try_read() {
                if app_state.user.is_some() {
                    match app_handle.emit_all("request_exit", "") {
                        Ok(_) => {
                            api.prevent_exit();
                        }
                        Err(error) => {
                            log::error!(target: "app", "Dock exit. {error}");
                        }
                    }
                }
            };
        }
        RunEvent::ExitRequested { api, .. } => {
            api.prevent_exit();
        }
        RunEvent::WindowEvent {
            label,
            event: tauri::WindowEvent::CloseRequested { api, .. },
            ..
        } => {
            log::debug!(target: "app", "Close requested {label}");
            match label.as_str() {
                "splashscreen" => {
                    let rx = exit(app_handle.app_handle());
                    rx.recv().unwrap();
                }
                "main" => {
                    let app_state = app_handle.state::<RwLock<AppState>>();
                    if let Ok(app_state) = app_state.try_read() {
                        if app_state.user.is_none() {
                            let rx = exit(app_handle.app_handle());
                            rx.recv().unwrap();
                            return;
                        }
                    };

                    #[cfg(target_os = "macos")]
                    app_handle.hide().unwrap();

                    #[cfg(not(target_os = "macos"))]
                    if let Some(window) = app_handle.get_window("main") {
                        window.hide().unwrap();
                    }

                    // use the exposed close api, and prevent the event loop to close
                    api.prevent_close();
                }
                _ => (),
            }
        }
        // 程序启动后
        RunEvent::Ready => {
            log::info!(target: "app", "App started");

            #[cfg(target_os = "linux")]
            {
                let app_state = app_handle.state::<RwLock<AppState>>();
                if let Ok(mut app_state) = app_state.try_write() {
                    app_state.tray_auto_start_state = true;
                };
            }

            let manager = app_handle.state::<AutoLaunchManager>();
            if let Ok(enable) = manager.is_enabled() {
                let tray_handle = app_handle.tray_handle();
                let menu_handle = tray_handle.get_item("auto_start");
                if let Err(err) = menu_handle.set_selected(enable) {
                    log::error!(target: "app", "Failed to set menu `auto_start` state. {}", err);
                }
            }
        }
        _ => {}
    }
}

fn exit<R: Runtime>(app_handle: AppHandle<R>) -> mpsc::Receiver<()> {
    let (tx, rx) = mpsc::sync_channel(1);
    tauri::async_runtime::spawn(async move {
        async_exit(&app_handle).await;
        tx.send(()).unwrap();
    });
    rx
}

async fn async_exit<R: Runtime>(app_handle: &AppHandle<R>) {
    let rpc = app_handle.try_state::<Mutex<Option<ControlServiceClient>>>();
    if let Some(rpc) = rpc {
        let mut rpc_guard = rpc.lock().await;
        let mut rpc = rpc_guard.take().unwrap();
        if let Err(status) = rpc.exit(tonic::Request::new(())).await {
            log::error!(target: "app", "Failed to request exit. {status}");

            setup::reset();
            app_handle.exit(1);
        }

        let rpc_handle = app_handle.state::<Mutex<Option<JoinHandle<()>>>>();
        let mut rpc_handle_guard = rpc_handle.lock().await;
        if let Some(rpc_handle) = rpc_handle_guard.take() {
            rpc_handle.abort();
        }
    } else {
        setup::reset();
        app_handle.exit(0);
    }
}

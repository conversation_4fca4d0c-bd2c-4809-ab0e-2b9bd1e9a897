#[cfg(not(windows))]
use crate::updater::file::Compression;
use crate::updater::{
    file::{ArchiveFormat, Extract},
    Update,
};
use base64::Engine;
use minisign_verify::{PublicKey, Signature};
use semver::Version;
use serde::{de::Error as DeError, Deserialize, Deserializer};
use std::{
    ffi::OsStr,
    fs::read_dir,
    io::{Read, Seek},
    path::PathBuf,
    process::{exit, Command},
    str::{from_utf8, FromStr},
    sync::atomic::AtomicBool,
};
use time::OffsetDateTime;
use tokio::sync::RwLock;
use url::Url;

use once_cell::sync::Lazy;
#[cfg(windows)]
use std::env::current_exe;

pub type Result<T = ()> = std::result::Result<T, Error>;

const PUBKEY: &str = include_str!("../../resources/updater_pubkey");

#[derive(thiserror::Error, Debug)]
pub enum Error {
    #[error("User canceled")]
    Cancel,
    #[error("{0}")]
    Utf8(#[from] std::string::FromUtf8Error),
    /// IO Errors.
    #[error("{0}")]
    Io(#[from] std::io::Error),
    #[error("{0}")]
    Zip(#[from] zip::result::ZipError),
    /// Semver Errors.
    #[error("Unable to compare version")]
    Semver(#[from] semver::Error),
    /// JSON (Serde) Errors.
    #[error("JSON error")]
    SerdeJson(#[from] serde_json::Error),
    /// Minisign is used for signature validation.
    #[error("Verify signature error")]
    Minisign(#[from] minisign_verify::Error),
    /// Error with Minisign base64 decoding.
    #[error("Signature decoding error")]
    Base64(#[from] base64::DecodeError),
    /// UTF8 Errors in signature.
    #[error("The signature {0} could not be decoded, please check if it is a valid base64 string. The signature must be the contents of the `.sig` file generated by the Tauri bundler, as a string.")]
    SignatureUtf8(String),
    /// Network error.
    #[error("Network error: {0}")]
    Network(String),
    /// Could not fetch a valid response from the server.
    #[error("Could not fetch a valid release JSON from the remote")]
    ReleaseNotFound,
    /// Error building updater.
    #[error("Unable to prepare the updater: {0}")]
    Builder(String),
    /// Error building updater.
    #[error("Unable to extract the new version: {0}")]
    Extract(String),
    /// Updater cannot be executed on this Linux package. Currently the updater is enabled only on
    /// AppImages.
    #[error(
        "Cannot run updater on this Linux package. Currently only an AppImage can be updated."
    )]
    UnsupportedLinuxPackage,
    /// Operating system is not supported.
    #[error("unsupported OS, expected one of `linux`, `darwin` or `windows`.")]
    UnsupportedOs,
    /// Unsupported app architecture.
    #[error(
        "Unsupported application architecture, expected one of `x86`, `x86_64`, `arm` or `aarch64`."
    )]
    UnsupportedArch,
    /// The platform was not found on the updater JSON response.
    #[error("the platform `{0}` was not found on the response `platforms` object")]
    TargetNotFound(String),
    /// Triggered when there is NO error and the two versions are equals.
    /// On client side, it's important to catch this error.
    #[error("No updates available")]
    UpToDate,
    /// The updater responded with an invalid signature type.
    #[error("the updater response field `{0}` type is invalid, expected {1} but found {2}")]
    InvalidResponseType(&'static str, &'static str, serde_json::Value),
    /// HTTP error.
    #[error("{0}")]
    Http(#[from] http::Error),
}

pub static FLAG: AtomicBool = AtomicBool::new(false);

pub static UPDATE: Lazy<RwLock<Option<Update>>> = Lazy::new(|| RwLock::const_new(None));

#[derive(Debug)]
pub struct RemoteRelease {
    /// Version to install.
    pub version: Version,
    /// Release notes.
    pub notes: Option<String>,
    /// Release date.
    pub pub_date: Option<OffsetDateTime>,
    /// Force update.
    pub force: bool,
    /// Release data.
    pub manifest: ReleaseManifestPlatform,
}

#[derive(Debug, Deserialize)]
pub struct ReleaseManifestPlatform {
    /// Download URL for the platform
    pub url: Url,
    /// Signature for the platform
    pub signature: String,
}

impl<'de> Deserialize<'de> for RemoteRelease {
    fn deserialize<D>(deserializer: D) -> std::result::Result<Self, D::Error>
    where
        D: Deserializer<'de>,
    {
        #[derive(Deserialize)]
        struct InnerRemoteRelease {
            #[serde(alias = "name", deserialize_with = "parse_version")]
            version: Version,
            notes: Option<String>,
            pub_date: Option<String>,
            force: bool,
            manifest: ReleaseManifestPlatform,
        }

        let release = InnerRemoteRelease::deserialize(deserializer)?;

        let pub_date = if let Some(date) = release.pub_date {
            Some(
                OffsetDateTime::parse(&date, &time::format_description::well_known::Rfc3339)
                    .map_err(|e| DeError::custom(format!("invalid value for `pub_date`: {e}")))?,
            )
        } else {
            None
        };

        Ok(RemoteRelease {
            version: release.version,
            notes: release.notes,
            pub_date,
            force: release.force,
            manifest: release.manifest,
        })
    }
}

fn parse_version<'de, D>(deserializer: D) -> std::result::Result<Version, D::Error>
where
    D: Deserializer<'de>,
{
    let str = String::deserialize(deserializer)?;

    Version::from_str(str.trim_start_matches('v')).map_err(serde::de::Error::custom)
}

pub fn get_updater_target() -> Option<&'static str> {
    if cfg!(target_os = "linux") {
        Some("linux")
    } else if cfg!(target_os = "macos") {
        Some("darwin")
    } else if cfg!(target_os = "windows") {
        Some("windows")
    } else {
        None
    }
}

pub fn get_updater_arch(ext: &str) -> Option<&'static str> {
    if ext.ends_with("deb") {
        if cfg!(target_arch = "x86") {
            Some("i386")
        } else if cfg!(target_arch = "x86_64") {
            Some("amd64")
        } else if cfg!(target_arch = "arm") {
            Some("armhf")
        } else if cfg!(target_arch = "aarch64") {
            Some("arm64")
        } else {
            None
        }
    } else if ext.ends_with("rpm") {
        if cfg!(target_arch = "x86") {
            Some("i386")
        } else if cfg!(target_arch = "x86_64") {
            Some("x86_64")
        } else if cfg!(target_arch = "arm") {
            Some("armhfp")
        } else if cfg!(target_arch = "aarch64") {
            Some("aarch64")
        } else {
            None
        }
    } else {
        if cfg!(target_arch = "x86") {
            Some("i686")
        } else if cfg!(target_arch = "x86_64") {
            Some("x86_64")
        } else if cfg!(target_arch = "arm") {
            Some("armv7")
        } else if cfg!(target_arch = "aarch64") {
            Some("aarch64")
        } else {
            None
        }
    }
}

// Windows
//
// ### Expected structure:
// ├── [AppName]_[version]_x64.msi.zip          # ZIP generated by tauri-bundler
// │   └──[AppName]_[version]_x64.msi           # Application MSI
// ├── [AppName]_[version]_x64-setup.exe.zip          # ZIP generated by tauri-bundler
// │   └──[AppName]_[version]_x64-setup.exe           # NSIS installer
// └── ...
//
// ## MSI
// Update server can provide a MSI for Windows. (Generated with tauri-bundler from *Wix*)
// To replace current version of the application. In later version we'll offer
// incremental update to push specific binaries.
//
// ## EXE
// Update server can provide a custom EXE (installer) who can run any task.
#[cfg(target_os = "windows")]
#[allow(clippy::unnecessary_wraps)]
pub fn save_files<R: Read + Seek>(archive_buffer: R) -> Result<PathBuf> {
    // FIXME: We need to create a memory buffer with the MSI and then run it.
    //        (instead of extracting the MSI to a temp path)
    //
    // The tricky part is the MSI need to be exposed and spawned so the memory allocation
    // shouldn't drop but we should be able to pass the reference so we can drop it once the
    // installation is done, otherwise we have a huge memory leak.

    let tmp_dir = tempfile::Builder::new().tempdir()?.keep();

    // extract the buffer to the tmp_dir
    // we extract our signed archive into our final directory without any temp file
    let mut extractor = Extract::from_cursor(archive_buffer, ArchiveFormat::Zip);

    // extract the msi
    extractor.extract_into(&tmp_dir)?;

    Ok(tmp_dir)
}

// MacOS
// ### Expected structure:
// ├── [AppName]_[version]_x64.app.tar.gz       # GZ generated by tauri-bundler
// │   └──[AppName].app                         # Main application
// │      └── Contents                          # Application contents...
// │          └── ...
// └── ...
#[cfg(target_os = "macos")]
pub fn save_files<R: Read + Seek>(archive_buffer: R) -> Result<PathBuf> {
    let tmp_dir = tempfile::Builder::new().tempdir()?.keep();

    let mut extractor =
        Extract::from_cursor(archive_buffer, ArchiveFormat::Tar(Some(Compression::Gz)));

    // extract the dmg
    extractor.extract_into(&tmp_dir)?;

    Ok(tmp_dir)
}

#[cfg(target_os = "windows")]
pub fn run(path: &PathBuf) -> Result<()> {
    let paths = read_dir(path)?;

    let system_root = std::env::var("SYSTEMROOT");
    let powershell_path = system_root.as_ref().map_or_else(
        |_| "powershell.exe".to_string(),
        |p| format!("{p}\\System32\\WindowsPowerShell\\v1.0\\powershell.exe"),
    );

    for path in paths {
        let found_path = path?.path();
        // we support 2 type of files exe & msi for now
        // If it's an `exe` we expect an installer not a runtime.
        if found_path.extension() == Some(OsStr::new("exe")) {
            // we need to wrap the installer path in quotes for Start-Process
            let mut installer_path = std::ffi::OsString::new();
            installer_path.push("\"");
            installer_path.push(&found_path);
            installer_path.push("\"");

            // Run the EXE
            let mut cmd = Command::new(powershell_path);
            cmd.args(["-NoProfile", "-WindowStyle", "Hidden"])
                .args(["Start-Process"])
                .arg(installer_path);
            cmd.spawn().expect("installer failed to start");

            exit(0);
        } else if found_path.extension() == Some(OsStr::new("msi")) {
            // we need to wrap the current exe path in quotes for Start-Process
            let mut current_exe_arg = std::ffi::OsString::new();
            current_exe_arg.push("\"");
            current_exe_arg.push(current_exe()?);
            current_exe_arg.push("\"");

            let mut msi_path_arg = std::ffi::OsString::new();
            msi_path_arg.push("\"\"\"");
            msi_path_arg.push(&found_path);
            msi_path_arg.push("\"\"\"");

            // run the installer and relaunch the application
            let system_root = std::env::var("SYSTEMROOT");
            let powershell_path = system_root.as_ref().map_or_else(
                |_| "powershell.exe".to_string(),
                |p| format!("{p}\\System32\\WindowsPowerShell\\v1.0\\powershell.exe"),
            );
            let powershell_install_res = Command::new(powershell_path)
                .args(["-NoProfile", "-windowstyle", "hidden"])
                .args([
                    "Start-Process",
                    "-Wait",
                    "-FilePath",
                    "$env:SYSTEMROOT\\System32\\msiexec.exe",
                    "-ArgumentList",
                ])
                .arg("/i,")
                .arg(msi_path_arg)
                .arg("/promptrestart")
                .arg("Start-Process")
                .arg(current_exe_arg)
                .spawn();
            if powershell_install_res.is_err() {
                // fallback to running msiexec directly - relaunch won't be available
                // we use this here in case powershell fails in an older machine somehow
                let msiexec_path = system_root.as_ref().map_or_else(
                    |_| "msiexec.exe".to_string(),
                    |p| format!("{p}\\System32\\msiexec.exe"),
                );
                let _ = Command::new(msiexec_path)
                    .arg("/i")
                    .arg(found_path)
                    .arg("/promptrestart")
                    .spawn();
            }

            exit(0);
        }
    }

    Ok(())
}

#[cfg(target_os = "linux")]
pub fn save_files<R: Read + Seek>(archive_buffer: R) -> Result<PathBuf> {
    let tmp_dir = tempfile::Builder::new().tempdir()?.keep();

    let mut extractor =
        Extract::from_cursor(archive_buffer, ArchiveFormat::Tar(Some(Compression::Gz)));

    // extract the deb
    extractor.extract_into(&tmp_dir)?;

    Ok(tmp_dir)
}

#[cfg(target_os = "macos")]
pub fn run(path: &PathBuf) -> Result<()> {
    let paths = read_dir(path)?;

    for path in paths {
        let found_path = path?.path();

        if found_path.extension() == Some(OsStr::new("dmg")) {
            // Open the dmg
            let mut open = Command::new("open");
            open.arg(found_path).spawn().expect("Unable to open");

            exit(0);
        }
    }

    Ok(())
}

#[cfg(target_os = "linux")]
pub fn run(path: &PathBuf) -> Result<()> {
    let paths = read_dir(path)?;

    for path in paths {
        let found_path = path?.path();

        if found_path.extension() == Some(OsStr::new("deb")) {
            // Open the deb
            let mut open = Command::new("xdg-open");
            open.arg(found_path).spawn().expect("Unable to open");

            exit(0);
        }
    }

    Ok(())
}

// Convert base64 to string and prevent failing
pub fn base64_to_string(base64_string: &str) -> Result<String> {
    let decoded_string = &base64::engine::general_purpose::STANDARD.decode(base64_string)?;
    let result = from_utf8(decoded_string)
        .map_err(|_| Error::SignatureUtf8(base64_string.into()))?
        .to_string();
    Ok(result)
}

// Validate signature
// need to be public because its been used
// by our tests in the bundler
//
// NOTE: The buffer position is not reset.
pub fn verify_signature<R>(archive_reader: &mut R, release_signature: &str) -> Result<bool>
where
    R: Read,
{
    // we need to convert the pub key
    let pub_key_decoded = base64_to_string(PUBKEY)?;
    let public_key = PublicKey::decode(&pub_key_decoded)?;
    let signature_base64_decoded = base64_to_string(release_signature)?;
    let signature = Signature::decode(&signature_base64_decoded)?;

    // read all bytes until EOF in the buffer
    let mut data = Vec::new();
    archive_reader.read_to_end(&mut data)?;

    // Validate signature or bail out
    public_key.verify(&data, &signature, true)?;
    Ok(true)
}

#[test]
fn test_der() {
    let data = r#"{
    "notes": "<div style=\"padding:0 0 0 15px;text-align:left;font-weight:bold\">v7.4.6<div style=font-weight:normal>修复了一些已知问题</div></div>",
    "pub_date": "2023-10-17T00:00:00Z",
    "force": false,
    "manifest": {
        "signature": "dW50cnVzdGVkIGNvbW1lbnQ6IHNpZ25hdHVyZSBmcm9tIHRhdXJpIHNlY3JldCBrZXkKUlVTVVZ2dVdKYmxUOHlTTlIvSHdGc2tuMGpBTktZQnd6NjlGWWNhR2NYM08rcTNnUTBBSmNCdWdsWTBIdDk4RVBYRk0wWTI5Q1ZmRjBON280dTVhLy81c3B5WnZ3VS9WN2d3PQp0cnVzdGVkIGNvbW1lbnQ6IHRpbWVzdGFtcDoxNjk3NTM1MTU5CWZpbGU65a6J5YWo5LukXzcuNC42X3g2NC1zZXR1cC5uc2lzLnppcAp1UjV0SHBqSUlraHB0VC9SS2E4V3ZLejhnbEZYU3B4NEptZCtpRGFnc1k1Z1p2bjVObi9TSEVQZWtPUS9iUUVZV0V2KzZqU0l2TGpRc1VmN1NIaWRBQT09Cg==",
        "url": "https://promotion.jinganiam.com:21000/packages/JA-SDP/安全令_7.4.6_x64-setup.nsis.zip"
    },
    "version": "7.5.0"
}"#;
    let result = serde_json::from_str::<RemoteRelease>(data);
    println!("{:?}", result);
}

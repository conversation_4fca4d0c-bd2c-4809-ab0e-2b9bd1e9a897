<template>
  <div class="mfa-content">
    <h3 v-if="type">硬件令牌登录</h3>
    <div v-if="firstStep && type">
      <div class="inputItem">
        <div class="inputContent">
          <el-input
            ref="username"
            v-model="user.username"
            placeholder="请输入用户名"
          />
        </div>
      </div>
      <el-button
        ref="nextStepBtn"
        class="Btn"
        :disabled="!user.username"
        @click="generateRequestId"
      >
        下一步
      </el-button>
      <userAgreement />
    </div>
    <div v-else style="margin-top: 61px">
      <div class="auth-code-content border">
        <el-input
          v-model="authCode"
          :class="type === 'login' ? 'auth-code-input' : 'auth-input'"
          placeholder="请输入动态码"
          maxlength="6"
        />
      </div>
      <el-button
        ref="confirmBtn"
        type="primary"
        :class="{
          loginBtn: type === 'login',
          'mfa-confirm-btn': type !== 'login',
        }"
        :disabled="!authCode || submitting"
        @click="submitAuth"
      >
        {{ type === "login" ? "登录" : "确定" }}
      </el-button>
      <userAgreement v-if="type" />
      <p v-if="type" style="font-size: 12px">
        用户:
        <span @click="cancel" style="color: #f9780c; cursor: pointer"
          >&nbsp;{{ user.username }}&nbsp;
          <el-icon color="#F9780C" style="vertical-align: middle"
            ><EditPen
          /></el-icon>
        </span>
      </p>
    </div>
  </div>
</template>

<script>
import { getRequestId, verifyOtpsCode, cancelAuth } from "@/api/service";
import userAgreement from "../../views/userAgreement.vue";

export default {
  name: "TFC200",
  components: {
    userAgreement,
  },
  props: {
    participantGroupId: {
      type: String,
      default: "",
    },
    userId: {
      type: String,
      default: "",
    },
    type: {
      type: String,
      default: "",
    },
    name: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      requestId: "",
      authCode: "",
      code: "ft_c200",
      reg: /^\d+$/,
      user: {
        username: "" || this.name,
      },
      firstStep: true,
      submitting: false,
    };
  },
  created() {
    if (this.type !== "login") {
      this.generateRequestId();
    }
  },
  mounted() {},
  methods: {
    // 获取requestId
    generateRequestId() {
      let data = {
        participantTypes: this.type === "login" ? "03" : "01,03",
        participantKeyword: this.userId || this.user.username,
      };

      getRequestId(
        this.participantGroupId,
        data.participantTypes,
        data.participantKeyword,
        this.code
      )
        .then((result) => {
          this.requestId = result.data;
          this.$emit("requestId", this.requestId);
          this.firstStep = false;
        })
        .catch((error) => {
          this.$emit("generateRequestIdFailCallbackFunc", error);
        });
    },
    /**
     * 提交认证
     */
    async submitAuth() {
      try {
        this.subbmitting = true;
        await this.Complete();
      } finally {
        this.subbmitting = false;
      }
    },
    Complete() {
      if (!this.authCode) {
        this.alert.error("请输入动态口令");
        return;
      }

      if (this.authCode && !this.reg.test(this.authCode)) {
        this.alert.error("动态口令格式错误");
        return;
      }
      return verifyOtpsCode({
        requestId: this.requestId,
        authCode: this.authCode,
        method: this.code,
      })
        .then(() => {
          this.$emit("mfaCallbackFunc", {
            requestId: this.requestId,
            type: this.code,
          });
        })
        .catch((error) => {
          this.$emit("childEventCancel", this.requestId);
          let msg = JSON.parse(err.message);
          if (
            msg.data.messageKey == "MFA.MFA.AUTH.CANCELED" ||
            msg.data.messageKey == "MFA.MFA.AUTH.ALREADY"
          ) {
            this.alert.error("认证已失效，请重试");
            this.type && setTimeout(this.cancel, 1000);
          }
        });
    },
    cancel() {
      if (this.requestId) {
        let requestId = this.requestId;
        this.requestId = "";
        cancelAuth(requestId);
      }
      this.firstStep = true;
    },
  },
};
</script>

<style scoped lang="less">
.mfa-content {
  .auth-code-content {
    width: 100%;
    margin: auto;
    text-align: center;
    position: relative;

    .auth-code-input {
      width: 100%;
      margin-bottom: 30px;
      height: 40px;
      border-bottom: 1px solid #d1d3db;
      // border-radius: 30px;
      padding-left: 10px;
      &:hover {
        border-bottom: 1px solid #f9780c;
      }
    }
  }

  .icon_password {
    position: absolute;
    left: 2px;
    top: 7px;
    z-index: 999999;
  }

  //   .el-input__wrapper {
  //       border-right: 1px solid #f9780c;
  //     }
  .inputItem {
    text-indent: 0.8em;
    position: relative;
    margin-top: 1.8rem;
    border-bottom: 1px solid #d1d3db;
    // border-radius: 30px;
    margin-bottom: 10px;
    &:hover {
      border-bottom: 1px solid #f9780c;
    }
  }

  .login_icon {
    color: #bfbfbf;
    font-size: 22px;
  }

  .inputContent {
    padding: 0.4rem;
    width: 240px;
  }

  :deep(.el-input__wrapper) {
    border: 0 !important;
    padding: 1px 1px;
    border-radius: 20px;
  }

  .Btn {
    margin: 15px 0 10px;
    width: 100%;
    color: #f59547;
    background: #fff;
    border: 1px solid #f59547;
    outline: none;
    height: 45px;
    border-radius: 30px;
  }
}
</style>

<template>
  <div
    v-loading.fullscreen.lock="loading"
    class="app-home"
    element-loading-text="加载中..."
    element-loading-background="rgba(0, 0, 0, 0.5)"
  >
    <el-alert
      class="network_alert"
      v-show="connectState.state"
      :title="connectState.title"
      type="error"
      center
      :closable="false"
      show-icon
    />

    <!-- 占位 -->
    <div style="height: 53px; background-color: #fff"></div>

    <!-- #if [includeIAM] -->
    <div class="home-body">
      <div class="home_header">
        <h2>我的应用</h2>
        <div class="app-search">
          <el-input
            v-model="keyword"
            class="app-search-input"
            placeholder="请输入应用名"
            @keyup.enter.native="searchApps"
          />
          <div v-if="keyword" class="app-search-clear" @click="clearApps">
            <el-icon>
              <CircleClose />
            </el-icon>
          </div>
          <el-icon class="app-search-btn" @click="searchApps">
            <Search />
          </el-icon>
        </div>
      </div>
      <div class="home_nav" ref="textContainer">
        <el-tabs v-model="homeActiveName" @tab-click="handleClick">
          <el-tab-pane label="全部应用" name="all"></el-tab-pane>
          <el-tab-pane
            v-for="item in institutionalList"
            :key="item.id"
            :name="item.id"
          >
            <template #label>
              <!-- <el-tooltip  effect="light" :content="item.name" placement="top">
              </el-tooltip> -->
              <span>{{ item.name }}</span>
            </template>
          </el-tab-pane>
          <el-tab-pane name="culture" v-if="sliceList.length" disabled>
            <template #label>
              <el-dropdown
                :hide-on-click="false"
                @command="handleCommand($event, row)"
              >
                <span class="more-link">
                  更多<el-icon class="el-icon--right"><arrow-down /></el-icon>
                </span>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item
                      v-for="res in sliceList"
                      :key="res.id"
                      :command="res.id"
                      :class="{ selected: selectId == res.id }"
                    >
                      <span class="dropdownClass">{{ res.name }}&nbsp;</span>
                      <span class="is_active">
                        <el-icon v-show="selectId == res.id">
                          <Check />
                        </el-icon>
                      </span>
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </template>
          </el-tab-pane>
        </el-tabs>
      </div>
      <div
        class="home_footer infinite-scroll"
        v-infinite-scroll="loadMore"
        nfinite-scroll-disabled="busy"
        infinite-scroll-immediate="false"
      >
        <div
          class="singleApp"
          v-for="app in myapps"
          :key="app.id"
          @click="visit(app)"
        >
          <span class="singleApp_logo">
            <img v-real-img="{
              value: app.src,
              defaultValue: defaultAppLogo
            }" width="36" height="36" alt="" />
          </span>
          <span class="singleApp_title">{{ app.name }}</span>
        </div>
        <div v-if="myapps.length === 0 && !busy" class="help-text">
          <img src="/assets/images/noData.png" alt="" />
          <span>暂无内容</span>
        </div>
        <div class="help-text" v-if="busy">
          <img width="150" height="150" src="/assets/listLoading.gif" alt="" />
          <span>加载中...</span>
        </div>
      </div>
    </div>
    <!-- #elif [includeSDP] -->
    <div class="home-body">
      <div class="home_header">
        <h2>我的资源</h2>
        <div class="app-search">
          <el-input
            v-model="applynames"
            class="app-search-input"
            placeholder="请输入资源名"
            @keyup.enter.native="seekapp"
          />
          <div v-if="applynames" class="app-search-clear" @click="clearnames">
            <el-icon>
              <CircleClose />
            </el-icon>
          </div>
          <el-icon class="app-search-btn" @click="seekapp">
            <Search />
          </el-icon>
        </div>
      </div>
      <div
        class="home_footer infinite-scroll"
        v-infinite-scroll="sdploadMore"
        nfinite-scroll-disabled="busy"
        infinite-scroll-immediate="false"
      >
        <div
          class="singleApp"
          v-for="(item, index) in list"
          :key="index"
          @click="activeBox(item)"
        >
          <span class="singleApp_logo">
            <img
              v-if="item.src"
              src=""
              v-real-img="{
                value: item.src,
                defaultValue: defaultAppLogo
              }"
              width="36"
              height="36"
              alt=""
            />
          </span>
          <span class="singleApp_title">{{ item.name }}</span>
        </div>
        <div v-if="list.length === 0 && !busy" class="help-text">
          <img src="/assets/images/noData.png" alt="" />
          <span>暂无内容</span>
        </div>
        <div class="help-text" v-if="busy">
          <img width="150" height="150" src="/assets/listLoading.gif" alt="" />
          <span>加载中...</span>
        </div>
      </div>
    </div>
    <!-- #endif -->

    <!-- 占位 -->
    <div style="height: 30px; background-color: #fff"></div>

    <!-- 选择账号 -->
    <el-dialog
      v-model="selectAccountDialog"
      title="选择访问账号"
      width="35%"
      style="padding-bottom: 16px"
      :before-close="cancelSelectAccount"
      destroy-on-close
      center
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <div class="account-model">
        <div
          v-for="item in accounts"
          :key="item.id"
          class="account-item"
          @click="selectAccount(item)"
        >
          {{ item.accountName }}
        </div>
      </div>
    </el-dialog>

    <!-- 选择认证方式 -->
    <el-dialog
      v-model="mfaAuthDialog"
      :title="title"
      width="50%"
      destroy-on-close
      :before-close="closeBox"
      center
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <el-scrollbar>
        <div style="padding: 10px 30px">
          <div v-if="appName" class="mfa-model-text">
            正在请求登录 <span style="color: #f9780c">{{ appName }}</span
            >,&nbsp;请验证...
          </div>
          <div v-if="target_resource" class="mfa-model-text">
            <!-- <el-tooltip effect="dark" :content="target_resource" placement="top">
                正在请求访问<span style="color: #f9780c;">{{ target_resource }}</span>
            </el-tooltip>  -->
            正在请求访问
            <span style="color: #f9780c">{{ target_resource }}</span
            >资源,&nbsp;请验证...
          </div>
          <div style="display: flex; flex-wrap: wrap">
            <div
              v-for="item in mfaTypes"
              :key="item.method.code"
              class="mfaTypesCode"
            >
              <img
                width="46"
                height="46"
                alt=""
                @mouseenter="showHighlightedImage(item.method.code)"
                @mouseleave="showNormalImage"
                :src="
                  currentCode != item.method.code
                    ? 'assets/images/defaultlogin/' + item.method.code + '.png'
                    : '/assets/images/mfalogin/' + item.method.code + '.png'
                "
                @click="selectMfaType(item.method.code, item.method.name)"
              />
              <span :title="item.method.name">{{ item.method.name }}</span>
            </div>
          </div>
          <!-- <el-carousel style="height: 200px; width: 100%;" :autoplay="false" arrow="always">
              <el-carousel-item v-for="res in mfaTypes" :key="res">
                  <div v-for="item in res" :key="item.method.code" style="display: flex;flex-direction: column; justify-content: center;align-items: center;">
                      <img width="50" height="50"  alt=""
                      :src="currentCode != item.method.code ? 'assets/images/defaultlogin/' + item.method.code + '.png' : '/assets/images/mfalogin/' + item.method.code + '.png'"
                      @click="selectMfaType(item.method.code,item.method.name)">
                      <span style="font-size: 12px; margin-top: 10px;">{{ item.method.name == "指纹" ? "指纹/面容ID" : item.method.name }}</span>
                  </div>
              </el-carousel-item>
          </el-carousel> -->
        </div>
      </el-scrollbar>
    </el-dialog>

    <!-- 认证详情 -->
    <el-dialog
      v-model="mfaAuthDetailDialog"
      width="50%"
      :title="currentName + '认证'"
      :before-close="closeBox"
      destroy-on-close
      center
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <div class="auth-model">
        <FACE
          v-if="currentCode === 'face'"
          ref="pollMfaMethod"
          :user-id="participantKeyword"
          :participant-group-id="participantGroupId"
          @mfaCallbackFunc="CompleteAuth"
          @cancelMfaCallbackFunc="cancelAuth"
          @requestId="receiveRequestId"
        />
        <FACIAL
          v-if="currentCode === 'facial_recognition'"
          ref="pollMfaMethod"
          :user-id="participantKeyword"
          :participant-group-id="participantGroupId"
          @mfaCallbackFunc="CompleteAuth"
          @cancelMfaCallbackFunc="cancelAuth"
          @requestId="receiveRequestId"
          :tenant="tenantCode"
        />
        <AUTHCODE
          v-if="currentCode === 'auth_code'"
          ref="manualMfaMethod"
          :user-id="participantKeyword"
          :participant-group-id="participantGroupId"
          @mfaCallbackFunc="CompleteAuth"
          @cancelMfaCallbackFunc="cancelAuth"
          @requestId="receiveRequestId"
        />
        <SMSOTP
          v-if="currentCode === 'sms_otp'"
          ref="manualMfaMethod"
          :user-id="participantKeyword"
          :participant-group-id="participantGroupId"
          @mfaCallbackFunc="CompleteAuth"
          @cancelMfaCallbackFunc="cancelAuth"
          @requestId="receiveRequestId"
        />
        <FINGERPRINT
          v-if="currentCode === 'fingerprint'"
          ref="pollMfaMethod"
          :user-id="participantKeyword"
          :participant-group-id="participantGroupId"
          @mfaCallbackFunc="CompleteAuth"
          @requestId="receiveRequestId"
        />
        <VOICE
          v-if="currentCode === 'voice'"
          ref="pollMfaMethod"
          :user-id="participantKeyword"
          :participant-group-id="participantGroupId"
          @mfaCallbackFunc="CompleteAuth"
          @cancelMfaCallbackFunc="cancelAuth"
          @requestId="receiveRequestId"
        />
        <MOBILECLICK
          v-if="currentCode === 'mobileclick'"
          ref="pollMfaMethod"
          :user-id="participantKeyword"
          :participant-group-id="participantGroupId"
          @mfaCallbackFunc="CompleteAuth"
          @cancelMfaCallbackFunc="cancelAuth"
          @requestId="receiveRequestId"
        />
        <NATIVEPASS
          v-if="currentCode === 'nativepass'"
          ref="pollMfaMethod"
          :user-id="participantKeyword"
          :participant-group-id="participantGroupId"
          @mfaCallbackFunc="CompleteAuth"
          @cancelMfaCallbackFunc="cancelAuth"
          @requestId="receiveRequestId"
        />
        <EMAILOTP
          v-if="currentCode === 'email_otp'"
          ref="manualMfaMethod"
          :user-id="participantKeyword"
          :participant-group-id="participantGroupId"
          @mfaCallbackFunc="CompleteAuth"
          @cancelMfaCallbackFunc="cancelAuth"
          @requestId="receiveRequestId"
        />
        <QRCODE
          v-if="currentCode === 'qrcode'"
          ref="pollMfaMethod"
          :user-id="participantKeyword"
          :participant-group-id="participantGroupId"
          @mfaCallbackFunc="CompleteAuth"
          @cancelMfaCallbackFunc="cancelAuth"
          @requestId="receiveRequestId"
        >
          <div class="auth-tip">
            请打开<span class="mfa-text">【安全令APP】</span>进行扫码认证
          </div>
        </QRCODE>
        <TFC200
          v-if="currentCode === 'ft_c200'"
          ref="manualMfaMethod"
          :user-id="participantKeyword"
          :participant-group-id="participantGroupId"
          @mfaCallbackFunc="CompleteAuth"
          @cancelMfaCallbackFunc="cancelAuth"
          @requestId="receiveRequestId"
        />
        <ANSHUA5
          v-if="currentCode === 'anshu_a5'"
          ref="manualMfaMethod"
          :user-id="participantKeyword"
          :participant-group-id="participantGroupId"
          @mfaCallbackFunc="CompleteAuth"
          @cancelMfaCallbackFunc="cancelAuth"
          @requestId="receiveRequestId"
        />
        <ESCUOTP1
          v-if="currentCode === 'escuotp1'"
          ref="manualMfaMethod"
          :user-id="participantKeyword"
          :participant-group-id="participantGroupId"
          @mfaCallbackFunc="CompleteAuth"
          @cancelMfaCallbackFunc="cancelAuth"
          @requestId="receiveRequestId"
        />
        <WECHATOTP
          v-if="currentCode === 'wechat_otp'"
          ref="manualMfaMethod"
          :user-id="participantKeyword"
          :participant-group-id="participantGroupId"
          @mfaCallbackFunc="CompleteAuth"
          @cancelMfaCallbackFunc="cancelAuth"
          @requestId="receiveRequestId"
        />
        <ETZ203
          v-if="currentCode === 'et_z203'"
          ref="manualMfaMethod"
          :user-id="participantKeyword"
          :participant-group-id="participantGroupId"
          @mfaCallbackFunc="CompleteAuth"
          @cancelMfaCallbackFunc="cancelAuth"
          @requestId="receiveRequestId"
        />
        <UKEY
          v-if="currentCode === 'ukey_bjca'"
          ref="pollMfaMethod"
          :user-id="participantKeyword"
          :participant-group-id="participantGroupId"
          @mfaCallbackFunc="CompleteAuth"
          @cancelMfaCallbackFunc="cancelAuth"
          @requestId="receiveRequestId"
        />
        <UKEYWQ
          v-if="currentCode === 'ukey_wq'"
          ref="pollMfaMethod"
          :user-id="participantKeyword"
          :participant-group-id="participantGroupId"
          @mfaCallbackFunc="CompleteAuth"
          @cancelMfaCallbackFunc="cancelAuth"
          @requestId="receiveRequestId"
        />
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { open } from "@tauri-apps/api/shell";
import { callonApp } from "@/api/service";
import { invoke } from "@tauri-apps/api/tauri";
import FACE from "@/components/mfa/face.vue";
import FACIAL from "@/components/mfa/facial_recognition.vue";
import AUTHCODE from "@/components/mfa/authCode.vue";
import SMSOTP from "@/components/mfa/smsOtp.vue";
import FINGERPRINT from "@/components/mfa/fingerprint.vue";
import VOICE from "@/components/mfa/voice.vue";
import MOBILECLICK from "@/components/mfa/mobileClick.vue";
import NATIVEPASS from "@/components/mfa/nativepass.vue";
import EMAILOTP from "@/components/mfa/emailOtp.vue";
import QRCODE from "@/components/mfa/qrcode.vue";
import TFC200 from "@/components/mfa/ftc200.vue";
import ANSHUA5 from "@/components/mfa/anshuA5.vue";
import ESCUOTP1 from "@/components/mfa/escuotp1.vue";
import WECHATOTP from "@/components/mfa/wechatOtp.vue";
import ETZ203 from "@/components/mfa/etz203.vue";
import UKEY from "@/components/mfa/ukey.vue";
import UKEYWQ from "@/components/mfa/ukey_wq.vue";
import {
  getAccounts,
  strategiesCheckInfo,
  clientAuthResult,
  sdpResourceStrategy,
  pageResourceByUser,
  allResource,
  appGroupId,
} from "@/api/service";
import defaultImg from "/assets/images/mfa/default.png";
import {
  login,
  getLogin,
  getServiceUrl,
  setUrlParam,
  getToken,
} from "@/api/service";
// import ModuleName from '../components/mfa/approve.vue';
import { rustRequestByEventResponse } from "@/tools/invoke";
import { emit } from "@tauri-apps/api/event";
import { computed } from "vue";
import { useStore } from "vuex";

function authProxy(promise, callback) {
  promise.catch(() => {
    callback();
  });
  return promise;
}

export default {
  name: "Home",
  components: {
    FACE,
    AUTHCODE,
    SMSOTP,
    FINGERPRINT,
    VOICE,
    MOBILECLICK,
    NATIVEPASS,
    EMAILOTP,
    QRCODE,
    TFC200,
    ANSHUA5,
    ESCUOTP1,
    WECHATOTP,
    ETZ203,
    UKEY,
    UKEYWQ,
    FACIAL,
  },
  setup() {
    const store = useStore();
    return {
      networkStatus: computed(() => store.getters.networkStatus),
      connectState: computed(() => store.getters.connectState),
      isLogin: computed(() => store.getters.isLogin),
      currentUser: computed(() => store.getters.currentUser),
    };
  },
  inject: ["reload"],
  data() {
    return {
      tenantCode: window.CONFIG.selected_tenant,
      defaultAppLogo: '/assets/default_app_logo.png',
      loginState: true,
      loading: false,
      isClear: false, //是否显示清除按钮
      // #if [!includeSDP]
      busy: false,
      // #else
      busy: true,
      interval: undefined, // 询问是否准备好, 加载应用
      // #endif
      keyword: "",
      applynames: "",
      myapps: [],
      appId: "",
      appName: "",
      title: "选择认证方式",
      currentCode: "",
      clientApiKey: "",
      authResultId: "",
      accounts: [],
      selectedAccount: "",
      participantGroupId: "", //参与者组ID
      participantKeyword: "", //参与者userId
      selectAccountDialog: false, // 选择账号
      mfaAuthDialog: false, // 选择认证方式
      mfaAuthDetailDialog: false, // 选择详情
      mfaTypes: [],
      authSource: "",
      target_resource: "", //访问的目标资源
      deviceId: "", //设备ID
      hasNext: false, //设备ID
      currentPage: 0,
      totalPages: 0,
      defaultSrc: defaultImg,
      // #if [includeSDP && !includeIAM]
      list: [],
      activeText: "",
      currentSize: 0,
      totalSize: 0,
      // #endif
      requestId: "",
      // Keyword:'',
      // GroupId:''
      appType: "",
      stopPolling: true,
      lockup: false,
      homeActiveName: "all",
      currentName: "",
      institutionalList: [],
      sliceList: [],
      selectId: "",
      timer: null,
      pitchId: "",
      allResource: [],
    };
  },
  async created() {
    console.log(this.networkStatus, "联网状态");

    let that = this;

    const loadResources = function () {
      // #if [includeIAM]
      that.loadApps(true, true, 0, undefined);
      // #elif [includeSDP]
      that.loadAdmin(true, true, 0);
      // #endif
    };

    // #if [includeSDP]
    window.invokeMfaAuth = that.invokeMfaAuth;
    window.relogin = function () {
      window.previousIAMAutoLogin = true;
      rustRequestByEventResponse("plugin:sdp|ticket", "ticket")
        .then((result) => {
          const loginIam = function () {
            login(result)
              .then(() => {
                loadResources();
              })
              .finally(() => {
                window.previousIAMAutoLogin = false;
              });
          };

          loginIam();
        })
        .catch((err) => {
          // #if [includeSDP]
          window.previousIAMAutoLogin = false;
          // #endif
          emit("log", {
            level: "Error",
            message: "get ticket: " + JSON.stringify(err),
          });
          that.reload();
        });
    };

    // 检查联网状态
    if (this.networkStatus) {
      loadResources();
    } else {
      this.busy = false;
    }
    // #endif
  },
  beforeUnmount() {
    if (location.hash.includes("/login")) {
      this.stopPolling = false;
      // clearInterval(this.timer)
    }
  },
  methods: {
    getLogin() {
      getLogin().then(() => {
        this.stopPolling && window.setTimeout(this.getLogin, 1000 * 60 * 10);
      });
    },
    numberToIp(number) {
      let ip = "";
      if (number <= 0) {
        return ip;
      }
      const ip3 = (number << 0) >>> 24;
      const ip2 = (number << 8) >>> 24;
      const ip1 = (number << 16) >>> 24;
      const ip0 = (number << 24) >>> 24;
      ip += ip3 + "." + ip2 + "." + ip1 + "." + ip0;
      return ip;
    },
    /**
     * 加载应用
     *
     * @param {boolean} reload 为true时表示清空应用列表数据, 重新加载第一页
     * @param {boolean} first  是否为第一次加载
     * @param {Number} times 执行次数
     * @param {string} id 分组ID
     */
    loadApps(reload, first, times, id) {
      // if (!id) {
      //   // this.homeActiveName = 'all'
      //   id = this.pitchId;
      // }
      this.busy = true;
      if (reload === true) {
        this.currentPage = 0;
        this.myapps = [];
      }
      allResource(this.keyword).then(
        (result) => {
          if (!result) {
            this.busy = false;
            return;
          }
          // 应用组逻辑
          const container = this.$refs.textContainer;
          let indexs = null,
            num = container.clientWidth - 200;

          for (let index = 0; index < result.data.groupList.length; index++) {
            let item = result.data.groupList[index];
            if (num > this.calculateTextWidth(item.name)) {
              num -= this.calculateTextWidth(item.name);
            } else {
              indexs = index;
              break;
            }
          }
          if (indexs) {
            this.sliceList = result.data.groupList.splice(indexs);
          } else {
            this.sliceList = [];
          }
          this.institutionalList = result.data.groupList;

          // 应用逻辑
          this.resourceMethod(reload, result.data.resourceList);
          this.totalPages = Math.ceil(result.data.resourceList.length / 24);
        },
        (error) => {
          this.busy = false;
          // 如果是用户有效期无效
          if (window.previousUserLifecycleInvalid) {
            return;
          }

          if (typeof error === "string") {
            // DNS未准备好
            if (error.indexOf("os error 11004") > -1 && first === true) {
              let that = this;
              setTimeout(function () {
                that.loadApps(true, false, times + 1, id);
              }, 300);
            }
          } else if (
            error &&
            error.data &&
            error.data.errorCode !== "SYSTEM-0002"
          ) {
            emit("log", {
              level: "Error",
              message: "Load apps: session expired.",
            });
            let that = this;
            if (first === true && typeof times === "number" && times < 5) {
              setTimeout(function () {
                that.loadApps(true, true, times + 1, id);
              }, 300);
            }
          }
        }
      );
    },
    resourceMethod(reload, data) {
      this.busy = true;
      let list = [];

      if (data) {
        this.allResource = data;
      }

      if (reload === true) {
        list = this.allResource.slice(this.currentPage, 24);
      } else {
        list = this.allResource.slice(
          this.currentPage * 24,
          (this.currentPage + 1) * 24
        );
      }

      for (const index in list) {
        let app = list[index];
        if (app.avatarId) {
          app.src =
            "http://sso.jingantech.com/identity-admin/v8/v1/sdk/files/" +
            app.avatarId;
        } else {
          app.src =
            "http://sso.jingantech.com/common/images/appLibrary/" +
            app.appCode +
            ".png";
        }
      }
      this.busy = false;
      if (reload === true) {
        this.myapps = list;
      } else {
        this.myapps = this.myapps.concat(list);
      }
    },

    // #if [includeSDP && !includeIAM]
    loadAdmin(reload) {
      this.busy = true;
      if (reload === true) {
        this.currentSize = 0;
        this.list = [];
      }
      pageResourceByUser(this.applynames, this.currentSize, 24).then((res) => {
        for (const index in res.data.content) {
          let app = res.data.content[index];
          if (app.avatarId) {
            app.src =
              "http://sso.jingantech.com/identity-admin/v8/v1/sdk/files/" +
              app.avatarId;
          } else {
            app.src =
              "http://sso.jingantech.com/common/images/appLibrary/" +
              app.appCode +
              ".png";
          }
          this.list.push(app);
        }
        this.totalPages = res.data.totalPages;
        this.busy = false;
      });
    },
    seekapp() {
      if (!this.busy) {
        // this.activeText=""
        this.loadAdmin(true);
      }
    },
    clearnames() {
      if (!this.busy) {
        this.applynames = "";
        this.loadAdmin(true);
      }
    },
    async activeBox(res) {
      // let ids = res.hide ? res.dummyAddress : res.ipAddress
      // this.activeText = res.name;
      if (!res.visitAddress) {
        this.alert.error("跳转失败，请联系管理员配置该资源的访问地址");
        return;
      }
      if (res.type == 2) {
        this.alert.error("请选择数据库访问工具访问");
        return;
      }
      await open(
        res.visitAddress.includes("http") || res.visitAddress.includes("https")
          ? res.visitAddress
          : "http://" + res.visitAddress
      );
    },
    // #endif
    loadMore() {
      clearInterval(this.interval);
      if (this.currentPage < this.totalPages - 1) {
        this.currentPage++;
        if (this.homeActiveName == "all") {
          this.resourceMethod(false, undefined);
        } else if (this.homeActiveName == "culture") {
          this.institutionalFramework(false, this.selectId);
        } else {
          this.institutionalFramework(false, this.homeActiveName);
        }
      }
    },
    sdploadMore() {
      clearInterval(this.interval);
      if (this.currentSize < this.totalPages - 1) {
        this.currentSize++;
        this.loadAdmin();
      }
    },
    searchApps() {
      if (!this.busy) {
        clearInterval(this.interval);
        if (this.homeActiveName == "all") {
          this.loadApps(true, false, 0, undefined);
          this.selectId = "";
        } else if (this.homeActiveName == "culture") {
          this.institutionalFramework(true, this.selectId);
        } else {
          this.institutionalFramework(true, this.homeActiveName);
        }
      }
    },
    clearApps() {
      if (!this.busy) {
        this.keyword = "";
        if (this.homeActiveName == "all") {
          this.loadApps(true, false, 0, undefined);
          this.selectId = "";
        } else if (this.homeActiveName == "culture") {
          this.institutionalFramework(true, this.selectId);
        } else {
          this.institutionalFramework(true, this.homeActiveName);
        }
      }
    },
    // 获取认证信息
    invokeMfaAuth() {
      if (this.lockup) return;
      invoke("plugin:sdp|pop_mfa_event")
        .then((result) => {
          if (result === null || result === undefined) {
            return;
          }
          this.lockup = true;
          this.appName = "";
          this.authSource = "INVOKE";
          this.target_resource = result;

          let param = {
            username: this.currentUser.username,
            deviceId: window.DEVICE_ID,
            target: this.target_resource,
          };
          authProxy(sdpResourceStrategy(param), this.cancelAuth).then(
            (data) => {
              this.getStrategyInfoAfter(data);
            }
          );
        })
        .catch((res) => {
          // 204 表示没有下一个需要认证后访问的资源
          if (!res || res.code != 204) {
            // 获取失败... 等待一会儿开启下一次调用
            setTimeout(this.invokeMfaAuth, 1000);
          }
        });
    },
    visit(app) {
      if (this.lockup) return;
      emit("log", { level: "Debug", message: "single sign on" });
      this.appId = app.id;
      this.appData = app;
      this.appName = app.name;
      this.clientApiKey = app.clientApiKey;
      this.loading = true;
      this.lockup = true;
      this.appType = app.type;
      this.authSource = "";
      this.currentCode = "";
      // 移动端不允许直接跳转
      if (app.clientType === "MOBILE") {
        this.$message({
          message: "请打开移动客户端访问应用",
          type: "warning",
        });
        this.lockup = false;
        this.loading = false;
        return;
      }

      if (app.type === "NGIAM_ADMIN") {
        getToken().then((token) => {
          getServiceUrl("identity-admin").then((url) => {
            url = setUrlParam(
              "token",
              token.data,
              url.data + "/dashboard/index.html"
            );
            url = setUrlParam("tenant", app.tenant, url);
            open(url);
          });
        });
        this.lockup = false;
        this.loading = false;
        return;
      }
      if (!app.ssoEnabled) {
        app.accessAddress
          ? open(app.accessAddress)
          : this.alert.error("跳转失败，请联系管理员配置该应用的访问地址");
        this.lockup = false;
        this.loading = false;
        return;
      }
      // PC端跳转认证
      this.loadAccounts();
    },
    logout() {
      this.$confirm("是否退出登录?", "退出登录", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        center: true,
        closeOnClickModal: false,
        closeOnPressEscape: false,
      })
        .then(() => {
          this.loading = true;
          invoke("plugin:sdp|logout")
            .then(() => {
              this.stopPolling = false;
              this.$store.commit("updateIsLogin", false);
              window.processMfaAuthState = false;
              this.$router.push({
                path: "/login",
                query: { date: new Date().getTime() },
              });
            })
            .finally(() => {
              this.loading = false;
            });
        })
        .catch(() => {
          this.loading = false;
        });
    },
    // mfa 相关
    loadAccounts() {
      getAccounts(this.appId)
        .then((res) => {
          this.accounts = res.data;
          if (!this.accounts.length) {
            this.lockup = false;
            this.loading = false;
            return this.alert.error("登录失败，没有绑定账号");
          }

          if (this.accounts.length === 1) {
            this.selectedAccount = this.accounts[0];
            this.getStrategiesInfo();
          } else {
            window.processMfaAuthState = this.selectAccountDialog = true;
            this.loading = false;
          }
        })
        .catch(() => {
          this.lockup = false;
          this.loading = false;
        });
    },
    selectAccount(account) {
      this.selectAccountDialog = false;
      this.loading = true;
      this.selectedAccount = account;
      this.getStrategiesInfo();
    },
    cancelSelectAccount() {
      this.authResultId = "";
      this.participantGroupId = "";
      this.participantKeyword = "";
      this.mfaAuthDialog = false;
      window.processMfaAuthState = this.selectAccountDialog = false;
      this.target_resource = "";
      this.appName = "";
      this.loading = false;
      this.invokeMfaAuth();
      this.lockup = false;
    },
    mfaDetail() {
      this.loadAccounts();
    },
    getStrategiesInfo() {
      strategiesCheckInfo(this.clientApiKey, this.selectedAccount.accountName)
        .then((data) => {
          this.getStrategyInfoAfter(data);
        })
        .catch((err) => {
          if (err && err.messageKey === "MFA.CHECK.DENY") {
            this.$message.error(err.errorMsg);
          }
          this.appName = "";
          if (this.authSource === "INVOKE") {
            invoke("plugin:sdp|cancel_auth", { ipAddr: this.target_resource });
          }
          this.lockup = false;
          this.loading = false;
        });
    },
    getStrategyInfoAfter(data) {
      this.authResultId = data.data.id;
      if (!data || !data.data) {
        this.alert.error("系统错误");
        window.processMfaAuthState = this.loading = false;
      } else if (data.code != "SUCCESS") {
        this.alert.error("系统错误");
        window.processMfaAuthState = this.loading = false;
      } else if (!data.data.enabled || data.data.auth) {
        if (this.authSource === "INVOKE") {
          this.CompleteAuth(true);
        } else {
          // 认证已完成
          this.callonApp();
        }
      } else {
        let decision = data.data.decision;
        switch (decision) {
          case "PERMIT":
            this.alert.error(data.data);
            window.processMfaAuthState = this.loading = false;
            break;
          case "DENY":
            // this.alert.error('禁止操作')
            this.$alert(
              '<div class="NoActionMsg">您没有权限访问此资源<br/>请联系系统管理员</div>',
              "提示",
              {
                dangerouslyUseHTMLString: true,
                showConfirmButton: false,
              }
            );
            window.processMfaAuthState = this.loading = false;
            break;
          case "MFA":
            this.participantGroupId = data.data.participantGroups[0].id;
            this.participantKeyword =
              data.data.participantGroups[0].participants[0].participant;
            this.mfaTypes =
              data.data.participantGroups[0].participants[0].mfaMethods;
            if (this.mfaTypes.length > 1) {
              this.currentCode = "";
              window.processMfaAuthState = this.mfaAuthDialog = true;
            } else {
              this.currentName = this.mfaTypes[0].method.name;
              this.currentCode = this.mfaTypes[0].method.code;
              this.mfaAuthDialog = false;
              this.mfaAuthDetailDialog = true;
            }
            // window.processMfaAuthState = this.mfaAuthDialog = true
            this.loading = false;
            break;
        }
      }
    },
    convertToNestedArray(array) {
      var nestedArray = [];
      var tempArray = [];
      for (var i = 0; i < array.length; i++) {
        tempArray.push(array[i]);

        if (tempArray.length === 6) {
          nestedArray.push(tempArray);
          tempArray = [];
        }
      }
      if (tempArray.length > 0) {
        nestedArray.push(tempArray);
      }
      return nestedArray;
    },
    selectMfaType(code, name) {
      if (code == "facial_recognition") {
        invoke("plugin:bio|is_support_facial_recognition").then((support) => {
          // 不支持面部识别
          if (!support) {
            this.alert.error("检测到设备不支持人脸识别，请换其他方式认证");
            return;
          }

          this.currentName = name;
          this.currentCode = code;
          this.mfaAuthDialog = false;
          this.mfaAuthDetailDialog = true;
        });
      } else {
        this.currentName = name;
        this.currentCode = code;
        this.mfaAuthDialog = false;
        this.mfaAuthDetailDialog = true;
      }
    },
    /**
     * MFA认证完成
     *
     * @param hasVerify
     * @constructor
     */
    CompleteAuth(hasVerify) {
      // TODO 抽一个clean的方法, 清理认证中的各种中间状态
      if (this.authSource === "INVOKE") {
        invoke("plugin:sdp|auth_complete", {
          authResultId: this.authResultId,
          ipAddr: this.target_resource,
        }).then((hasNext) => {
          !hasVerify ? this.alert.success("认证成功") : "";
          window.processMfaAuthState = this.mfaAuthDetailDialog = false;
          this.authSource = "";
          this.lockup = false;
          if (hasNext) {
            this.invokeMfaAuth();
            return;
          }
        });
      } else {
        clientAuthResult(this.authResultId).then((data) => {
          if (data.data.auth) {
            !hasVerify ? this.alert.success("认证成功") : "";
            window.processMfaAuthState = this.mfaAuthDetailDialog = false;
            this.authSource = "";
            this.lockup = false;
            //todo 调取认证成功事件
            this.callonApp();
          } else {
            var way = data.data.participantGroups[0].participants[0].mfaMethods;

            for (var i = 0, len = way.length; i < len; i++) {
              var e = way[i];
              if (!e.auth) {
                this.currentName = e.method.name;
                this.currentCode = e.method.code;
                break;
              }
            }
          }
        });
      }
      this.target_resource = null;
    },
    callonApp() {
      callonApp(this.appId, this.selectedAccount.id, this.authResultId)
        .then((result) => {
          // TODO 抽一个clean的方法, 清理认证中的各种中间状态
          this.appName = null;
          const ssoType = result.data.ssoType || result.data.SSOType;
          switch (ssoType) {
            case "CAS_CLIENT":
              if (this.appType === "NGIAM_ADMIN") {
                var url =
                  result.data.content.service +
                  "?ticket=" +
                  result.data.content.st +
                  "&tenant=" +
                  this.tenant.code;
              } else {
                var url =
                  result.data.content.service +
                  "?ticket=" +
                  result.data.content.st;
              }
              open(url).catch((e) => {
                emit("log", {
                  level: "Error",
                  message: "Open url: " + JSON.stringify(e),
                });
              });
              window.processMfaAuthState = this.loading = false;
              this.invokeMfaAuth();
              return;
            case "CAS_WINDOWS":
              var url = result.data.content.agreementName;
              var st = result.data.content.st;
              url += "://" + st;
              open(url).catch((e) => {
                emit("log", {
                  level: "Error",
                  message: "Open url: " + JSON.stringify(e),
                });
              });
              window.processMfaAuthState = this.loading = false;
              this.invokeMfaAuth();
              return;
          }

          invoke("plugin:iam|sso_result", { result: JSON.stringify(result) })
            .then(() => {
              window.processMfaAuthState = this.loading = false;
              this.invokeMfaAuth();
            })
            .catch(() => {
              window.processMfaAuthState = this.loading = false;
              this.alert.error("网络连接异常, 请稍后再试");
            });
        })
        .catch((err) => {
          window.processMfaAuthState = this.loading = false;
        })
        .finally(() => {
          this.lockup = false;
        });
    },
    cancelAuth() {
      this.authResultId = "";
      this.participantGroupId = "";
      this.participantKeyword = "";
      this.mfaAuthDialog = false;
      window.processMfaAuthState = this.mfaAuthDetailDialog = false;
      this.authSource = "";
      this.appName = "";
      this.$refs.pollMfaMethod && this.$refs.pollMfaMethod.cancel();
      if (this.authSource === "INVOKE") {
        invoke("plugin:sdp|cancel_auth", { ipAddr: this.target_resource })
          .then((hasNext) => {
            if (hasNext) {
              this.invokeMfaAuth();
            }
          })
          .catch((err) => {
            emit("log", { level: "Error", message: JSON.stringify(err) });
          });
        this.target_resource = "";
      } else {
        this.target_resource = "";
        this.invokeMfaAuth();
      }
    },
    goSetting() {
      this.$router.push({
        path: "/setting",
        query: { date: new Date().getTime() },
      });
    },
    // 接收生成的RequestID
    receiveRequestId(res) {
      this.requestId = res;
    },
    //取消请求/外层弹框
    closeBox() {
      window.processMfaAuthState = false;
      this.lockup = false;
      if (this.authSource === "INVOKE") {
        invoke("plugin:sdp|cancel_auth", { ipAddr: this.target_resource }).then(
          () => {
            this.invokeMfaAuth();
          }
        );
      } else {
        this.invokeMfaAuth();
      }
      this.$refs.pollMfaMethod && this.$refs.pollMfaMethod.cancel();
      this.mfaAuthDetailDialog = false;
      this.mfaAuthDialog = false;
      this.appName = "";
      this.target_resource = "";
      this.currentCode = "";
    },
    // 内层弹框
    coreBox() {
      window.processMfaAuthState = false;
      // this.lockup = false
      if (this.authSource === "INVOKE") {
        invoke("plugin:sdp|cancel_auth", { ipAddr: this.target_resource }).then(
          () => {
            this.invokeMfaAuth();
          }
        );
      } else {
        this.invokeMfaAuth();
      }
      this.$refs.pollMfaMethod && this.$refs.pollMfaMethod.cancel();
      this.mfaAuthDetailDialog = false;
      // this.mfaAuthDialog = false
      // this.appName = ''
      this.target_resource = "";
    },

    // 分组选中获取ID
    handleClick(tab, event) {
      this.keyword = "";
      if (tab.props.name == "all") {
        this.pitchId = "";
        this.loadApps(true, false, 0, undefined);
      } else if (tab.props.name != "all" && tab.props.name != "culture") {
        this.pitchId = tab.props.name;
        this.institutionalFramework(true, tab.props.name);
      }
      if (tab.props.name != "culture") {
        this.selectId = "";
      }
    },
    calculateTextWidth(text) {
      const span = document.createElement("span");
      span.textContent = text;
      span.style.visibility = "hidden";
      span.style.whiteSpace = "nowrap";
      document.body.appendChild(span);

      const textWidth = span.getBoundingClientRect().width;

      document.body.removeChild(span);

      return Math.floor(textWidth) + 20;
    },

    // 根据应用组id查应用
    institutionalFramework(reload, id) {
      this.busy = true;
      if (reload === true) {
        this.currentPage = 0;
        this.myapps = [];
      }
      appGroupId(this.keyword, id, this.currentPage, 24)
        .then((result) => {
          if (!result) {
            this.busy = false;
            return;
          }

          for (const index in result.data.content) {
            let app = result.data.content[index];
            if (app.avatarId) {
              app.src =
                "http://sso.jingantech.com/identity-admin/v8/v1/sdk/files/" +
                app.avatarId;
            } else {
              app.src =
                "http://sso.jingantech.com/common/images/appLibrary/" +
                app.appCode +
                ".png";
            }
          }

          this.busy = false;
          if (reload === true) {
            this.myapps = result.data.content;
          } else {
            this.myapps = this.myapps.concat(result.data.content);
          }
          this.totalPages = result.data.totalPages;
        })
        .catch((err) => {
          console.log("err: ", err);
          this.busy = false;
        });
    },
    // 下拉菜单选中ID
    handleCommand(res) {
      this.homeActiveName = "culture";
      this.selectId = res;

      this.institutionalFramework(true, res);
    },
    // 移入事件
    showHighlightedImage(res) {
      this.currentCode = res;
    },
  },
};
</script>

<style lang="less" scoped>
.app-home {
  flex: 1;
  background: #fafafa;
  display: flex;
  flex-direction: column;

  .title-icon {
    color: #fff;
  }
}

.home-header {
  background-color: #ee761b;
  height: 160px;
  width: 100%;
  clear: both;
  overflow: hidden;
  padding: 60px 20px 0;
  box-sizing: border-box;

  .home-header-left {
    float: left;
    color: #fff;

    .home_icon_user {
      font-size: 60px;
      float: left;
    }

    .loginInfo {
      margin-top: 2px;
      float: left;
      line-height: 30px;
      vertical-align: middle;

      .loginInfo-bottom {
        line-height: 20px;
        margin-top: 5px;
      }

      .home_icon_check {
        font-size: 18px;
        float: left;
      }

      .name-status {
        float: left;
        font-size: 13px;
        letter-spacing: 1px;
      }
    }
  }

  .home-header-right {
    float: right;
    padding: 15px 0;

    .home-btn {
      border: 0;
    }
  }
}

.home-body {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  background-color: #fff;

  .home_nav {
    padding: 0 25px;
  }

  .home_header {
    padding: 0 25px;
    display: flex;
    justify-content: space-between;
    align-items: center;

    h2 {
      font-size: 18px;
      font-weight: normal;
    }
  }

  .app-search {
    position: relative;
    // margin: 5vw 5vw 0 5vw;
    width: 260px;

    .app-search-input {
      border-radius: 5px;
      border-color: #e8e8e8;

      font-size: 12px;

      :deep(.el-input__wrapper) {
        border-radius: 20px;
        height: 34px;
        background-color: #f4f5f7;
      }

      :deep(.el-input__inner) {
        padding-right: 70px;
      }
    }

    .app-search-clear {
      position: absolute;
      top: 9px;
      right: 45px;
      cursor: pointer;
      // font-size: 13px;
      color: #999;
    }

    .app-search-btn {
      position: absolute;
      top: 10px;
      right: 10px;
      border-left: 1px solid #e8e8e8;
      color: #f9780c;
      padding-left: 10px;
      cursor: pointer;
    }
  }

  .home-body-item {
    width: 33%;
    padding-bottom: 5px;
    display: inline-block;
    text-align: center;
    overflow: hidden;
    clear: both;
    margin: 15px auto 0;

    :hover .app-img {
      border-color: #f9780c;
    }

    .app-img {
      position: relative;
      width: 80%;
      height: 68px;
      border: 1px solid #e0e0e0;
      border-radius: 10px;
      overflow: hidden;
      margin: auto;
      cursor: pointer;
    }

    .pc-icon {
      position: absolute;
      width: 19px;
      height: 21px;
      background: url("../../public/assets/images/pc-icon.png");
    }

    .phone-icon {
      position: absolute;
      width: 19px;
      height: 21px;
      background: url("../../public/assets/images/phone-icon.png");
    }

    img {
      width: 100%;
      height: 100%;
    }

    .appName {
      height: 30px;
      vertical-align: middle;
      color: #666;
      font-size: 12px;
      text-align: center;
      line-height: 30px;
      width: 100%;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
  }

  .help-text {
    width: 100%;
    line-height: 60px;
    font-size: 15px;
    display: flex;
    flex-direction: column;
    // justify-content: center;
    align-items: center;
    font-weight: 400;
    color: #9c9ea3;
    margin: 14% auto;
  }

  .infinite-scroll {
    // height: 380px;
    overflow-x: hidden;
    overflow-y: auto;
    // padding: 0vw 5vw;
  }
}

.home_footer {
  display: flex;
  flex-wrap: wrap;
  align-content: flex-start;
  padding: 10px 25px 4px;
  gap: 20px;

  .singleApp {
    cursor: pointer;
    border: 1px solid #ddd;
    display: flex;
    align-items: center;
    width: 194px;
    height: 58px;
    box-shadow: 2px 2px 4px rgb(131 122 122 / 0.2);
    border-radius: 10px;
    font-size: 12px;
    box-sizing: border-box;
    word-wrap: break-word;
    &_logo {
      margin: 3px 10px 0;

      img {
        border: 1px solid #ddd;
        border-radius: 10px;
      }
    }
    &_title {
      overflow: hidden;
    }
  }
}

.home-btn {
  display: inline-block;
  background-color: #fff;
  height: 33px;
  line-height: 33px;
  padding: 0;
  font-size: 15px;
  color: #f9780c;
  text-align: center;
  width: 99px;
  letter-spacing: 1px;
  font-weight: 600;
  border-radius: 8px;
  box-shadow: 0 0 0 1px #eee;
}

/* el-dialog关闭按钮颜色 */
:deep(.el-dialog__headerbtn:hover .el-dialog__close) {
  color: #f9780c;
}

/* el-scrollbar滚动条宽度 */
:deep(.el-scrollbar__bar) {
  &.is-vertical {
    width: 4px;
    height: 4px;
  }
}

/* el-scrollbar滚动条颜色 */
:deep(.el-scrollbar__thumb) {
  background: rgba(0, 0, 0, 0.1);
  -webkit-box-shadow: inset006pxrgba(0, 0, 0, 0.5);
}

/* 设置滚动条的样式 */
::-webkit-scrollbar {
  width: 4px;
  height: 4px;
}

/* 后期添加的滚动条 */
/* 滚动槽 */
::-webkit-scrollbar-track {
  -webkit-box-shadow: inset006pxrgba(0, 0, 0, 0.3);
  border-radius: 10px;
}

/* 滚动条滑块 */
::-webkit-scrollbar-thumb {
  border-radius: 10px;
  background: rgba(0, 0, 0, 0.1);
  -webkit-box-shadow: inset006pxrgba(0, 0, 0, 0.5);
}

.account-model {
  height: 198px;
  overflow: auto;
  margin-top: 14px;
  text-align: -webkit-center;
  // padding: 20px 0;
  .account-item {
    /*文字超出宽度则显示ellipsis省略号*/
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    height: 40px;
    line-height: 40px;
    text-align: center;
    cursor: pointer;
    width: 264px;
    border-radius: 8px;
    opacity: 1;
    border: 1px solid #e5e6e8;
    margin-bottom: 8px;

    &:hover {
      color: #f9780c;
      border: 1px solid #f9780c;
    }

    :last-manualmfamethod {
      border: 0;
    }
  }
}

:deep(.el-dialog--center .el-dialog__body) {
  padding: 0;
}

.for {
  padding: 0 30px;

  &_title {
    padding: 0px 20px;
    display: flex;
    align-items: center;
    cursor: pointer;
    border-bottom: 1px solid rgb(226, 226, 226);

    .itemText {
      font-size: 14px;
    }
  }

  .size:hover {
    color: #f9780c;
  }

  .activeso {
    color: #f9780c;
  }

  .ipstyle {
    font-size: 12px;
    color: #adadad;
  }

  .size {
    font-size: 16px;
    margin-bottom: 2px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}

.df {
  display: flex;
  flex-direction: column;
  margin: 10px 0;
}

.network_alert {
  color: #f87f1a;
  background-color: #ffedde;
  display: flex;
  align-items: baseline;
  top: 30px;
  left: 3%;
  width: 94%;
}

.el-carousel__item {
  height: 200px;
  display: flex;
  align-items: center;
  justify-content: space-evenly;
}

:deep(.el-carousel__indicators--horizontal) {
  display: none;
}

:deep(.el-carousel__arrow--right) {
  right: 10px;
  top: 108px;
}

:deep(.el-carousel__arrow--left) {
  left: 10px;
  top: 108px;
}

.el-tabs {
  margin-right: 20px;
}

.mfaTypesCode {
  width: 60px;
  height: 100px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  margin: 10px 25px 0 0;
  // flex-wrap: wrap;
  span {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    font-size: 12px;
    margin-top: 10px;
    width: 100%;
    text-align: center;
  }
}

:deep(.el-tabs__item) {
  padding: 0 18px;
  //width: 84px;
  //overflow: hidden;
}

:deep(.el-dropdown-menu__item) {
  padding: 5px 0 5px 15px;
}

:deep(.el-dropdown-menu__item i) {
  /* margin-right: -24px; */
  color: #f87f1a;
}

:deep(.el-dropdown-menu__item:not(.is-disabled):hover) {
  background-image: linear-gradient(to right, #f4f5f7, #f4f5f7);
  color: #000;
}
:deep(#tab-culture) {
  margin-left: auto;
}
:deep(.el-tabs__nav) {
  width: 100%;
}
.is_active {
  display: inline-block;
  width: 19px;
  height: 22px;
}
.dropdownClass {
  width: 150px;
  white-space: normal;
  word-break: break-word;
  overflow-wrap: break-word;
}
.more-link {
  outline: 0;
}
</style>

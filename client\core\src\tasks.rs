use std::{collections::HashMap, future::Future, pin::Pin, time::Duration};

use tokio::{
    sync::{mpsc, oneshot},
    task::Join<PERSON><PERSON><PERSON>,
};

pub mod environment;
pub mod refresh_ticket;
const TASK_RUNNER_SHUTDOWN_TIMEOUT: Duration = Duration::from_secs(5);

pub enum TaskCommand {
    AddTask(
        (
            &'static str,
            Pin<Box<dyn Future<Output = ()> + Send + 'static>>,
        ),
    ),
    CancelTask(&'static str),
}

pub async fn spawn() -> TaskHandle {
    let (command_tx, command_rx) = mpsc::channel(1);
    let (shutdown_tx, shutdown_rx) = oneshot::channel();

    let tasks_runner = TasksRunner {
        command_rx,
        tasks: Default::default(),
    };
    tokio::spawn(async move {
        tasks_runner.run().await;
        if shutdown_tx.send(()).is_err() {
            log::error!("Can't send shutdown completion to task");
        }
    });

    TaskHandle {
        command_tx,
        shutdown_rx,
    }
}

struct TasksRunner {
    command_rx: mpsc::Receiver<TaskCommand>,
    tasks: HashMap<&'static str, JoinHandle<()>>,
}

impl TasksRunner {
    pub async fn run(mut self) {
        loop {
            let Some(command) = self.command_rx.recv().await else {
                return;
            };

            match command {
                TaskCommand::AddTask((identifier, task)) => {
                    if let Some(handle) = self.tasks.remove(identifier) {
                        handle.abort();
                        log::debug!("Cancel task: {}", identifier);
                    }
                    log::debug!("Register task: {identifier}");
                    let handle = tokio::spawn(task);
                    self.tasks.insert(identifier, handle);
                }
                TaskCommand::CancelTask(identifier) => {
                    if let Some(handle) = self.tasks.remove(identifier) {
                        handle.abort();
                        log::debug!("Cancel task: {identifier}");
                    }
                }
            }
        }
    }
}

pub struct TaskHandle {
    command_tx: mpsc::Sender<TaskCommand>,
    shutdown_rx: oneshot::Receiver<()>,
}

impl TaskHandle {
    /// Waits for the task runner to shut down.
    /// This may fail after a timeout of `TASK_RUNNER_SHUTDOWN_TIMEOUT`.
    pub async fn try_join(self) {
        drop(self.command_tx);

        match tokio::time::timeout(TASK_RUNNER_SHUTDOWN_TIMEOUT, self.shutdown_rx).await {
            Ok(_) => log::debug!("Task runner shut down"),
            Err(_) => log::error!("Task runner did not shut down gracefully"),
        }
    }

    pub fn command_tx(&self) -> mpsc::Sender<TaskCommand> {
        self.command_tx.clone()
    }
}

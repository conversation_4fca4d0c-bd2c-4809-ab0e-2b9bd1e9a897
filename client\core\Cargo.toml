[package]
edition = "2021"
name = "oneidcore"
version = "0.0.0"

[package.metadata.winresource]
FileDescription = "安全令内核"
LegalCopyright = "© 2022 - 2024 景安云信. All rights reserved."
OriginalFilename = "oneidcore.exe"
ProductName = "安全令内核"

[dependencies]
async-trait = "0.1.68"
base = { path = "../../base" }
base64 = "0.21.4"
bytes = "1.5.0"
clap = { version = "4.2.1", features = ["derive", "string"] }
communicate-interface = { path = "../communicate-interface" }
console-subscriber = { version = "0.1.8", optional = true }
encrypt-files = { path = "../encrypt-files" }
err_ext = { path = "../../err_ext" }
exception_logging = { workspace = true }
faster-hex = "0.6.1"
flate2 = "1.0.28"
futures = "0.3.28"
futures-channel = { version = "0.3.31", default-features = false }
futures-util = { version = "0.3.31", default-features = false }
gm-sm2 = { workspace = true }
gm-sm3 = { workspace = true }
hex = { workspace = true }
hickory-proto = { version = "0.24.2", optional = true }
hickory-server = { version = "0.24.2", optional = true, features = [
    "hickory-resolver",
] }
http = "1.1.0"
http-body-util = "0.1.2"
hyper = { version = "1.5.0", features = ["full"] }
hyper-util = { version = "0.1.10", features = ["full"] }
ipnet = { version = "2.7.2", features = ["json"] }
ipnetwork = "0.20.0"
libc = "0.2.141"
libloading = "0.8.0"
log = "0.4.17"
log-panics = { version = "2", features = ["with-backtrace"] }
minisign-verify = "0.2.1"
moka = { version = "0.12", features = ["future"] }
netdev = "0.27.0"
once_cell = { workspace = true }
parking_lot = "0.12.1"
paths = { workspace = true }
pkcs8 = { workspace = true }
proxy-request = { version = "0.0.0", path = "../../proxy-request" }
rand = "0.8.5"
rcgen = { git = "https://gitee.com/luoffei/rcgen.git", version = "0.13.3", features = [
    "x509-parser",
] }
regex = "1.8.1"
reqwest = { version = "0.12.6", default-features = false, features = [
    "blocking",
    "charset",
    "http2",
    "json",
    "macos-system-configuration",
    "rustls-tls",
    "stream",
] }
rustls = { version = "0.23.23", default-features = false, features = [
    "logging",
    "std",
    "tls12",
    "tlcp11",
    "ring",
] }
rustls-pemfile = "1.0.2"
semver = { version = "1.0.20", features = ["serde"] }
serde = { version = "1.0.159", features = ["derive"] }
serde_json = "1.0.95"
socket2 = "0.5.2"
sysinfo = { git = "https://gitee.com/luoffei/sysinfo.git", branch = "0.33" }
tar = "0.4.40"
tempfile = "3.8.0"
thiserror = "2.0.4"
time = { version = "0.3.15", features = [
    "macros",
    "serde",
    "parsing",
    "local-offset",
] }
tlv = { path = "../../tlv/tlv" }
tlv-types = { path = "../../tlv/tlv-types" }
tokio = { workspace = true, features = ["full"] }
tokio-rustls = { version = "0.26.2", default-features = false, features = [
    "logging",
    "tls12",
    "ring",
] }
tokio-stream = { version = "0.1.12", features = ["io-util"] }
tokio-tungstenite = { version = "0.26.2", optional = true, features = [
    "__rustls-tls",
] }
tokio-util = "0.7.7"
tonic = "0.12"
tracing-appender = "0.2.2"
tracing-subscriber = { version = "0.3.16", features = [
    "time",
    "local-time",
    "env-filter",
] }
tun-rs = { workspace = true, features = ["async"] }
types = { path = "../types" }
uniqueid = { workspace = true }
untrusted = "0.9.0"
url = { version = "2.4.1", features = ["serde"] }
version = { path = "../../version" }
zip = "0.6.6"

[target.'cfg(windows)'.dependencies]
ctrlc = "3.2.5"
# network-interface = "1.0.0"
encoding = "0.2.33"
netsh = { workspace = true }
once_cell = "1.17.1"
widestring = "1.0"
winapi = { version = "0.3", features = ["winnt", "excpt"] }
windows = { version = "0.58.0", features = [
    "Win32_Foundation",
    "Win32_Security",
    "Win32_System_Threading",
    "Win32_UI_WindowsAndMessaging",
    "Win32_Networking_WinSock",
    "Win32_NetworkManagement_Ndis",
    "Win32_NetworkManagement_IpHelper",
    "Win32_System_SystemInformation",
    "Win32_NetworkManagement_NetManagement",
    "Win32_NetworkManagement_WindowsFirewall",
    "Win32_Networking_ActiveDirectory",
    "Win32_System_Com",
    "Win32_Networking_NetworkListManager",
    "Win32_System_Ole",
    "Win32_System_Power",
] }
windows-net = { path = "../windows-net" }
winreg = { version = "0.10.1", features = ["transactions"] }

[target.'cfg(windows)'.dependencies.windows-sys]
features = [
    "Win32_Foundation",
    "Win32_Security",
    "Win32_Security_Authorization",
    "Win32_Security_Authentication_Identity",
    "Win32_System_Diagnostics_Debug",
    "Win32_System_Diagnostics_ToolHelp",
    "Win32_System_Kernel",
    "Win32_System_Memory",
    "Win32_System_Threading",
    "Win32_NetworkManagement_WiFi",
    "Win32_NetworkManagement_IpHelper",
    "Win32_NetworkManagement_Ndis",
    "Win32_System_LibraryLoader",
    "Win32_System_Rpc",
    "Win32_System_WindowsProgramming",
    "Win32_System_SystemInformation",
    "Win32_System_Threading",
    "Win32_UI_WindowsAndMessaging",
]
version = "0.52.0"

[target.'cfg(unix)'.dependencies]
simple-signal = "1.1.1"

[target.'cfg(target_os = "linux")'.dependencies]
duct = "0.13.6"
freedesktop-desktop-entry = "0.7.5"
inotify = "0.10.0"
netlink-packet-core = "0.7.0"
netlink-packet-route = { version = "0.19.0", features = ["rich_nlas"] }
netlink-proto = "0.11.3"
netlink-sys = "0.8.6"
nix = "0.26.2"
resolv-conf = "0.7.0"
rtnetlink = "0.14.1"
sdp-dbus = { path = "../sdp-dbus" }
systeminfo = { git = "https://github.com/marirs/systeminfo-rs", branch = "main" }
triggered = "0.1.2"
which = { version = "4.4.0", default-features = false }

[target.'cfg(target_os = "macos")'.dependencies]
bitflags = "2.6.0"
core-foundation = { git = "https://github.com/luoffei/core-foundation-rs", branch = "core-foundation-0.9.3" }
core-foundation-sys = { git = "https://github.com/luoffei/core-foundation-rs", branch = "core-foundation-0.9.3" }
nix = { version = "0.28", features = ["socket", "fs", "net"] }
system-configuration = "0.5.0"

[target.'cfg(windows)'.build-dependencies]
semver = "1.0.20"
version = { path = "../../version" }
winresource = "0.1.17"

[target.'cfg(windows)'.build-dependencies.windows-sys]
features = ["Win32_System_SystemServices"]
version = "0.45.0"

[features]
default = ["sdp", "iam", "tlcp"]
iam = []
sdp = []
tlcp = ["sdp", "base/tlcp", "rustls/tlcp11"]
trace = ["dep:console-subscriber"]
# 溯源
traceability = ["sdp", "dep:tokio-tungstenite"]
# DNS服务, 支持域名访问资源
dns_server = ["sdp", "dep:hickory-proto", "dep:hickory-server"]

[build-dependencies]
bindgen = "0.68.1"
built = { version = "0.7", features = ["git2"] }
time = { version = "0.3", features = ["formatting", "local-offset"] }

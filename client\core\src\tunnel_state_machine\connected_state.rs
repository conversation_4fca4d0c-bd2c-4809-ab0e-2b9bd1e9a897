use std::{
    collections::{hash_set::Iter, HashSet},
    net::IpAddr,
};

use futures::{
    channel::{mpsc, oneshot},
    StreamExt,
};

use err_ext::ErrorExt;
use ipnet::IpNet;
use ipnetwork::IpNetwork;
use tokio::task::<PERSON><PERSON><PERSON><PERSON><PERSON>;
use types::{tunnel::TunnelStateTransition, BoxedError};

#[cfg(windows)]
use crate::tunnel;
use crate::{
    routing::{Node, RequiredRoute},
    tunnel::TunTunnel,
    tunnel_state_machine::{
        disconnected_state::DisconnectedState, error_state::ErrorState, EventResult,
    },
};

use super::{
    EventConsequence, ResourceEvent, SharedTunnelStateValues, TunnelCommand, TunnelCommandReceiver,
    TunnelEvent, TunnelMetadata, TunnelState, TunnelStateWrapper,
};

pub(crate) type TunnelEventsReceiver = mpsc::Receiver<TunnelEvent>;

pub struct ConnectedStateBootstrap {
    pub tunnel: TunTunnel,
    pub metadata: TunnelMetadata,
    pub tunnel_events: TunnelEventsReceiver,
}

pub struct ConnectedState {
    metadata: TunnelMetadata,
    tunnel: TunTunnel,
    tunnel_events: TunnelEventsReceiver,
    tunnel_close_tx: Option<oneshot::Sender<()>>,
    tunnel_packet_tx: Option<mpsc::UnboundedSender<Vec<u8>>>,
    auth_resource_tx: Option<mpsc::UnboundedSender<ResourceEvent>>,
    #[cfg(feature = "traceability")]
    traceability_available: bool,
    route_sender: mpsc::UnboundedSender<(ResourceEvent, Option<oneshot::Sender<()>>)>,
    route_join_handle: JoinHandle<()>,
    wait_tunnel_up_handle: Option<JoinHandle<()>>,
}

impl ConnectedState {
    fn from(
        bootstrap: ConnectedStateBootstrap,
        tx: mpsc::UnboundedSender<(ResourceEvent, Option<oneshot::Sender<()>>)>,
        route_join_handle: JoinHandle<()>,
    ) -> Self {
        ConnectedState {
            metadata: bootstrap.metadata,
            tunnel: bootstrap.tunnel,
            tunnel_events: bootstrap.tunnel_events,
            tunnel_close_tx: None,
            tunnel_packet_tx: None,
            auth_resource_tx: None,
            #[cfg(feature = "traceability")]
            traceability_available: true,
            route_sender: tx,
            route_join_handle,
            wait_tunnel_up_handle: None,
        }
    }

    #[allow(unused_variables)]
    fn get_dns_servers(&self, shared_values: &SharedTunnelStateValues) -> Vec<IpAddr> {
        if let Some(ref servers) = shared_values.dns_servers {
            servers.clone()
        } else {
            vec![]
        }
    }

    async fn set_dns(&self, shared_values: &mut SharedTunnelStateValues) -> Result<(), BoxedError> {
        let dns_ips = self.get_dns_servers(shared_values);

        shared_values
            .dns_monitor
            .set(&self.metadata.interface, &dns_ips)
            .await
            .map_err(BoxedError::new)?;

        Ok(())
    }

    async fn reset_dns(shared_values: &mut SharedTunnelStateValues) {
        if let Err(error) = shared_values
            .dns_monitor
            .reset_before_interface_removal()
            .await
        {
            log::error!("{}", error.display_chain_with_msg("Unable to reset DNS"));
        }
    }

    async fn reset_routes(shared_values: &mut SharedTunnelStateValues) {
        if let Err(error) = shared_values.route_manager.clear_routes() {
            log::error!("{}", error.display_chain_with_msg("Failed to clear routes"));
        }
        #[cfg(target_os = "linux")]
        if let Err(error) = shared_values.route_manager.clear_routing_rules().await {
            log::error!(
                "{}",
                error.display_chain_with_msg("Failed to clear routing rules")
            );
        }
    }

    async fn disconnect(
        mut self,
        shared_values: &mut SharedTunnelStateValues,
        callback: Option<oneshot::Sender<()>>,
    ) -> EventConsequence {
        if let Some(tx) = self.tunnel_close_tx.take() {
            _ = tx.send(());
        }
        Self::reset_dns(shared_values).await;
        Self::reset_routes(shared_values).await;
        _ = shared_values.set_dns_servers(None);
        if let Some(tx) = callback {
            _ = tx.send(());
        }
        self.route_join_handle.abort();
        if let Some(handle) = self.wait_tunnel_up_handle.take() {
            handle.abort();
        }
        EventConsequence::NewState(DisconnectedState::enter(shared_values, ()).await)
    }

    async fn handle_commands(
        mut self,
        command: Option<TunnelCommand>,
        shared_values: &mut SharedTunnelStateValues,
    ) -> EventConsequence {
        use self::EventConsequence::*;

        match command {
            Some(TunnelCommand::Dns(servers)) => match shared_values.set_dns_servers(servers) {
                Ok(_) => match self.set_dns(shared_values).await {
                    Ok(_) => SameState(self.into()),
                    Err(error) => {
                        log::error!("{}", error.display_chain_with_msg("Failed to set DNS"));
                        SameState(self.into())
                    }
                },
                Err(error_cause) => NewState(ErrorState::enter(shared_values, error_cause).await),
            },
            Some(TunnelCommand::ResetDns(tx)) => {
                Self::reset_dns(shared_values).await;
                if let Some(tx) = tx {
                    _ = tx.send(());
                }
                SameState(self.into())
            }
            Some(TunnelCommand::Routes((routes, tx))) => {
                _ = match routes {
                    Some(routes) => self
                        .route_sender
                        .unbounded_send((ResourceEvent::AddResources(routes), tx)),
                    None => self.route_sender.unbounded_send((ResourceEvent::Clear, tx)),
                };
                SameState(self.into())
            }
            Some(TunnelCommand::AskIsOffline(tx)) => {
                let connectivity = shared_values.offline_monitor.connectivity().await;
                _ = tx.send(connectivity.is_offline());
                SameState(self.into())
            }
            Some(TunnelCommand::Connect) => self.disconnect(shared_values, None).await,
            Some(TunnelCommand::Disconnect(tx)) => self.disconnect(shared_values, tx).await,
            Some(TunnelCommand::Resource(event)) => {
                match event {
                    ResourceEvent::AddResources(hash_set) => {
                        _ = self
                            .route_sender
                            .unbounded_send((ResourceEvent::AddResources(hash_set.clone()), None));
                        if let Some(ref resource_tx) = self.auth_resource_tx {
                            _ = resource_tx.unbounded_send(ResourceEvent::AddResources(hash_set));
                        }
                    }
                    ResourceEvent::DeleteResource(hash_set) => {
                        _ = self.route_sender.unbounded_send((
                            ResourceEvent::DeleteResource(hash_set.clone()),
                            None,
                        ));
                        if let Some(ref resource_tx) = self.auth_resource_tx {
                            _ = resource_tx.unbounded_send(ResourceEvent::DeleteResource(hash_set));
                        }
                    }
                    ResourceEvent::Clear => {
                        _ = self
                            .route_sender
                            .unbounded_send((ResourceEvent::Clear, None));
                        if let Some(ref resource_tx) = self.auth_resource_tx {
                            _ = resource_tx.unbounded_send(ResourceEvent::Clear);
                        }
                    }
                    other => {
                        if let Some(ref resource_tx) = self.auth_resource_tx {
                            _ = resource_tx.unbounded_send(other);
                        }
                    }
                }

                SameState(self.into())
            }
            Some(TunnelCommand::Up { ipv4, ipv6 }) => {
                let (packet_tx, packet_rx) = mpsc::unbounded();
                let (resource_tx, resource_rx) = mpsc::unbounded();

                if let Some(handle) = self.wait_tunnel_up_handle.take() {
                    handle.abort();
                }

                // 先监听, 再设置IP
                let (listen_tx, listen_rx) = oneshot::channel::<()>();
                // 等待IP设置生效
                self.wait_tunnel_up_handle = Some(tokio::spawn({
                    let mut ips = HashSet::new();
                    if let Some(ip) = ipv4 {
                        ips.insert(IpAddr::V4(ip));
                    }
                    if let Some(ip) = ipv6 {
                        ips.insert(IpAddr::V6(ip));
                    }
                    let _if_index = self.tunnel.if_index();
                    let tunnel_up_sender = shared_values.tunnel_up_sender.clone();
                    async move {
                        #[cfg(windows)]
                        let status = tunnel::wait_for_ips(_if_index, ips, listen_tx)
                            .await
                            .is_ok();
                        #[cfg(not(windows))]
                        let status = true;
                        #[cfg(not(windows))]
                        let _ = listen_tx.send(());

                        _ = tunnel_up_sender.send(status);
                    }
                }));

                _ = listen_rx.await;
                match self
                    .tunnel
                    .up(
                        ipv4,
                        ipv6,
                        packet_rx,
                        resource_rx,
                        #[cfg(feature = "traceability")]
                        self.traceability_available,
                    )
                    .await
                {
                    Ok(tunnel_close_tx) => {
                        if let Some(tx) = self.tunnel_close_tx.take() {
                            _ = tx.send(());
                        }
                        self.tunnel_close_tx = Some(tunnel_close_tx);
                        self.tunnel_packet_tx = Some(packet_tx);
                        self.auth_resource_tx = Some(resource_tx);
                        SameState(self.into())
                    }
                    Err(err) => {
                        log::error!("Failed to up tunnel: {}", err.display_chain());
                        self.disconnect(shared_values, None).await
                    }
                }
            }
            Some(TunnelCommand::Down) => {
                _ = shared_values.set_dns_servers(None);

                if let Some(ref resource_tx) = self.auth_resource_tx {
                    _ = resource_tx.unbounded_send(ResourceEvent::Clear);
                }
                if let Some(tx) = self.tunnel_close_tx.take() {
                    _ = tx.send(());
                }
                if let Some(handle) = self.wait_tunnel_up_handle.take() {
                    _ = handle.abort();
                }
                SameState(self.into())
            }
            Some(TunnelCommand::Packet(packet)) => {
                if let Some(ref packet_tx) = self.tunnel_packet_tx {
                    if packet_tx.unbounded_send(packet).is_err() {
                        log::error!("Tunnel has stopped");
                        return self.disconnect(shared_values, None).await;
                    }
                }
                SameState(self.into())
            }
            #[cfg(feature = "traceability")]
            Some(TunnelCommand::TraceabilityFunction(available)) => {
                self.traceability_available = available;
                SameState(self.into())
            }
            None => self.disconnect(shared_values, None).await,
        }
    }

    async fn handle_tunnel_events(
        self,
        event: Option<TunnelEvent>,
        shared_values: &mut SharedTunnelStateValues,
    ) -> EventConsequence {
        use self::EventConsequence::*;

        match event {
            Some(TunnelEvent::Down) | None => self.disconnect(shared_values, None).await,
            Some(_) => SameState(self.into()),
        }
    }
}

#[async_trait::async_trait]
impl TunnelState for ConnectedState {
    type Bootstrap = ConnectedStateBootstrap;

    #[cfg_attr(target_os = "android", allow(unused_variables))]
    async fn enter(
        shared_values: &mut SharedTunnelStateValues,
        bootstrap: Self::Bootstrap,
    ) -> (TunnelStateWrapper, TunnelStateTransition) {
        let (tx, mut rx) = mpsc::unbounded::<(ResourceEvent, Option<oneshot::Sender<()>>)>();
        // 等待IP生效, 接收路由设置
        let route_join_handle = tokio::spawn({
            #[cfg(windows)]
            let if_luid = bootstrap.tunnel.if_luid();
            #[cfg(not(windows))]
            let interface_name = bootstrap.metadata.interface.clone();

            let route_manager = shared_values.route_manager.clone();

            let mut tunnel_up_recever = shared_values.tunnel_up_sender.subscribe();
            async move {
                let mut routes = HashSet::<IpNet>::new();
                let mut ip_set = false;
                loop {
                    tokio::select! {
                        Ok(value) = tunnel_up_recever.recv() => {
                            ip_set = value;
                            if ip_set {
                                let routes = resources_to_routes(routes.iter(), #[cfg(windows)] if_luid, #[cfg(not(windows))] &interface_name);
                                _ = route_manager.add_routes(routes).await;
                            }
                            routes.clear();
                        }
                        Some((event, callback_tx)) = rx.next() => match event {
                            ResourceEvent::AddResources(resources) => {
                                if ip_set {
                                    let routes = resources_to_routes(resources.iter(), #[cfg(windows)] if_luid, #[cfg(not(windows))] &interface_name);
                                    _ = route_manager.add_routes(routes).await;
                                } else {
                                    routes.extend(resources.iter());
                                }
                                if let Some(callback_tx) = callback_tx {
                                    _ = callback_tx.send(());
                                }
                            }
                            ResourceEvent::DeleteResource(hash_set) => {
                                if ip_set {
                                    let routes = resources_to_routes(hash_set.iter(), #[cfg(windows)] if_luid, #[cfg(not(windows))] &interface_name);
                                    _ = route_manager.del_routes(routes).await;
                                } else {
                                    routes.retain(|ip_net| !hash_set.contains(ip_net));
                                }
                                if let Some(callback_tx) = callback_tx {
                                    _ = callback_tx.send(());
                                }
                            }
                            ResourceEvent::Clear => {
                                routes.clear();
                                _ = route_manager.clear_routes();
                                if let Some(callback_tx) = callback_tx {
                                    _ = callback_tx.send(());
                                }
                            }
                            _ => ()
                        }
                    }
                }
            }
        });
        let connected_state = ConnectedState::from(bootstrap, tx, route_join_handle);

        (
            TunnelStateWrapper::from(connected_state),
            TunnelStateTransition::Connected,
        )
    }

    async fn handle_event(
        mut self,
        commands: &mut TunnelCommandReceiver,
        shared_values: &mut SharedTunnelStateValues,
    ) -> EventConsequence {
        let result = futures::select! {
            command = commands.next() => EventResult::Command(command),
            event = self.tunnel_events.next() => EventResult::Event(event),
        };

        match result {
            EventResult::Command(command) => self.handle_commands(command, shared_values).await,
            EventResult::Event(event) => self.handle_tunnel_events(event, shared_values).await,
        }
    }
}

fn resources_to_routes(
    resources: Iter<IpNet>,
    #[cfg(windows)] if_luid: u64,
    #[cfg(not(windows))] interface_name: &str,
) -> HashSet<RequiredRoute> {
    resources
        .map(|ip_net| {
            let ip_network = IpNetwork::new(ip_net.addr(), ip_net.prefix_len()).unwrap();
            RequiredRoute::new(
                ip_network,
                #[cfg(not(windows))]
                Node::device(interface_name.to_owned()),
                #[cfg(windows)]
                Node::luid(if_luid),
            )
        })
        .collect::<HashSet<RequiredRoute>>()
}

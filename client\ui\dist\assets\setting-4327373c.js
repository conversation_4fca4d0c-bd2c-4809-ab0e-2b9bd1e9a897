import{bE as C,c2 as E,bJ as B,q as f,cD as S,cE as T,aR as c,aS as v,aE as m,u as g,I as r,bz as b,bx as n,v as e,H as a,b3 as d,F as V,aP as F,t as y,s as M,aI as O,aF as D}from"./index-8ff3976b.js";import q from"./changePwd-e8035d1b.js";import G from"./about-0371f60c.js";import{t as j}from"./terminalPage-4138c85a.js";import"./updater-d62bd7a9.js";const z="/assets/images/setting/user.png",A="/assets/images/setting/change.png",R="/assets/images/setting/terminal.png",H="/assets/images/setting/our.png",J="/assets/images/setting/recover.png";const L={name:"Setting",components:{headerBar:E,changePwd:q,about:G,terminalPage:j},setup(){const t=B();return{tenant:CONFIG.selected_tenant,username:f(()=>t.getters.username),currentUser:f(()=>t.getters.currentUser)}},data(){return{activeMenuName:"personalCenter",loading:!1,loading_text:"加载中...",tabPosition:"left",user:{},imageUrl:"",defaultImageUrl:"/assets/user.png"}},created(){this.currentUserInfo(),this.$route?.query?.type&&(this.activeMenuName=this.$route.query.type)},mounted(){},methods:{resetSettings(){this.$confirm("是否恢复出厂设置？","恢复出厂设置",{confirmButtonText:"确认",cancelButtonText:"取消",roundButton:!0,center:!0,closeOnClickModal:!1,closeOnPressEscape:!1}).then(()=>{S("reset").then(t=>{window.CONFIG=t,this.$router.push({path:"/login",query:{date:new Date().getTime()}}),this.$message({type:"success",message:"恢复出厂设置成功"})}).catch(()=>{this.$message({type:"error",message:"恢复出厂设置失败"})})}).catch(()=>{})},currentUserInfo(){this.currentUser&&this.currentUser.username?(this.user=this.currentUser,this.currentUser.avatarId?this.imageUrl="http://sso.jingantech.com/identity-admin/v8/v1/sdk/files/"+this.currentUser.avatarId+"?TENANT="+CONFIG.selected_tenant.code:this.imageUrl=this.defaultImageUrl):T().then(t=>{this.user=t.data,t.data.avatarId?this.imageUrl="http://sso.jingantech.com/identity-admin/v8/v1/sdk/files/"+t.data.avatarId+"?TENANT="+CONFIG.selected_tenant.code:this.imageUrl=this.defaultImageUrl}).catch(t=>{})},handleImageError(t){t.target.src=this.defaultImageUrl}}},l=t=>(O("data-v-e49daa97"),t=t(),D(),t),K={class:"app-content"},Q={class:"common-layout","element-loading-text":"loading_text","element-loading-background":"rgba(0, 0, 0, 0.5)"},W=l(()=>e("h3",{class:"f_w_no",style:{margin:"42px 0 20px 30px","font-size":"18px"}}," 设置 ",-1)),X=l(()=>e("div",{class:"custom-tabs-label"},[e("img",{width:"16",height:"16",src:z,alt:""}),a(" 个人信息 ")],-1)),Y={style:{width:"100%",height:"100vh"}},Z={style:{"margin-top":"2%"}},$={class:"textstyle"},ee={style:{display:"flex"}},te=l(()=>e("span",{style:{"font-weight":"normal"}},"组织机构 :   ",-1)),se={style:{width:"78%"}},ae={key:0},ne=l(()=>e("div",{class:"custom-tabs-label"},[e("img",{src:A,width:"16",height:"16",alt:""}),a(" 修改密码 ")],-1)),re=l(()=>e("div",{class:"custom-tabs-label"},[e("img",{src:R,width:"16",height:"16",alt:""}),a(" 终端列表 ")],-1)),le={style:{"margin-left":"10px"}},ie=l(()=>e("h3",{style:{"margin-top":"0"}},"终端列表",-1)),oe=l(()=>e("div",{class:"custom-tabs-label"},[e("img",{src:H,width:"16",height:"16",alt:""}),a(" 关于我们 ")],-1)),ce=l(()=>e("img",{width:"16",height:"16",src:J,alt:""},null,-1));function me(t,o,de,h,s,_){const I=c("headerBar"),u=c("el-tab-pane"),x=c("changePwd"),U=c("terminalPage"),w=c("about"),N=c("el-tabs"),k=v("real-img"),P=v("loading");return m(),g("div",K,[r(I),b((m(),g("div",Q,[W,r(N,{"tab-position":s.tabPosition,style:{height:"100%"},modelValue:s.activeMenuName,"onUpdate:modelValue":o[2]||(o[2]=i=>s.activeMenuName=i)},{default:n(()=>[r(u,{name:"personalCenter"},{label:n(()=>[X]),default:n(()=>[e("div",Y,[e("div",Z,[b(e("img",{style:{"margin-left":"60px"},src:"",class:"userhead",alt:"",width:"100",height:"100",onError:o[0]||(o[0]=(...i)=>_.handleImageError&&_.handleImageError(...i))},null,544),[[k,{value:s.imageUrl,defaultValue:s.defaultImageUrl}]]),e("div",$,[e("p",null,[a(" 账号 : "),e("span",null,"  "+d(h.username||"-"),1)]),e("p",null,[a(" 姓名 : "),e("span",null,"  "+d(s.user.displayName||"-"),1)]),e("p",null,[a(" 电话 : "),e("span",null,"  "+d(s.user.mobile||"-"),1)]),e("p",null,[a(" 邮箱 : "),e("span",null,"  "+d(s.user.email||"-"),1)]),e("p",ee,[te,e("span",se,[(m(!0),g(V,null,F(s.user.groups,(i,p)=>(m(),g("span",{key:p},[a(d(i.v||"-")+" ",1),p!==s.user.groups.length-1?(m(),g("span",ae,", ")):y("",!0)]))),128))])]),e("p",null,[a(" 所在单位 :   "),e("span",null,d(h.tenant&&h.tenant.name||"-")+" ",1)])])])])]),_:1}),r(u,{name:"changePwd",style:{"text-align":"left"}},{label:n(()=>[ne]),default:n(()=>[r(x,{username:h.username},null,8,["username"])]),_:1}),r(u,{name:"terminalPage",style:{"text-align":"left"}},{label:n(()=>[re]),default:n(()=>[e("div",le,[ie,s.activeMenuName==="terminalPage"?(m(),M(U,{key:0,ref:"terminalRef",userId:s.user.id},null,8,["userId"])):y("",!0)])]),_:1}),r(u,{name:"about",style:{"text-align":"left"}},{label:n(()=>[oe]),default:n(()=>[r(w)]),_:1}),r(u,{disabled:"",name:"reset",style:{"text-align":"left"}},{label:n(()=>[e("div",{class:"custom-tabs-label",onClick:o[1]||(o[1]=(...i)=>_.resetSettings&&_.resetSettings(...i))},[ce,a(" 恢复出厂设置 ")])]),_:1})]),_:1},8,["tab-position","modelValue"])])),[[P,s.loading,void 0,{fullscreen:!0,lock:!0}]])])}const fe=C(L,[["render",me],["__scopeId","data-v-e49daa97"]]);export{fe as default};

use clap::Parser;
use serde_json::Value;
use std::{collections::HashMap, error::Error, path::PathBuf};

use cli::Mode;
use comm::CommunicateInterfaceServer;
use err_ext::ErrorExt;
use oneidcore::*;

use crate::{cli::AppArgs, comm::CommunicateInterfaceEventBroadcaster};

mod build_info;
mod cli;
mod comm;
mod logging;
mod rpc_uniqueness_check;

#[tokio::main]
async fn main() -> Result<(), Box<dyn Error>> {
    // 解析参数
    let args = AppArgs::parse();
    assert_unique().await?;

    logging::init_logger(&args.filter, args.mode).unwrap_or_else(|error| {
        eprintln!("{error}");
        std::process::exit(1)
    });

    if !args.resources_dir.exists() || !args.resources_dir.is_dir() {
        log::error!(
            "No such directory. {}",
            args.resources_dir.to_string_lossy()
        );
        std::process::exit(1);
    }

    let settings = setting::Setting::load(&args.resources_dir.join("core.yaml"))
        .map_err(|error| error.display_chain_with_msg("Failed to load settings"))?;

    let mut identities = HashMap::new();
    for (domain, setting) in settings {
        let identity: setting::DeviceIdentity =
            setting
                .try_into()
                .map_err(|error: oneidcore::setting::Error| {
                    error.display_chain_with_msg("Core config error")
                })?;
        identities.insert(domain, identity);
    }

    let exit_code = match run(args.mode, identities).await {
        Ok(_) => 0,
        Err(error) => {
            log::error!("{}", error);
            1
        }
    };

    log::debug!("Process exiting with code {}", exit_code);
    std::process::exit(exit_code);
}

async fn run(
    _mode: Mode,
    identities: HashMap<String, setting::DeviceIdentity>,
) -> Result<(), String> {
    #[cfg(not(windows))]
    cleanup_old_rpc_socket(paths::get_rpc_socket_path()).await;

    let Ok(device_id) = uniqueid::device_id() else {
        return Err("An error occurred computing the device ID".to_owned());
    };

    let system_info = environment::system::info(&device_id);

    let (core, communicate_interface) = create_core(device_id, system_info, identities).await?;

    let shutdown_handle = core.shutdown_handle();
    shutdown::set_shutdown_signal_handler(move || shutdown_handle.shutdown())
        .map_err(|e| e.display_chain())?;

    core.run().await.map_err(|e| e.display_chain())?;
    communicate_interface.stop().await;

    #[cfg(not(windows))]
    cleanup_old_rpc_socket(paths::get_rpc_socket_path()).await;

    log::info!("OneID core is quitting");
    Ok(())
}

/// Check that there's not another core currently running.
async fn assert_unique() -> Result<(), &'static str> {
    if rpc_uniqueness_check::is_another_instance_running().await {
        return Err("Another instance of the core is already running");
    }
    Ok(())
}

async fn create_core(
    device_id: String,
    system_info: Value,
    identities: HashMap<String, setting::DeviceIdentity>,
) -> Result<
    (
        Core<comm::CommunicateInterfaceEventBroadcaster>,
        CommunicateInterfaceServer,
    ),
    String,
> {
    let rpc_socket_path = paths::get_rpc_socket_path();
    let command_channel = CoreCommandChannel::new();
    let core_command_sender = command_channel.sender();

    let (core_event_sender, core_event_receiver) = command_channel.destructure();

    // 启动HTTP代理服务
    let proxy_handle = proxy_request::spawn(device_id.clone(), core_event_sender.clone());

    let (communicate_interface, event_listener) = spawn_communicate_server(
        device_id.clone(),
        system_info,
        rpc_socket_path,
        core_command_sender,
        proxy_handle,
    )
    .await?;

    Core::start(
        device_id,
        identities,
        event_listener,
        core_event_sender,
        core_event_receiver,
    )
    .await
    .map(|core| (core, communicate_interface))
    .map_err(|e| e.display_chain_with_msg("Unable to initialize core"))
}
/// 生成通信服务器
async fn spawn_communicate_server(
    device_id: String,
    system_info: Value,
    rpc_socket_path: PathBuf,
    command_sender: CoreCommandSender,
    proxy_handle: proxy_request::ProxyHandle,
) -> Result<
    (
        CommunicateInterfaceServer,
        CommunicateInterfaceEventBroadcaster,
    ),
    String,
> {
    CommunicateInterfaceServer::start(
        device_id,
        system_info,
        command_sender,
        rpc_socket_path,
        proxy_handle,
    )
    .await
    .map_err(|error| error.display_chain_with_msg("Unable to start communicate interface server"))
}

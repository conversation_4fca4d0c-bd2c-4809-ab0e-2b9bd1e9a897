use std::fs::File;

use time::{
    macros::{format_description, offset},
    UtcOffset,
};
use tracing::Level;
use tracing_appender::rolling;
use tracing_rolling::{Checker, Daily, Token};
use tracing_subscriber::{
    filter::FilterFn,
    fmt::{self, time::OffsetTime},
    prelude::__tracing_subscriber_SubscriberExt,
    reload::{self, Handle},
    util::SubscriberInitExt,
    EnvFilter, Layer, Registry,
};

const COMMIT_DATE: &str = include_str!(concat!(env!("OUT_DIR"), "/git-commit-date.txt"));

pub fn init(dir: &str, filter: &str) -> (Token<File>, Handle<EnvFilter, Registry>) {
    let (writer, token) = Daily::new::<&str>(format!("{}/server.log", dir), None, offset!(+8))
        .build()
        .unwrap();

    let env_filter = EnvFilter::new(filter);
    let (filter, reload_handle) = reload::Layer::new(env_filter);

    let timer = OffsetTime::new(
        UtcOffset::from_hms(8, 0, 0).unwrap(),
        format_description!("[year]-[month]-[day] [hour]:[minute]:[second].[subsecond digits:3]"),
    );

    let log_layer = fmt::layer()
        .compact()
        .with_timer(timer.clone())
        .with_line_number(if cfg!(debug_assertions) { true } else { false })
        .with_file(if cfg!(debug_assertions) { true } else { false })
        .with_thread_ids(if cfg!(debug_assertions) { true } else { false })
        .with_thread_names(if cfg!(debug_assertions) { true } else { false })
        .with_target(if cfg!(debug_assertions) { true } else { false })
        .with_ansi(if cfg!(debug_assertions) { true } else { false })
        .with_writer(writer)
        .with_filter(filter);

    #[cfg(feature = "trace")]
    let console_layer = console_subscriber::spawn();

    let audit_appender = rolling::daily(dir, "audit");

    let audit_layer = fmt::layer()
        .json()
        .with_timer(timer)
        .with_file(false)
        .with_level(false)
        .with_target(false)
        .with_writer(audit_appender)
        .with_filter(FilterFn::new(|metadata| {
            metadata.level() == &Level::INFO && {
                let target = metadata.target();
                target == "audit"
            }
        }));

    #[cfg(feature = "trace")]
    tracing_subscriber::registry()
        .with(log_layer)
        .with(audit_layer)
        .with(console_layer)
        .init();

    #[cfg(not(feature = "trace"))]
    tracing_subscriber::registry()
        .with(log_layer)
        .with(audit_layer)
        .init();

    log_panics::init();

    let banner = r#"
   _______  ___    ____
  / __/ _ \/ _ \  / __/__ _____  _____ ____
 _\ \/ // / ___/ _\ \/ -_) __/ |/ / -_) __/
/___/____/_/    /___/\__/_/  |___/\__/_/

"#;

    #[cfg(not(feature = "tlcp"))]
    let tlcp = "";
    #[cfg(feature = "tlcp")]
    let tlcp = " With TLCP";
    // banner
    tracing::info!(
        "{}Version: {} {}{}\n",
        banner,
        version::FILE_VERSION,
        COMMIT_DATE,
        tlcp
    );

    (token, reload_handle)
}

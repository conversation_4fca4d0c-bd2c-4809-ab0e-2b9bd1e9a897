use actix_cors::Cors;
use actix_web::{App, HttpResponse, HttpServer};
use rustls::{pki_types::PrivateKeyDer, ServerConfig};
use rustls_pemfile::{certs, pkcs8_private_keys};
use std::io::BufReader;
#[cfg(windows)]
use std::time::Duration;
use tokio::sync::broadcast;

use crate::ErrorExt;

pub async fn run(shutdown_tx: broadcast::Sender<()>) -> Result<(), String> {
    log::trace!("Starting https server...");

    #[cfg(windows)]
    {
        let mut shutdown_rx = shutdown_tx.subscribe();
        let shutdown_handle = shutdown_tx.clone();
        let check_future = async move {
            let mut tick = tokio::time::interval(Duration::from_secs(10));

            loop {
                tokio::select! {
                    _ = tick.tick() => {
                        // 端口不可用
                        match std::net::TcpListener::bind("127.0.0.1:52028") {
                            Ok(listener) => {
                                drop(listener);

                                tokio::time::sleep(Duration::from_secs(10)).await;
                                return run_facial_server(shutdown_handle.subscribe()).await;
                            }
                            Err(_) => {
                                log::error!("Port unavailable");
                            }
                        }
                    }
                    _ = shutdown_rx.recv() => {
                        break;
                    }
                }
            }

            Result::<(), String>::Ok(())
        };

        match tokio::join!(check_future, run_main_server(shutdown_tx.subscribe()),) {
            (Ok(_), Ok(_)) => Ok(()),
            (Ok(_), Err(error)) => Err(error),
            (Err(error), Ok(_)) => Err(error),
            (Err(error), Err(_)) => Err(error),
        }
    }

    #[cfg(not(windows))]
    run_main_server(shutdown_tx.subscribe()).await
}

async fn run_main_server(mut shutdown_rx: broadcast::Receiver<()>) -> Result<(), String> {
    let cert = include_str!("../attachments/server.crt");
    let key = include_str!("../attachments/server.key");

    let cert_file = &mut BufReader::new(cert.as_bytes());
    let key_file = &mut BufReader::new(key.as_bytes());
    let cert_chain = certs(cert_file).collect::<Result<Vec<_>, _>>().unwrap();
    let mut keys = pkcs8_private_keys(key_file)
        .map(|key| key.map(PrivateKeyDer::Pkcs8))
        .collect::<Result<Vec<_>, _>>()
        .unwrap();

    let config = ServerConfig::builder()
        .with_no_client_auth()
        .with_single_cert(cert_chain, keys.remove(0))
        .unwrap();

    let srv = HttpServer::new(|| {
        #[allow(unused_mut)]
        let mut app = App::new().wrap(Cors::permissive());
        #[cfg(all(windows, feature = "pki"))]
        {
            app = app.service(crate::pki::list).service(crate::pki::verify);
        }

        app.service(device_id).service(ticket)
    })
    .bind_rustls_0_23("127.0.0.1:50021", config)
    .map_err(|err| err.display_chain())?
    .run();

    let server_handle = srv.handle();
    tokio::spawn(async move {
        _ = shutdown_rx.recv().await;
        server_handle.stop(false).await;
    });

    srv.await.map_err(|e| e.display_chain())?;
    Ok(())
}

#[cfg(windows)]
async fn run_facial_server(mut shutdown_rx: broadcast::Receiver<()>) -> Result<(), String> {
    log::trace!("Starting facial recognition server...");

    let cert = include_str!("../attachments/server.crt");
    let key = include_str!("../attachments/server.key");
    let cert_file = &mut BufReader::new(cert.as_bytes());
    let key_file = &mut BufReader::new(key.as_bytes());
    let cert_chain = certs(cert_file).collect::<Result<Vec<_>, _>>().unwrap();
    let mut keys = pkcs8_private_keys(key_file)
        .map(|key| key.map(PrivateKeyDer::Pkcs8))
        .collect::<Result<Vec<_>, _>>()
        .unwrap();

    let config = ServerConfig::builder()
        .with_no_client_auth()
        .with_single_cert(cert_chain, keys.remove(0))
        .unwrap();

    let srv = HttpServer::new(|| {
        App::new()
            .wrap(Cors::permissive())
            .service(crate::bio::is_support_facial_recognition)
            .service(crate::bio::facial_recognition)
            .service(crate::bio::cancel_facial_recognition)
    })
    .bind_rustls_0_23("127.0.0.1:52028", config)
    .map_err(|err| err.display_chain())?
    .run();

    let server_handle = srv.handle();
    tokio::spawn(async move {
        _ = shutdown_rx.recv().await;
        server_handle.stop(true).await;
    });

    Ok(())
}

#[actix_web::get("/device_id")]
async fn device_id() -> HttpResponse {
    if let Ok(device_id) = uniqueid::device_id() {
        HttpResponse::Ok().json(serde_json::json!({
            "code": "SUCCESS",
            "data": device_id,
        }))
    } else {
        HttpResponse::InternalServerError().finish()
    }
}

#[actix_web::get("/login-ticket")]
async fn ticket() -> HttpResponse {
    #[cfg(windows)]
    {
        if let Ok(_device_id) = uniqueid::device_id() {
            log::info!("new_from_registry first");
            let ticket_info_ret = crate::sm::de_en_sm2::TicketInfo::new_from_registry().await;
            match ticket_info_ret {
                Ok(ticket_info) => HttpResponse::Ok().json(serde_json::json!({
                    "code": "SUCCESS",
                    "data": ticket_info,
                })),
                Err(err) => {
                    log::error!("{:?}", &err);
                    sdp_ticket().await
                }
            }
        } else {
            log::error!("device id is null");
            HttpResponse::BadRequest().json(serde_json::json!({
                "code": "ERROR",
                "data": "Can not get device id",
            }))
        }
    }
    #[cfg(any(target_os = "linux", target_os = "macos"))]
    {
        sdp_ticket().await
    }
}

async fn sdp_ticket() -> HttpResponse {
    let client = crate::http::client().await;
    #[cfg(windows)]
    let port = get_gui_port();
    #[cfg(not(windows))]
    let port = 50031u16;
    let url = format!("http://127.0.0.1:{port}/web_login_ticket"); //{userId:"",tenant:""}
    log::info!("access url {}", &url);
    match client.get(url).send().await {
        Ok(response) => {
            if response.status().is_server_error() {
                log::error!("response: {:?}", &response);
                HttpResponse::BadRequest().json(serde_json::json!({
                    "code": "ERROR",
                    "data": "Server Error",
                }))
            } else {
                let bytes = response.bytes().await.unwrap_or_default();
                match serde_json::from_slice::<serde_json::Value>(&bytes) {
                    Ok(value) => {
                        let pubkey_str = value["publicKeyStr"].as_str().clone().unwrap_or("");
                        let pubkey = crate::sm::cryptor_factory::PubkeyInfo {
                            data_transmit_type: value["dataTransmitType"].as_i64().unwrap_or(0)
                                as i32,
                            manufacturer: value["manufacturer"].as_i64().unwrap_or(0) as i32,
                            public_key_str: Some(pubkey_str.to_string()),
                        };

                        let ticket_ret = crate::sm::de_en_sm2::TicketInfo::new_from_userinfo(
                            value["tenant"].as_str().unwrap_or_default(),
                            value["username"].as_str().unwrap_or_default(),
                            &pubkey,
                        )
                        .await;

                        match ticket_ret {
                            Ok(ticket_info) => HttpResponse::Ok().json(serde_json::json!({
                                "code": "SUCCESS",
                                "data": ticket_info,
                            })),
                            Err(err) => HttpResponse::BadRequest().json(serde_json::json!({
                                "code": "ERROR",
                                "data": err,
                            })),
                        }
                    }
                    Err(_) => HttpResponse::BadRequest().json(serde_json::json!({
                            "code": "ERROR",
                    "data": "Serialize json error",
                        })),
                }
            }
        }
        Err(_) => HttpResponse::BadRequest().json(serde_json::json!({
                    "code": "ERROR",
        "data": "err in get login ticket from sdp",
                })),
    }
}

#[cfg(windows)]
pub fn get_gui_port() -> u16 {
    // 将端口存储到注册表中
    let hklm = winreg::RegKey::predef(winreg::enums::HKEY_LOCAL_MACHINE);
    let software = hklm.open_subkey("SOFTWARE").unwrap();

    if let Ok(product) = software.open_subkey(paths::PRODUCT_NAME) {
        let port = product.get_value::<u32, &str>("port").unwrap_or(50031u32);
        return port as u16;
    }

    50031
}

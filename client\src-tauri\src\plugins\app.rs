use crate::state::AppState;

#[cfg(not(feature = "cluster"))]
use super::ErrorResponse;
use super::Result;
use communicate_interface::ControlServiceClient;
use config::AppConfig;
#[cfg(not(feature = "cluster"))]
use tauri::Manager;
use tauri::{async_runtime::Mutex, AppHandle, State, Wry};
use tokio::sync::RwLock;

/// 恢复出厂设置
#[tauri::command(async)]
pub(super) async fn reset(
    app_handle: AppHandle<Wry>,
    rpc: State<'_, Mutex<Option<ControlServiceClient>>>,
    app_state: State<'_, RwLock<AppState>>,
) -> Result<AppConfig> {
    // 断开连接
    #[cfg(feature = "sdp")]
    let _ = crate::plugins::sdp::auth::logout(rpc, app_state.clone()).await;

    // 重置程序状态
    let mut app_state = app_state.write().await;
    app_state.reset();
    drop(app_state);

    // 重置数据
    #[cfg_attr(not(feature = "cluster"), allow(unused_mut))]
    let mut default_config = AppConfig::default();

    // 开启集群模式
    #[cfg(feature = "cluster")]
    if !crate::cluster::load_nodes_and_update_cluster_config_url(
        &app_handle,
        config::CLUSTER_CONFIG_URL.map(|url| url.parse().unwrap()),
        config::CLUSTER_EXTERNAL_CONFIG_URL.map(|url| url.parse().unwrap()),
        &mut default_config,
    )
    .await
    {
        log::warn!(target: "app", "Failed to obtain the node list");
    }

    #[cfg(not(feature = "cluster"))]
    if let Err(err) = default_config.save_file() {
        log::error!(target: "app", "{err}");
        return Err(ErrorResponse::new(622, None));
    } else {
        let state = app_handle.state::<Mutex<AppConfig>>();
        let mut state = state.lock().await;
        *state = default_config.clone();
    }

    Ok(default_config)
}

/// 退出程序
#[tauri::command]
pub(super) async fn graceful_exit(app_handle: AppHandle<Wry>) -> Result<()> {
    log::debug!(target: "app", "Graceful exit");
    crate::async_exit(&app_handle).await;
    Ok(())
}

/// 是否已登录过
#[tauri::command(async)]
pub async fn has_logged_in(app_state: State<'_, RwLock<AppState>>) -> Result<bool> {
    let app_state = app_state.read().await;
    Ok(app_state.already_executed_login)
}

#[tauri::command(async)]
pub async fn set_logged_in(app_state: State<'_, RwLock<AppState>>) -> Result<()> {
    let mut app_state = app_state.write().await;
    app_state.already_executed_login = true;
    Ok(())
}

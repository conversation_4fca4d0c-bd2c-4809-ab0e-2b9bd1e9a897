import{bE as C,c2 as I,bJ as k,aa as g,q as D,aR as c,aS as V,aE as r,u as i,bz as E,v as t,b3 as l,H as d,I as a,bx as _,aI as U,aF as N,ce as S}from"./index-8ff3976b.js";import{u as A}from"./updater-d62bd7a9.js";const T={name:"About",components:{updater:A,headerBar:I},setup(){const e=k(),o=g("checkUpdate"),p=g("openUserAgreement");return{checkUpdate:o,openUserAgreement:p,productVersion:window.PRODUCT_VERSION,deviceId:window.DEVICE_ID,hasNewVersion:D(()=>e.getters.hasNewVersion)}},data(){return{loading_text:"正在退出...",loading:!1}},created(){},methods:{copyDeviceId(){if(navigator.clipboard&&navigator.clipboard.writeText)navigator.clipboard.writeText(window.DEVICE_ID).then(()=>{this.alert.success("已复制到剪贴板")}).catch(e=>{this.alert.error("复制失败："+e)});else{const e=document.createElement("textarea");e.value=window.DEVICE_ID,e.style.position="absolute",e.style.left="-9999px",document.body.appendChild(e),e.select();try{document.execCommand("copy"),this.alert.success("已复制到剪贴板")}catch(o){this.alert.error("复制失败："+o)}finally{document.body.removeChild(e)}}},async check(){this.loading_text="检查中",this.loading=!0;let e=await this.checkUpdate(!0,5e3);e&&e.shouldUpdate&&this.$nextTick(()=>{this.$refs.updater.init(e)}),this.loading=!1,this.loading_text="正在退出..."}}},s=e=>(U("data-v-952c4f2b"),e=e(),N(),e),B={class:"app-content"},O={class:"about-content","element-loading-text":"loading_text","element-loading-background":"rgba(0, 0, 0, 0.5)"},R=s(()=>t("div",{class:"about-header"},[t("img",{src:S,alt:"",class:"icon"})],-1)),F={class:"about-footer"},j=s(()=>t("strong",{class:"appstr"},"软件更新",-1)),q=s(()=>t("br",null,null,-1)),z={key:0,style:{"font-weight":"normal",color:"#53565d"}},H={key:1,style:{"font-weight":"normal",color:"#53565d"}},J=s(()=>t("strong",{class:"appstr"},"关于我们",-1)),P=s(()=>t("br",null,null,-1)),G={style:{"font-weight":"normal",display:"flex","align-items":"center","padding-left":"2px"}},K=s(()=>t("span",{style:{"text-indent":"5px",color:"#9c9ea3"}},"已通过 ISO 27001:2013 信息安全认证",-1)),L=s(()=>t("strong",{class:"appstr"},"设备ID",-1)),M=s(()=>t("br",null,null,-1));function Q(e,o,p,n,f,u){const v=c("el-button"),b=c("CircleCheck"),h=c("el-icon"),y=c("DocumentCopy"),x=c("updater"),w=V("loading");return r(),i("div",B,[E((r(),i("div",O,[R,t("div",F,[t("div",null,[j,q,n.hasNewVersion?(r(),i("strong",z,"发现新版本：v"+l(n.productVersion),1)):(r(),i("strong",H,"当前已是最新版本：v"+l(n.productVersion),1)),d("  "),a(v,{plain:"",onClick:u.check,style:{border:"1px solid #f87f1a",color:"#f87f1a"}},{default:_(()=>[d(l(n.hasNewVersion?"立即更新":"检查更新"),1)]),_:1},8,["onClick"])]),t("div",null,[J,P,t("strong",G,[a(h,{color:"#77C16F"},{default:_(()=>[a(b)]),_:1}),K]),t("span",{style:{color:"#f87f1a",cursor:"pointer"},onClick:o[0]||(o[0]=(...m)=>n.openUserAgreement&&n.openUserAgreement(...m))},"《用户协议》")]),t("div",null,[L,M,t("span",null,[d(l(n.deviceId)+"  ",1),a(h,{style:{color:"#f9780c",cursor:"pointer"},onClick:o[1]||(o[1]=m=>u.copyDeviceId())},{default:_(()=>[a(y)]),_:1})])])])])),[[w,f.loading,void 0,{fullscreen:!0,lock:!0}]]),a(x,{ref:"updater"},null,512)])}const Y=C(T,[["render",Q],["__scopeId","data-v-952c4f2b"]]);export{Y as default};
